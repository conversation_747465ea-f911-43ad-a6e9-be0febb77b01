import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { WalletService } from '../../../services/wallet.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const walletService = new WalletService();
    const balance = await walletService.getBalance(user.userId);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: balance })
    };
  } catch (err: any) {
    logger.error('Get balance error', { error: err.message });
    return errorResponse(401, 'UNAUTHORIZED', err.message);
  }
};
