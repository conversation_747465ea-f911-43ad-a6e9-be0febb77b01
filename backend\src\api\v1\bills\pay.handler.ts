import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { BillService } from '../../../services/bill.service';
import { validateBillPayment } from '../../../common/validation/bill.validation';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const body = JSON.parse(event.body || '{}');
    const { error } = validateBillPayment(body);
    if (error) {
      logger.warn('Validation failed', { details: error.details });
      return errorResponse(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    
    const billService = new BillService();
    const paymentResult = await billService.payBill(user.userId, body);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: paymentResult })
    };
  } catch (err: any) {
    logger.error('Bill payment error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during bill payment');
  }
};
