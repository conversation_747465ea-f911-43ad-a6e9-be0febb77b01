import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { UserService } from '../../../services/user.service';
import { validateUpdateProfile } from '../../../common/validation/user.validation';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const body = JSON.parse(event.body || '{}');
    const { error } = validateUpdateProfile(body);
    if (error) {
      logger.warn('Validation failed', { details: error.details });
      return errorResponse(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    
    const userService = new UserService();
    const updatedProfile = await userService.updateProfile(user.userId, body);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: updatedProfile })
    };
  } catch (err: any) {
    logger.error('Update profile error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during profile update');
  }
};
