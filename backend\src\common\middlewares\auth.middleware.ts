import { APIGatewayProxyEvent } from 'aws-lambda';
import { JwtService } from '../security/jwt.service';
import { TokenPayload, AuthenticatedEvent } from '../../interfaces/common';

export class AuthMiddleware {
  private jwtService = new JwtService();

  authenticate(event: APIGatewayProxyEvent): TokenPayload {
    const authHeader = event.headers['Authorization'] || event.headers['authorization'];
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Missing or invalid authorization header');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    try {
      return this.jwtService.verifyAccessToken(token);
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  // Helper function to get user from event
  static getUser(event: AuthenticatedEvent): TokenPayload {
    if (!event.user) {
      throw new Error('User not authenticated');
    }
    return event.user;
  }
}
