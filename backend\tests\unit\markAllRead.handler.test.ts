import { APIGatewayProxyEvent } from 'aws-lambda';
import { main } from '../../src/api/v1/notifications/markAllRead.handler';
import { NotificationService } from '../../src/services/notification.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';

// Mock dependencies
jest.mock('../../src/services/notification.service');
jest.mock('../../src/common/middlewares/auth.middleware');

describe('Mark All Read Notifications Handler', () => {
  let mockEvent: APIGatewayProxyEvent;
  let mockNotificationServiceInstance: any;
  let mockAuthMiddlewareInstance: any;

  beforeEach(() => {
    mockEvent = {
      headers: { Authorization: 'Bearer valid-token' }
    } as any;

    mockNotificationServiceInstance = {
      markAllAsRead: jest.fn()
    };

    mockAuthMiddlewareInstance = {
      authenticate: jest.fn()
    };

    (NotificationService as jest.MockedClass<typeof NotificationService>).mockImplementation(() => mockNotificationServiceInstance);
    (AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>).mockImplementation(() => mockAuthMiddlewareInstance);

    jest.clearAllMocks();
  });

  describe('successful mark all read', () => {
    it('should mark all notifications as read successfully', async () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const mockResult = {
        modifiedCount: 5,
        message: 'All notifications marked as read'
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.markAllAsRead.mockResolvedValue(mockResult);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockResult
      });
      expect(mockNotificationServiceInstance.markAllAsRead).toHaveBeenCalledWith('user123');
    });

    it('should handle case with no unread notifications', async () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const mockResult = {
        modifiedCount: 0,
        message: 'No unread notifications found'
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.markAllAsRead.mockResolvedValue(mockResult);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockResult
      });
    });
  });

  describe('authentication failures', () => {
    it('should return 500 when authentication fails', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred while marking all notifications as read');
    });

    it('should return 500 for invalid token', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Invalid or expired token');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred while marking all notifications as read');
    });
  });

  describe('service errors', () => {
    it('should handle notification service errors', async () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.markAllAsRead.mockRejectedValue(new Error('Database connection failed'));

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred while marking all notifications as read');
    });
  });

  describe('edge cases', () => {
    it('should handle missing Authorization header', async () => {
      mockEvent.headers = {};

      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred while marking all notifications as read');
    });
  });
});
