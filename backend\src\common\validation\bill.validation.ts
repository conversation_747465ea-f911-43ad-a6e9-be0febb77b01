import Joi from 'joi';

export const billValidationSchema = Joi.object({
  providerId: Joi.string().required(),
  customerNumber: Joi.string().required(),
  amount: Joi.number().min(0).optional()
});

export const billPaymentSchema = Joi.object({
  providerId: Joi.string().required(),
  customerNumber: Joi.string().required(),
  amount: Joi.number().min(50).required(),
  pin: Joi.string().length(4).required()
});

export function validateBillValidation(data: any) {
  return billValidationSchema.validate(data, { abortEarly: true });
}

export function validateBillPayment(data: any) {
  return billPaymentSchema.validate(data, { abortEarly: true });
}
