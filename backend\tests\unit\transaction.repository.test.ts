import { Types } from 'mongoose';
import { TransactionRepository, Transaction } from '../../src/repositories/transaction.repository';

describe('TransactionRepository', () => {
  let transactionRepository: TransactionRepository;

  beforeEach(() => {
    transactionRepository = new TransactionRepository();
  });

  describe('create', () => {
    it('should create a new transaction successfully', async () => {
      const createTransactionDto = {
        userId: new Types.ObjectId().toString(),
        type: 'CREDIT' as const,
        amount: 1000,
        fee: 50,
        description: 'Test credit transaction',
        status: 'PENDING' as const,
        category: 'TRANSFER',
        reference: 'TXN' + Date.now()
      };

      const transaction = await transactionRepository.create(createTransactionDto);

      expect(transaction).toBeDefined();
      expect(transaction.userId.toString()).toBe(createTransactionDto.userId);
      expect(transaction.type).toBe('CREDIT');
      expect(transaction.amount).toBe(1000);
      expect(transaction.fee).toBe(50);
      expect(transaction.description).toBe('Test credit transaction');
      expect(transaction.status).toBe('PENDING');
      expect(transaction.category).toBe('TRANSFER');
      expect(transaction.reference).toBe(createTransactionDto.reference);
      expect(transaction.createdAt).toBeDefined();
      expect(transaction.updatedAt).toBeDefined();
    });

    it('should create debit transaction', async () => {
      const createTransactionDto = {
        userId: new Types.ObjectId().toString(),
        type: 'DEBIT' as const,
        amount: 500,
        fee: 25,
        description: 'Test debit transaction',
        status: 'COMPLETED' as const,
        category: 'WITHDRAWAL',
        reference: 'TXN' + Date.now()
      };

      const transaction = await transactionRepository.create(createTransactionDto);

      expect(transaction.type).toBe('DEBIT');
      expect(transaction.amount).toBe(500);
      expect(transaction.status).toBe('COMPLETED');
      expect(transaction.category).toBe('WITHDRAWAL');
    });
  });

  describe('findById', () => {
    it('should find transaction by id', async () => {
      const createTransactionDto = {
        userId: new Types.ObjectId().toString(),
        type: 'CREDIT' as const,
        amount: 750,
        fee: 30,
        description: 'Test transaction',
        status: 'COMPLETED' as const,
        category: 'DEPOSIT',
        reference: 'TXN' + Date.now()
      };

      const createdTransaction = await transactionRepository.create(createTransactionDto);
      const transaction = await transactionRepository.findById(createdTransaction.id);      expect(transaction).toBeDefined();
      expect(transaction?.id).toStrictEqual(createdTransaction.id);
      expect(transaction?.amount).toBe(750);
    });

    it('should return null for non-existent transaction id', async () => {
      const nonExistentId = new Types.ObjectId().toString();
      const transaction = await transactionRepository.findById(nonExistentId);

      expect(transaction).toBeNull();
    });
  });

  describe('findByReference', () => {
    it('should find transaction by reference', async () => {
      const reference = 'TXN' + Date.now();
      const createTransactionDto = {
        userId: new Types.ObjectId().toString(),
        type: 'CREDIT' as const,
        amount: 1000,
        fee: 50,
        description: 'Test transaction',
        status: 'COMPLETED' as const,
        category: 'TRANSFER',
        reference
      };

      await transactionRepository.create(createTransactionDto);
      const transaction = await transactionRepository.findByReference(reference);

      expect(transaction).toBeDefined();
      expect(transaction?.reference).toBe(reference);
    });

    it('should return null for non-existent reference', async () => {
      const nonExistentReference = 'NONEXISTENT' + Date.now();
      const transaction = await transactionRepository.findByReference(nonExistentReference);

      expect(transaction).toBeNull();
    });
  });

  describe('findByUserId', () => {
    it('should find transactions by user id with pagination', async () => {
      const userId = new Types.ObjectId().toString();
      
      // Create multiple transactions
      await transactionRepository.create({
        userId,
        type: 'CREDIT',
        amount: 1000,
        fee: 50,
        description: 'Credit 1',
        status: 'COMPLETED',
        category: 'TRANSFER',
        reference: 'TXN' + Date.now() + '_1'
      });
      
      await transactionRepository.create({
        userId,
        type: 'DEBIT',
        amount: 500,
        fee: 25,
        description: 'Debit 1',
        status: 'COMPLETED',
        category: 'WITHDRAWAL',
        reference: 'TXN' + Date.now() + '_2'
      });

      const result = await transactionRepository.findByUserId(userId, { page: 1, limit: 10 });

      expect(result).toBeDefined();
      expect(result.transactions).toBeDefined();
      expect(Array.isArray(result.transactions)).toBe(true);
      expect(result.transactions.length).toBeGreaterThan(0);
      expect(result.total).toBeGreaterThan(0);
      expect(result.page).toBe(1);
      expect(result.pages).toBeGreaterThan(0);
      expect(result.transactions[0].userId.toString()).toBe(userId);
    });

    it('should return empty array for user with no transactions', async () => {
      const nonExistentUserId = new Types.ObjectId().toString();
      const result = await transactionRepository.findByUserId(nonExistentUserId, { page: 1, limit: 10 });

      expect(result).toBeDefined();
      expect(Array.isArray(result.transactions)).toBe(true);
      expect(result.transactions.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('update', () => {
    it('should update transaction', async () => {
      const createTransactionDto = {
        userId: new Types.ObjectId().toString(),
        type: 'CREDIT' as const,
        amount: 1000,
        fee: 50,
        description: 'Test transaction',
        status: 'PENDING' as const,
        category: 'TRANSFER',
        reference: 'TXN' + Date.now()
      };

      const createdTransaction = await transactionRepository.create(createTransactionDto);
      const updatedTransaction = await transactionRepository.update(createdTransaction.id, { status: 'COMPLETED' });

      expect(updatedTransaction).toBeDefined();
      expect(updatedTransaction.status).toBe('COMPLETED');
      expect(updatedTransaction.updatedAt).toBeDefined();
    });

    it('should throw error for non-existent transaction', async () => {
      const nonExistentId = new Types.ObjectId().toString();
      
      await expect(
        transactionRepository.update(nonExistentId, { status: 'COMPLETED' })
      ).rejects.toThrow('Transaction not found');
    });
  });

  describe('findMany with filters', () => {
    beforeEach(async () => {
      const userId = new Types.ObjectId().toString();
      
      // Create test transactions
      await transactionRepository.create({
        userId,
        type: 'CREDIT',
        amount: 1000,
        fee: 50,
        description: 'Pending transaction',
        status: 'PENDING',
        category: 'TRANSFER',
        reference: 'TXN' + Date.now() + '_pending'
      });
      
      await transactionRepository.create({
        userId,
        type: 'DEBIT',
        amount: 500,
        fee: 25,
        description: 'Completed transaction',
        status: 'COMPLETED',
        category: 'WITHDRAWAL',
        reference: 'TXN' + Date.now() + '_completed'
      });
    });

    it('should filter transactions by status', async () => {
      const result = await transactionRepository.findMany({ status: 'PENDING', page: 1, limit: 10 });

      expect(result).toBeDefined();
      expect(result.transactions.length).toBeGreaterThan(0);
      result.transactions.forEach(transaction => {
        expect(transaction.status).toBe('PENDING');
      });
    });

    it('should filter transactions by type', async () => {
      const result = await transactionRepository.findMany({ type: 'CREDIT', page: 1, limit: 10 });

      expect(result).toBeDefined();
      expect(result.transactions.length).toBeGreaterThan(0);
      result.transactions.forEach(transaction => {
        expect(transaction.type).toBe('CREDIT');
      });
    });

    it('should filter transactions by category', async () => {
      const result = await transactionRepository.findMany({ category: 'TRANSFER', page: 1, limit: 10 });

      expect(result).toBeDefined();
      result.transactions.forEach(transaction => {
        expect(transaction.category).toBe('TRANSFER');
      });
    });
  });

  describe('getStats', () => {
    it('should return transaction statistics', async () => {
      const userId = new Types.ObjectId().toString();
      
      await transactionRepository.create({
        userId,
        type: 'CREDIT',
        amount: 1000,
        fee: 50,
        description: 'Credit',
        status: 'COMPLETED',
        category: 'TRANSFER',
        reference: 'TXN' + Date.now() + '_1'
      });
      
      await transactionRepository.create({
        userId,
        type: 'DEBIT',
        amount: 500,
        fee: 25,
        description: 'Debit',
        status: 'PENDING',
        category: 'WITHDRAWAL',
        reference: 'TXN' + Date.now() + '_2'
      });

      const stats = await transactionRepository.getStats();

      expect(stats).toBeDefined();
      expect(stats.totalTransactions).toBeGreaterThan(0);
      expect(stats.totalVolume).toBeGreaterThan(0);
      expect(stats.totalFees).toBeGreaterThan(0);
      expect(stats.averageTransactionValue).toBeGreaterThan(0);
      expect(stats.completedTransactions).toBeDefined();
      expect(stats.pendingTransactions).toBeDefined();
      expect(stats.failedTransactions).toBeDefined();
    });

    it('should handle empty database', async () => {
      const stats = await transactionRepository.getStats();

      expect(stats).toBeDefined();
      expect(stats.totalTransactions).toBe(0);
      expect(stats.totalVolume).toBe(0);
      expect(stats.totalFees).toBe(0);
      expect(stats.completedTransactions).toBe(0);
      expect(stats.pendingTransactions).toBe(0);
      expect(stats.failedTransactions).toBe(0);
      expect(stats.averageTransactionValue).toBe(0);
    });
  });

  describe('getTopCategories', () => {
    it('should return top categories by volume', async () => {
      const userId = new Types.ObjectId().toString();
      
      await transactionRepository.create({
        userId,
        type: 'CREDIT',
        amount: 1000,
        fee: 50,
        description: 'Transfer',
        status: 'COMPLETED',
        category: 'TRANSFER',
        reference: 'TXN' + Date.now() + '_1'
      });
      
      await transactionRepository.create({
        userId,
        type: 'DEBIT',
        amount: 500,
        fee: 25,
        description: 'Withdrawal',
        status: 'COMPLETED',
        category: 'WITHDRAWAL',
        reference: 'TXN' + Date.now() + '_2'
      });

      const topCategories = await transactionRepository.getTopCategories(5);

      expect(topCategories).toBeDefined();
      expect(Array.isArray(topCategories)).toBe(true);
      if (topCategories.length > 0) {
        expect(topCategories[0]).toHaveProperty('category');
        expect(topCategories[0]).toHaveProperty('count');
        expect(topCategories[0]).toHaveProperty('volume');
      }
    });
  });

  describe('getVolumeByDate', () => {
    it('should return volume data by date', async () => {
      const userId = new Types.ObjectId().toString();
      
      await transactionRepository.create({
        userId,
        type: 'CREDIT',
        amount: 1000,
        fee: 50,
        description: 'Credit',
        status: 'COMPLETED',
        category: 'TRANSFER',
        reference: 'TXN' + Date.now()
      });

      const volumeData = await transactionRepository.getVolumeByDate(7);

      expect(volumeData).toBeDefined();
      expect(Array.isArray(volumeData)).toBe(true);
      if (volumeData.length > 0) {
        expect(volumeData[0]).toHaveProperty('date');
        expect(volumeData[0]).toHaveProperty('volume');
        expect(volumeData[0]).toHaveProperty('count');
      }
    });
  });

  describe('delete', () => {
    it('should delete transaction', async () => {
      const createTransactionDto = {
        userId: new Types.ObjectId().toString(),
        type: 'CREDIT' as const,
        amount: 1000,
        fee: 50,
        description: 'Test transaction',
        status: 'COMPLETED' as const,
        category: 'TRANSFER',
        reference: 'TXN' + Date.now()
      };

      const createdTransaction = await transactionRepository.create(createTransactionDto);
      const deleted = await transactionRepository.delete(createdTransaction.id);

      expect(deleted).toBe(true);
      
      const transaction = await transactionRepository.findById(createdTransaction.id);
      expect(transaction).toBeNull();
    });

    it('should return false for non-existent transaction', async () => {
      const nonExistentId = new Types.ObjectId().toString();
      const deleted = await transactionRepository.delete(nonExistentId);

      expect(deleted).toBe(false);
    });
  });
});
