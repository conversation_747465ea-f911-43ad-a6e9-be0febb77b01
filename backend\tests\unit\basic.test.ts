import * as bcrypt from 'bcryptjs';

describe('Basic functionality tests', () => {
  describe('bcrypt', () => {
    it('should hash and verify passwords', async () => {
      const password = 'testpassword123';
      const hashedPassword = await bcrypt.hash(password, 10);
      
      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(20);
      
      const isValid = await bcrypt.compare(password, hashedPassword);
      expect(isValid).toBe(true);
      
      const isInvalid = await bcrypt.compare('wrongpassword', hashedPassword);
      expect(isInvalid).toBe(false);
    });
  });

  describe('environment variables', () => {
    it('should have required test environment variables', () => {
      expect(process.env.JWT_SECRET).toBeDefined();
      expect(process.env.JWT_REFRESH_SECRET).toBeDefined();
      expect(process.env.NODE_ENV).toBe('test');
    });
  });

  describe('date operations', () => {
    it('should handle date operations correctly', () => {
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      
      expect(yesterday.getTime()).toBeLessThan(now.getTime());
      expect(tomorrow.getTime()).toBeGreaterThan(now.getTime());
    });
  });

  describe('string operations', () => {
    it('should handle string operations correctly', () => {
      const email = '<EMAIL>';
      const normalizedEmail = email.toLowerCase();
      
      expect(normalizedEmail).toBe('<EMAIL>');
      expect(email.includes('@')).toBe(true);
      expect(email.split('@')).toHaveLength(2);
    });
  });

  describe('array operations', () => {
    it('should handle array operations correctly', () => {
      const numbers = [1, 2, 3, 4, 5];
      const doubled = numbers.map(n => n * 2);
      const sum = numbers.reduce((acc, n) => acc + n, 0);
      
      expect(doubled).toEqual([2, 4, 6, 8, 10]);
      expect(sum).toBe(15);
      expect(numbers.filter(n => n > 3)).toEqual([4, 5]);
    });
  });

  describe('object operations', () => {
    it('should handle object operations correctly', () => {      const user: { id?: string; email: string; firstName: string; lastName: string } = {
        id: '123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      };
      
      const userWithoutId = { ...user };
      delete userWithoutId.id;
      
      expect(user.id).toBe('123');
      expect(userWithoutId.id).toBeUndefined();
      expect(Object.keys(user)).toHaveLength(4);
      expect(Object.keys(userWithoutId)).toHaveLength(3);
    });
  });
});
