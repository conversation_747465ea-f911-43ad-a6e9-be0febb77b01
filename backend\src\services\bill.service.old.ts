import { v4 as uuidv4 } from 'uuid';
import { TransactionService } from './transaction.service';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, BillPayment } from '../interfaces/services';

export class BillService {
  private transactionService = new TransactionService();
  
  // Mock data - replace with database
  private static categories: BillCategory[] = [
    { id: 'airtime', name: 'Airtime', description: 'Mobile airtime top-up', icon: 'phone' },
    { id: 'data', name: 'Data', description: 'Mobile data bundles', icon: 'wifi' },
    { id: 'electricity', name: 'Electricity', description: 'Electricity bill payments', icon: 'zap' },
    { id: 'cable-tv', name: 'Cable TV', description: 'Cable TV subscriptions', icon: 'tv' },
    { id: 'internet', name: 'Internet', description: 'Internet subscriptions', icon: 'globe' }
  ];

  private static providers: Bill<PERSON>rovider[] = [
    { id: 'mtn-airtime', categoryId: 'airtime', name: 'MTN Airtime', code: 'MTN', fee: 0, validationFields: ['phone'] },
    { id: 'glo-airtime', categoryId: 'airtime', name: 'Glo Airtime', code: 'GLO', fee: 0, validationFields: ['phone'] },
    { id: 'airtel-airtime', categoryId: 'airtime', name: 'Airtel Airtime', code: 'AIRTEL', fee: 0, validationFields: ['phone'] },
    { id: 'mtn-data', categoryId: 'data', name: 'MTN Data', code: 'MTN_DATA', fee: 0, validationFields: ['phone'] },
    { id: 'eko-electricity', categoryId: 'electricity', name: 'Eko Electricity', code: 'EKEDC', fee: 100, validationFields: ['meterNumber'] },
    { id: 'dstv', categoryId: 'cable-tv', name: 'DSTV', code: 'DSTV', fee: 100, validationFields: ['smartCardNumber'] }
  ];

  async getBillCategories(): Promise<BillCategory[]> {
    return BillService.categories;
  }

  async getBillProviders(categoryId: string): Promise<BillProvider[]> {
    return BillService.providers.filter(p => p.categoryId === categoryId);
  }

  async validateCustomer(validationData: any) {
    const { providerId, customerNumber, amount } = validationData;
    
    const provider = BillService.providers.find(p => p.id === providerId);
    if (!provider) {
      throw new Error('Provider not found');
    }

    // TODO: Integrate with actual bill provider APIs for validation
    // Mock validation response
    return {
      valid: true,
      customerName: 'John Doe Customer',
      customerNumber,
      providerId,
      providerName: provider.name,
      fee: provider.fee,
      totalAmount: amount ? amount + provider.fee : provider.fee
    };
  }

  async payBill(userId: string, paymentData: any) {
    const { providerId, customerNumber, amount, pin } = paymentData;
    
    // TODO: Validate PIN
    // TODO: Check user balance
    
    const provider = BillService.providers.find(p => p.id === providerId);
    if (!provider) {
      throw new Error('Provider not found');
    }

    const paymentId = uuidv4();
    const reference = `BILL_${Date.now()}`;
    const totalAmount = amount + provider.fee;

    const payment: BillPayment = {
      id: paymentId,
      userId,
      providerId,
      customerNumber,
      amount,
      fee: provider.fee,
      status: 'PENDING',
      reference,
      createdAt: new Date().toISOString()
    };

    // Create transaction record
    await this.transactionService.createTransaction({
      userId,
      type: 'DEBIT',
      amount: totalAmount,
      fee: provider.fee,
      description: `${provider.name} payment for ${customerNumber}`,
      status: 'PENDING',
      category: 'BILL_PAYMENT',
      reference
    });

    // TODO: Process actual bill payment with provider API

    return {
      paymentId,
      reference,
      status: 'PENDING',
      amount,
      fee: provider.fee,
      totalAmount,
      providerName: provider.name,
      customerNumber,
      message: 'Bill payment initiated successfully'
    };
  }
}
