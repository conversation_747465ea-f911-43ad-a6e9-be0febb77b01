import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { WalletService } from '../../../services/wallet.service';
import { validateWithdrawWallet } from '../../../common/validation/wallet.validation';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const body = JSON.parse(event.body || '{}');
    const { error } = validateWithdrawWallet(body);
    if (error) {
      logger.warn('Validation failed', { details: error.details });
      return errorResponse(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    
    const walletService = new WalletService();
    const withdrawalResult = await walletService.withdrawFromWallet(user.userId, body);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: withdrawalResult })
    };
  } catch (err: any) {
    logger.error('Withdraw wallet error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during wallet withdrawal');
  }
};
