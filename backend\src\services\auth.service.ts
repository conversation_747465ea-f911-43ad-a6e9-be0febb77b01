import { v4 as uuidv4 } from 'uuid';
import { UserRepository } from '../repositories/user.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import * as bcrypt from 'bcryptjs';
import { JwtService } from '../common/security/jwt.service';

export class AuthService {
  private userRepository = new UserRepository();
  private walletRepository = new WalletRepository();
  private jwtService = new JwtService();

  async register(data: any) {
    // Check if user already exists
    const existingEmail = await this.userRepository.findByEmail(data.email);
    if (existingEmail) {
      throw new Error('User with this email already exists');
    }

    // Check if phone already exists
    const existingPhone = await this.userRepository.findByPhone(data.phone);
    if (existingPhone) {
      throw new Error('User with this phone number already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, 10);
    
    // Create user
    const user = await this.userRepository.create({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      phone: data.phone,
      password: hashedPassword,
      dateOfBirth: data.dateOfBirth
    });    // Create wallet for the user
    try {
      await this.walletRepository.create({
        userId: user.id,
        accountNumber: `ZW${Date.now()}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        status: 'ACTIVE'
      });
    } catch (error) {
      console.error('Error creating wallet for user:', error);
      // For a financial app, failing to create a wallet should fail registration
      throw new Error('Failed to create user wallet');
    }

    // TODO: Send verification email/SMS
    return {
      userId: user.id,
      verificationStatus: 'PENDING',
      nextSteps: 'Verify your email or phone.',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        kycVerified: user.kycVerified,
        status: user.status
      }
    };
  }
  async login(data: any) {
    const user = await this.userRepository.findByEmailWithPassword(data.email);
    if (!user) {
      throw new Error('Invalid credentials');
    }
    
    const valid = await bcrypt.compare(data.password, user.password);
    if (!valid) {
      throw new Error('Invalid credentials');
    }

    // Update last login time
    try {
      await this.userRepository.updateLastLogin(user.id);
    } catch (error) {
      console.error('Error updating last login:', error);
      // Don't fail login if last login update fails
    }

    // Generate JWT tokens
    const tokenPayload = { 
      userId: user.id, 
      email: user.email,
      role: user.role 
    };
    const tokens = this.jwtService.generateTokenPair(tokenPayload);    
    
    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        kycVerified: user.kycVerified,
        status: user.status,
        role: user.role
      }
    };
  }

  async refresh(refreshToken: string) {
    try {
      const payload = this.jwtService.verifyRefreshToken(refreshToken);
      const user = await this.userRepository.findByEmail(payload.email);
      if (!user) {
        throw new Error('User not found');
      }

      const newTokenPayload = { 
        userId: user.id, 
        email: user.email,
        role: user.role 
      };
      const tokens = this.jwtService.generateTokenPair(newTokenPayload);
      
      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role
        }
      };
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }
}
