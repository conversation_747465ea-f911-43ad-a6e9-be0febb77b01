import { UserService } from '../../src/services/user.service';
import { UserRepository } from '../../src/repositories/user.repository';

jest.mock('../../src/repositories/user.repository');

describe('UserService', () => {
  let userService: UserService;
  let mockUserRepository: jest.Mocked<UserRepository>;

  beforeEach(() => {
    jest.clearAllMocks();
    userService = new UserService();
    mockUserRepository = UserRepository.prototype as jest.Mocked<UserRepository>;
  });

  describe('getProfile', () => {
    it('should return user profile without password', async () => {
      const userId = 'user123';      const mockUser = {
        id: userId,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+2348*********',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01',
        kycVerified: true,
        role: 'USER' as const,
        status: 'ACTIVE' as const,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockUserRepository.findById.mockResolvedValue(mockUser);

      const result = await userService.getProfile(userId);

      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId);      expect(result).toEqual({
        id: userId,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+2348*********',
        dateOfBirth: '1990-01-01',
        kycVerified: true,
        role: 'USER',
        status: 'ACTIVE',
        lastLoginAt: mockUser.lastLoginAt,
        createdAt: mockUser.createdAt,
        updatedAt: mockUser.updatedAt
      });
      expect(result).not.toHaveProperty('password');
    });

    it('should throw error when user not found', async () => {
      const userId = 'nonexistent';
      mockUserRepository.findById.mockResolvedValue(null);

      await expect(userService.getProfile(userId)).rejects.toThrow('User not found');
      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId);
    });

    it('should handle user repository errors', async () => {
      const userId = 'user123';
      mockUserRepository.findById.mockRejectedValue(new Error('Database error'));

      await expect(userService.getProfile(userId)).rejects.toThrow('Database error');
    });
  });
  describe('updateProfile', () => {
    it('should update user profile successfully', async () => {
      const userId = 'user123';
      const updateData = {
        firstName: 'Jane',
        lastName: 'Smith'
      };

      const existingUser = {
        id: userId,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+2348*********',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01',
        kycVerified: true,
        role: 'USER' as const,
        status: 'ACTIVE' as const,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const updatedUser = {
        ...existingUser,
        ...updateData,
        updatedAt: new Date()
      };

      mockUserRepository.findById.mockResolvedValue(existingUser);
      mockUserRepository.update.mockResolvedValue(updatedUser);

      const result = await userService.updateProfile(userId, updateData);

      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId);
      expect(mockUserRepository.update).toHaveBeenCalledWith(userId, updateData);
      expect(result).toEqual({
        id: userId,
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+2348*********',
        dateOfBirth: '1990-01-01',
        kycVerified: true,
        role: 'USER',
        status: 'ACTIVE',
        lastLoginAt: existingUser.lastLoginAt,
        createdAt: existingUser.createdAt,
        updatedAt: updatedUser.updatedAt
      });
      expect(result).not.toHaveProperty('password');
    });

    it('should throw error when user not found during update', async () => {
      const userId = 'nonexistent';
      const updateData = { firstName: 'Jane' };

      mockUserRepository.findById.mockResolvedValue(null);

      await expect(userService.updateProfile(userId, updateData)).rejects.toThrow('User not found');
      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId);
      expect(mockUserRepository.update).not.toHaveBeenCalled();
    });

    it('should handle update repository errors', async () => {
      const userId = 'user123';
      const updateData = { firstName: 'Jane' };
      const existingUser = {
        id: userId,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+2348*********',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01',
        kycVerified: true,
        role: 'USER' as const,
        status: 'ACTIVE' as const,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockUserRepository.findById.mockResolvedValue(existingUser);
      mockUserRepository.update.mockRejectedValue(new Error('Database error'));

      await expect(userService.updateProfile(userId, updateData)).rejects.toThrow('Database error');
    });
  });

  describe('submitKYC', () => {
    it('should submit KYC documents successfully', async () => {
      const userId = 'user123';
      const kycData = {
        documentType: 'passport',
        documentNumber: 'A12345678',
        documentImage: 'base64image'
      };

      const result = await userService.submitKYC(userId, kycData);

      expect(result).toEqual({
        verificationStatus: 'PENDING',
        referenceId: expect.any(String),
        message: 'KYC documents submitted successfully. Verification may take 1-3 business days.'
      });
      expect(result.referenceId).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/);
    });

    it('should generate unique reference IDs', async () => {
      const userId = 'user123';
      const kycData = { documentType: 'passport' };

      const result1 = await userService.submitKYC(userId, kycData);
      const result2 = await userService.submitKYC(userId, kycData);

      expect(result1.referenceId).not.toEqual(result2.referenceId);
    });

    it('should handle different KYC data formats', async () => {
      const userId = 'user123';
      const kycData = {
        documentType: 'drivers_license',
        documentNumber: 'DL123456',
        dateOfBirth: '1990-01-01',
        address: '123 Main St'
      };

      const result = await userService.submitKYC(userId, kycData);

      expect(result.verificationStatus).toBe('PENDING');
      expect(result.referenceId).toBeDefined();
    });

    it('should handle empty KYC data', async () => {
      const userId = 'user123';
      const kycData = {};

      const result = await userService.submitKYC(userId, kycData);

      expect(result.verificationStatus).toBe('PENDING');
      expect(result.referenceId).toBeDefined();
    });    it('should handle empty KYC data', async () => {
      const validData = {
        documentType: 'NIN',
        documentNumber: '*********01',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos'
        }
      };

      const result = await userService.submitKYC('user123', validData);

      expect(result).toEqual({
        verificationStatus: 'PENDING',
        referenceId: expect.any(String),
        message: 'KYC documents submitted successfully. Verification may take 1-3 business days.'
      });
    });it('should handle KYC data with all document types', async () => {
      const documentTypes = ['NIN', 'PASSPORT', 'DRIVERS_LICENSE'];
      
      for (const docType of documentTypes) {
        const kycData = {
          documentType: docType,
          documentNumber: '*********',
          documentImage: 'base64encodedimage',
          selfieImage: 'base64encodedselfie',
          address: {
            street: '123 Test Street',
            city: 'Test City',
            state: 'Test State',
            country: 'Nigeria'
          }
        };

        const result = await userService.submitKYC('user123', kycData);
        expect(result.verificationStatus).toBe('PENDING');
      }
    });

    it('should generate different reference IDs for concurrent submissions', async () => {
      const kycData = {
        documentType: 'NIN',
        documentNumber: '*********01',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos'
        }
      };

      const result1 = await userService.submitKYC('user123', kycData);
      const result2 = await userService.submitKYC('user456', kycData);
      
      expect(result1.referenceId).not.toBe(result2.referenceId);
      expect(result1.message).toBe(result2.message);
    });

    it('should handle updateProfile with all valid fields', async () => {
      const userId = 'user123';
      const updateData = {
        firstName: 'UpdatedFirst',
        lastName: 'UpdatedLast',
        phone: '+2348*********',
        dateOfBirth: '1995-05-15'
      };

      const existingUser = {
        id: userId,
        firstName: 'Original',
        lastName: 'Name',
        email: '<EMAIL>',
        phone: '+2348000000000',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01',
        kycVerified: false,
        role: 'USER' as const,
        status: 'ACTIVE' as const,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const updatedUser = {
        ...existingUser,
        ...updateData,
        updatedAt: new Date()
      };

      mockUserRepository.findById.mockResolvedValue(existingUser);
      mockUserRepository.update.mockResolvedValue(updatedUser);

      const result = await userService.updateProfile(userId, updateData);

      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId);
      expect(mockUserRepository.update).toHaveBeenCalledWith(userId, updateData);
      expect(result).toEqual({
        id: userId,
        firstName: 'UpdatedFirst',
        lastName: 'UpdatedLast',
        email: '<EMAIL>',
        phone: '+2348*********',
        dateOfBirth: '1995-05-15',
        kycVerified: false,
        role: 'USER',
        status: 'ACTIVE',
        lastLoginAt: existingUser.lastLoginAt,
        createdAt: existingUser.createdAt,
        updatedAt: updatedUser.updatedAt
      });
    });

    it('should handle partial profile updates correctly', async () => {
      const userId = 'user123';
      const updateData = {
        firstName: 'OnlyFirstName'
      };

      const existingUser = {
        id: userId,
        firstName: 'Original',
        lastName: 'Name',
        email: '<EMAIL>',
        phone: '+2348000000000',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01',
        kycVerified: false,
        role: 'USER' as const,
        status: 'ACTIVE' as const,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const updatedUser = {
        ...existingUser,
        firstName: 'OnlyFirstName',
        updatedAt: new Date()
      };

      mockUserRepository.findById.mockResolvedValue(existingUser);
      mockUserRepository.update.mockResolvedValue(updatedUser);

      const result = await userService.updateProfile(userId, updateData);      expect(result.firstName).toBe('OnlyFirstName');
      expect(result.lastName).toBe('Name'); // Should remain unchanged
    });
  });
});
