import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { TransferService } from '../../../services/transfer.service';
import { validateRecipient } from '../../../common/validation/transfer.validation';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const body = JSON.parse(event.body || '{}');
    const { error } = validateRecipient(body);
    if (error) {
      logger.warn('Validation failed', { details: error.details });
      return errorResponse(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    
    const transferService = new TransferService();
    const validationResult = await transferService.validateRecipient(body);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: validationResult })
    };
  } catch (err: any) {
    logger.error('Validate recipient error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during recipient validation');
  }
};
