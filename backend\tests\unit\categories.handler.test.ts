import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { main as categoriesHand<PERSON> } from '../../src/api/v1/bills/categories.handler';
import { BillService } from '../../src/services/bill.service';

// Mock BillService
jest.mock('../../src/services/bill.service');

// Mock DatabaseConfig
jest.mock('../../src/config/database', () => ({
  DatabaseConfig: {
    getInstance: jest.fn().mockReturnValue({
      connect: jest.fn().mockResolvedValue(undefined)
    })
  }
}));

describe('Bill Categories Handler', () => {
  let mockBillService: jest.Mocked<BillService>;
  let mockEvent: Partial<APIGatewayProxyEvent>;

  beforeEach(() => {
    mockBillService = {
      getBillCategories: jest.fn(),
      getBillProviders: jest.fn(),
      validateCustomer: jest.fn(),
      payBill: jest.fn()
    } as any;
    (BillService as jest.MockedClass<typeof BillService>).mockImplementation(() => mockBillService);

    mockEvent = {
      httpMethod: 'GET',
      path: '/bills/categories',
      headers: {},
      isBase64Encoded: false,
      multiValueHeaders: {},
      multiValueQueryStringParameters: null,
      pathParameters: null,
      queryStringParameters: null,
      requestContext: {} as any,
      resource: '',
      stageVariables: null,
      body: null
    };

    jest.clearAllMocks();
  });

  describe('successful retrieval', () => {
    it('should return all bill categories', async () => {
      const mockCategories = [
        {
          id: 'airtime',
          name: 'Airtime',
          description: 'Mobile airtime top-up',
          icon: 'phone',
          isActive: true
        },
        {
          id: 'data',
          name: 'Data',
          description: 'Mobile data bundles',
          icon: 'wifi',
          isActive: true
        },
        {
          id: 'electricity',
          name: 'Electricity',
          description: 'Electricity bill payments',
          icon: 'flash',
          isActive: true
        }
      ];

      mockBillService.getBillCategories.mockResolvedValue(mockCategories);

      const result = await categoriesHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data).toBeDefined();
      expect(body.data.categories).toHaveLength(3);
      expect(body.data.categories[0]).toHaveProperty('id');
      expect(body.data.categories[0]).toHaveProperty('name');
      expect(body.data.categories[0]).toHaveProperty('description');
      expect(mockBillService.getBillCategories).toHaveBeenCalled();
    });

    it('should return categories with required properties', async () => {
      const mockCategories = [
        {
          id: 'airtime',
          name: 'Airtime',
          description: 'Mobile airtime top-up',
          icon: 'phone',
          isActive: true
        }
      ];

      mockBillService.getBillCategories.mockResolvedValue(mockCategories);

      const result = await categoriesHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.data.categories[0]).toEqual(expect.objectContaining({
        id: expect.any(String),
        name: expect.any(String),
        description: expect.any(String),
        icon: expect.any(String),
        isActive: expect.any(Boolean)
      }));
    });
  });

  describe('service errors', () => {
    it('should handle service errors gracefully', async () => {
      mockBillService.getBillCategories.mockRejectedValue(new Error('Database connection failed'));

      const result = await categoriesHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
    });

    it('should handle undefined categories', async () => {
      mockBillService.getBillCategories.mockResolvedValue(undefined as any);

      const result = await categoriesHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data.categories).toBeDefined();
    });
  });

  describe('edge cases', () => {
    it('should handle empty categories list', async () => {
      mockBillService.getBillCategories.mockResolvedValue([]);

      const result = await categoriesHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data.categories).toEqual([]);
    });

    it('should include CORS headers', async () => {
      mockBillService.getBillCategories.mockResolvedValue([]);

      const result = await categoriesHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.headers).toEqual(expect.objectContaining({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'OPTIONS,POST,GET,PUT,DELETE'
      }));
    });

    it('should handle different HTTP methods', async () => {
      mockEvent.httpMethod = 'OPTIONS';
      mockBillService.getBillCategories.mockResolvedValue([]);

      const result = await categoriesHandler(mockEvent as APIGatewayProxyEvent);

      // Should still work for OPTIONS requests
      expect([200, 204]).toContain(result.statusCode);
    });
  });
});
