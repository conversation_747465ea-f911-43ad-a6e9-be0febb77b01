import Joi from 'joi';

export const fundWalletSchema = Joi.object({
  amount: Joi.number().min(100).required(),
  paymentMethod: Joi.string().valid('CARD', 'BANK_TRANSFER', 'card', 'bank_transfer').required(),
  paymentDetails: Joi.object().optional()
});

export const withdrawWalletSchema = Joi.object({
  amount: Joi.number().min(100).required(),
  bankCode: Joi.string().required(),
  accountNumber: Joi.string().required(),
  pin: Joi.string().length(4).required()
});

export function validateFundWallet(data: any) {
  return fundWalletSchema.validate(data, { abortEarly: true });
}

export function validateWithdrawWallet(data: any) {
  return withdrawWalletSchema.validate(data, { abortEarly: true });
}
