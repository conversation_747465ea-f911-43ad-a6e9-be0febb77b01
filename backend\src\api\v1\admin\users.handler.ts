import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { AdminService } from '../../../services/admin.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';
import { DatabaseConfig } from '../../../config/database';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Ensure database connection
    await DatabaseConfig.getInstance().connect();
    
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    // TODO: Check if user has admin role
    
    const queryParams = event.queryStringParameters || {};
    const adminService = new AdminService();
    const users = await adminService.getUsers(queryParams);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: users })
    };
  } catch (err: any) {
    logger.error('Get admin users error', { error: err.message });
    return errorResponse(500, 'INTERNAL_SERVER_ERROR', err.message);
  }
};
