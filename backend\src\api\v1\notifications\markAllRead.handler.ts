import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { NotificationService } from '../../../services/notification.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const notificationService = new NotificationService();
    const result = await notificationService.markAllAsRead(user.userId);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: result })
    };
  } catch (err: any) {
    logger.error('Mark all notifications read error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred while marking all notifications as read');
  }
};
