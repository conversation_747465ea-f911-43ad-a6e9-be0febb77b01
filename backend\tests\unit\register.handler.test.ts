import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { main as registerHandler } from '../../src/api/v1/auth/register.handler';
import { UserRepository } from '../../src/repositories/user.repository';
import { WalletRepository } from '../../src/repositories/wallet.repository';
import { DatabaseConfig } from '../../src/config/database';
import { AuthService } from '../../src/services/auth.service';

// Mock the repositories, services, and database
jest.mock('../../src/repositories/user.repository');
jest.mock('../../src/repositories/wallet.repository');
jest.mock('../../src/config/database');
jest.mock('../../src/services/auth.service');

const mockDatabaseConfig = {
  getInstance: jest.fn().mockReturnValue({
    connect: jest.fn().mockResolvedValue(undefined)
  })
};

(DatabaseConfig as any) = mockDatabaseConfig;

describe('Register Handler', () => {
  let mockEvent: Partial<APIGatewayProxyEvent>;
  let mockContext: Partial<Context>;
  let mockUserRepository: jest.Mocked<UserRepository>;
  let mockWalletRepository: jest.Mocked<WalletRepository>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    mockEvent = {
      headers: {},
      multiValueHeaders: {},
      httpMethod: 'POST',
      isBase64Encoded: false,
      path: '/api/v1/auth/register',
      pathParameters: null,
      queryStringParameters: null,
      multiValueQueryStringParameters: null,
      stageVariables: null,
      requestContext: {
        requestId: 'test-request-id',
        stage: 'test',
        resourceId: 'test-resource',
        httpMethod: 'POST',
        resourcePath: '/api/v1/auth/register',
        accountId: 'test-account',
        apiId: 'test-api',
        protocol: 'HTTP/1.1',
        requestTime: '01/Jan/2025:00:00:00 +0000',
        requestTimeEpoch: **********,
        identity: {
          accessKey: null,
          accountId: null,
          apiKey: null,
          apiKeyId: null,
          caller: null,
          cognitoAuthenticationProvider: null,
          cognitoAuthenticationType: null,
          cognitoIdentityId: null,
          cognitoIdentityPoolId: null,
          principalOrgId: null,
          sourceIp: '127.0.0.1',
          user: null,
          userAgent: 'test-agent',
          userArn: null,
          clientCert: null
        },
        authorizer: null
      },
      resource: '/api/v1/auth/register'
    };

    mockContext = {
      callbackWaitsForEmptyEventLoop: false,
      functionName: 'test-function',
      functionVersion: '1.0',
      invokedFunctionArn: 'arn:aws:lambda:us-east-1:*********:function:test-function',
      memoryLimitInMB: '128',
      awsRequestId: 'test-request-id',
      logGroupName: '/aws/lambda/test-function',
      logStreamName: 'test-stream',
      getRemainingTimeInMillis: () => 30000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn()
    };

    // Mock repository instances
    mockUserRepository = {
      create: jest.fn(),
      findByEmail: jest.fn(),
      findByPhone: jest.fn(),
      findById: jest.fn(),
      findByEmailWithPassword: jest.fn(),
      update: jest.fn(),
      getStats: jest.fn()
    } as any;    mockWalletRepository = {
      create: jest.fn(),
      findByUserId: jest.fn(),
      findById: jest.fn(),
      updateBalance: jest.fn(),
      transferFunds: jest.fn(),
      movePendingToAvailable: jest.fn(),
      updateDailyTransferUsed: jest.fn(),
      resetDailyLimits: jest.fn(),
      updateLimits: jest.fn(),
      getTotalBalance: jest.fn(),
      getTopWalletsByBalance: jest.fn(),
      delete: jest.fn()
    } as any;    // Mock AuthService with dynamic behavior
    const mockAuthService = {
      register: jest.fn().mockImplementation(async (data) => {
        try {
          // Check if user already exists by email
          const existingEmail = await mockUserRepository.findByEmail(data.email);
          if (existingEmail) {
            throw new Error('User with this email already exists');
          }
          
          // Check if user already exists by phone
          const existingPhone = await mockUserRepository.findByPhone(data.phone);
          if (existingPhone) {
            throw new Error('User with this phone number already exists');
          }

          // Mock user creation
          const user = await mockUserRepository.create(data);

          // Mock wallet creation - this can fail
          try {
            await mockWalletRepository.create({ userId: user.id });
          } catch (error) {
            throw new Error('Failed to create user wallet');
          }

          // Mock successful registration
          return {
            userId: user.id,
            verificationStatus: 'PENDING',
            nextSteps: 'Verify your email or phone.',
            user: {
              id: user.id,
              email: data.email,
              firstName: data.firstName,
              lastName: data.lastName,
              phone: data.phone,
              kycVerified: false,
              status: 'ACTIVE'
            }
          };
        } catch (error: any) {
          // Re-throw any errors from repository calls (like database errors)
          throw error;
        }
      }),
      login: jest.fn(),
      refresh: jest.fn()
    };

    (AuthService as jest.MockedClass<typeof AuthService>).mockImplementation(() => mockAuthService as any);
  });

  describe('successful registration', () => {
    it('should register a new user successfully', async () => {      const validRegisterData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        phone: '+2348*********',
        dateOfBirth: '1990-01-01',
        agreeToTerms: true
      };

      mockEvent.body = JSON.stringify(validRegisterData);

      // Mock repository responses
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.findByPhone.mockResolvedValue(null);      mockUserRepository.create.mockResolvedValue({
        id: 'user-id-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+2348*********',
        isActive: true,
        kycStatus: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date()
      } as any);

      mockWalletRepository.create.mockResolvedValue({
        id: 'wallet-id-123',
        userId: 'user-id-123',
        available: 0,
        pending: 0,
        currency: 'NGN',
        limits: {
          dailyTransfer: 1000000,
          dailyTransferUsed: 0,
          singleTransferMax: 500000
        },
        createdAt: new Date(),
        updatedAt: new Date()
      } as any);

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data).toBeDefined();
      expect(body.data.user).toBeDefined();
      expect(body.data.userId).toBe('user-id-123');
      expect(body.data.verificationStatus).toBe('PENDING');
    });
  });

  describe('validation errors', () => {
    it('should return 400 for invalid request body', async () => {
      mockEvent.body = 'invalid json';

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });

    it('should return 400 for missing required fields', async () => {
      mockEvent.body = JSON.stringify({
        email: '<EMAIL>'
      });

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid email format', async () => {
      mockEvent.body = JSON.stringify({
        firstName: 'John',
        lastName: 'Doe',
        email: 'invalid-email',
        password: 'StrongPassword123!',
        phoneNumber: '+2348*********'
      });

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('duplicate user errors', () => {
    it('should return 409 for duplicate email', async () => {      const validRegisterData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        phone: '+2348*********',
        dateOfBirth: '1990-01-01',
        agreeToTerms: true
      };

      mockEvent.body = JSON.stringify(validRegisterData);

      // Mock existing user
      mockUserRepository.findByEmail.mockResolvedValue({
        id: 'existing-user-id',
        email: '<EMAIL>'
      } as any);

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(409);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('USER_EXISTS');
    });    it('should return 409 for duplicate phone number', async () => {
      const validRegisterData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        phone: '+2348*********',
        dateOfBirth: '1990-01-01',
        agreeToTerms: true
      };

      mockEvent.body = JSON.stringify(validRegisterData);

      // Mock no email conflict but phone conflict
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.findByPhone.mockResolvedValue({
        id: 'existing-user-id',
        phoneNumber: '+2348*********'
      } as any);

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(409);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('USER_EXISTS');
    });
  });

  describe('server errors', () => {    it('should return 500 for database errors', async () => {
      const validRegisterData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        phone: '+2348*********',
        dateOfBirth: '1990-01-01',
        agreeToTerms: true
      };

      mockEvent.body = JSON.stringify(validRegisterData);

      // Mock database error
      mockUserRepository.findByEmail.mockRejectedValue(new Error('Database connection failed'));

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
    });    it('should handle wallet creation failure', async () => {
      const validRegisterData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        phone: '+2348*********',
        dateOfBirth: '1990-01-01',
        agreeToTerms: true
      };

      mockEvent.body = JSON.stringify(validRegisterData);

      // Mock successful user creation but wallet creation failure
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.findByPhone.mockResolvedValue(null);      mockUserRepository.create.mockResolvedValue({
        id: 'user-id-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+2348*********',
        isActive: true,
        kycStatus: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date()
      } as any);

      mockWalletRepository.create.mockRejectedValue(new Error('Wallet creation failed'));

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('edge cases', () => {
    it('should handle empty request body', async () => {
      mockEvent.body = '';

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });

    it('should handle null request body', async () => {
      mockEvent.body = null;

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });

    it('should handle undefined request body', async () => {
      delete mockEvent.body;

      const result = await registerHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });
});
