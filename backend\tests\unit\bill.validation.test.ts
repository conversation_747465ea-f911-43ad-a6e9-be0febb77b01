import { validateBillValidation, validateBillPayment } from '../../src/common/validation/bill.validation';

describe('Bill Validation', () => {
  describe('validateBillValidation', () => {
    it('should validate correct bill validation data', () => {
      const validData = {
        providerId: 'MTN',
        customerNumber: '08123456789'
      };

      const { error } = validateBillValidation(validData);
      expect(error).toBeUndefined();
    });

    it('should validate with optional amount', () => {
      const validData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 1000
      };

      const { error } = validateBillValidation(validData);
      expect(error).toBeUndefined();
    });

    it('should reject missing providerId', () => {
      const invalidData = {
        customerNumber: '08123456789'
      };

      const { error } = validateBillValidation(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('providerId');
    });

    it('should reject missing customerNumber', () => {
      const invalidData = {
        providerId: 'MTN'
      };

      const { error } = validateBillValidation(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('customerNumber');
    });

    it('should reject negative amount', () => {
      const invalidData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: -100
      };

      const { error } = validateBillValidation(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('greater than or equal to 0');
    });

    it('should reject empty strings', () => {
      const invalidData = {
        providerId: '',
        customerNumber: ''
      };

      const { error } = validateBillValidation(invalidData);
      expect(error).toBeDefined();
    });

    it('should reject non-string providerId', () => {
      const invalidData = {
        providerId: 123,
        customerNumber: '08123456789'
      };

      const { error } = validateBillValidation(invalidData);
      expect(error).toBeDefined();
    });
  });

  describe('validateBillPayment', () => {
    it('should validate correct bill payment data', () => {
      const validData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 500,
        pin: '1234'
      };

      const { error } = validateBillPayment(validData);
      expect(error).toBeUndefined();
    });

    it('should reject missing required fields', () => {
      const invalidData = {
        providerId: 'MTN'
      };

      const { error } = validateBillPayment(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('required');
    });

    it('should reject amount below minimum', () => {
      const invalidData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 25,
        pin: '1234'
      };

      const { error } = validateBillPayment(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('50');
    });

    it('should reject invalid pin length', () => {
      const invalidData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 500,
        pin: '123'
      };

      const { error } = validateBillPayment(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('4');
    });

    it('should reject pin longer than 4 digits', () => {
      const invalidData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 500,
        pin: '12345'
      };

      const { error } = validateBillPayment(invalidData);
      expect(error).toBeDefined();
    });

    it('should handle edge case amounts', () => {
      const validData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 50, // Exact minimum
        pin: '1234'
      };

      const { error } = validateBillPayment(validData);
      expect(error).toBeUndefined();
    });

    it('should handle large amounts', () => {
      const validData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 100000,
        pin: '1234'
      };

      const { error } = validateBillPayment(validData);
      expect(error).toBeUndefined();
    });

    it('should reject non-numeric amount', () => {
      const invalidData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 'invalid',
        pin: '1234'
      };

      const { error } = validateBillPayment(invalidData);
      expect(error).toBeDefined();
    });

    it('should validate with very large amounts', () => {
      const validData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 999999999,
        pin: '1234'
      };

      const { error } = validateBillPayment(validData);
      expect(error).toBeUndefined();
    });

    it('should validate different provider IDs', () => {
      const providers = ['MTN', 'GLO', 'AIRTEL', '9MOBILE', 'DSTV', 'GOTV'];
      
      providers.forEach(provider => {
        const validData = {
          providerId: provider,
          customerNumber: '08123456789',
          amount: 1000,
          pin: '1234'
        };

        const { error } = validateBillPayment(validData);
        expect(error).toBeUndefined();
      });
    });

    it('should validate different customer number formats', () => {
      const customerNumbers = [
        '08123456789',
        '07123456789',
        '09123456789',
        '2348123456789',
        '**********1'
      ];
      
      customerNumbers.forEach(customerNumber => {
        const validData = {
          providerId: 'MTN',
          customerNumber,
          amount: 1000,
          pin: '1234'
        };

        const { error } = validateBillPayment(validData);
        expect(error).toBeUndefined();
      });
    });

    it('should handle whitespace in providerId and customerNumber', () => {
      const validData = {
        providerId: '  MTN  ',
        customerNumber: '  08123456789  ',
        amount: 1000,
        pin: '1234'
      };

      const { error } = validateBillPayment(validData);
      expect(error).toBeUndefined();
    });

    it('should validate different pin formats', () => {
      const pins = ['1234', '0000', '9999', '1111'];
      
      pins.forEach(pin => {
        const validData = {
          providerId: 'MTN',
          customerNumber: '08123456789',
          amount: 1000,
          pin
        };

        const { error } = validateBillPayment(validData);
        expect(error).toBeUndefined();
      });
    });    it('should accept pin with non-numeric characters (validation only checks length)', () => {
      const validData = {
        providerId: 'MTN',
        customerNumber: '08123456789',
        amount: 1000,
        pin: 'abcd'
      };

      const { error } = validateBillPayment(validData);
      expect(error).toBeUndefined();
    });

    it('should handle zero amount for bill validation', () => {
      const validData = {
        providerId: 'DSTV',
        customerNumber: '**********',
        amount: 0
      };

      const { error } = validateBillValidation(validData);
      expect(error).toBeUndefined();
    });

    it('should validate bill validation with different provider types', () => {
      const providers = [
        { id: 'MTN', number: '08123456789' },
        { id: 'DSTV', number: '**********' },
        { id: 'EKEDC', number: '**********1' }
      ];
      
      providers.forEach(provider => {
        const validData = {
          providerId: provider.id,
          customerNumber: provider.number
        };

        const { error } = validateBillValidation(validData);
        expect(error).toBeUndefined();
      });
    });
  });
});
