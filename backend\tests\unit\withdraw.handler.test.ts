import { main } from '../../src/api/v1/wallets/withdraw.handler';
import { WalletService } from '../../src/services/wallet.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';
import { validateWithdrawWallet } from '../../src/common/validation/wallet.validation';

jest.mock('../../src/services/wallet.service');
jest.mock('../../src/common/middlewares/auth.middleware');
jest.mock('../../src/common/validation/wallet.validation');

describe('Withdraw Handler', () => {
  let mockWalletService: jest.Mocked<WalletService>;
  let mockAuthMiddleware: jest.Mocked<AuthMiddleware>;
  let mockValidateWithdrawWallet: jest.MockedFunction<typeof validateWithdrawWallet>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockWalletService = {
      withdrawFromWallet: jest.fn(),
    } as any;
    (WalletService as jest.MockedClass<typeof WalletService>).mockImplementation(() => mockWalletService);

    mockAuthMiddleware = {
      authenticate: jest.fn(),
    } as any;
    (AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>).mockImplementation(() => mockAuthMiddleware);

    mockValidateWithdrawWallet = validateWithdrawWallet as jest.MockedFunction<typeof validateWithdrawWallet>;
  });

  describe('successful withdrawal', () => {
    it('should initiate wallet withdrawal successfully', async () => {
      const event = {
        body: JSON.stringify({
          amount: 5000,
          bankAccount: '**********',
          bankCode: '044',
          accountName: 'John Doe'
        }),
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };      const withdrawalResult = {
        transactionId: 'txn_123',
        status: 'PENDING',
        amount: 5000,
        bankCode: '044',
        accountNumber: '**********',
        message: 'Withdrawal initiated successfully. Processing may take 1-3 business days.'
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue({ error: undefined } as any);
      mockWalletService.withdrawFromWallet.mockResolvedValue(withdrawalResult);

      const result = await main(event);

      expect(mockAuthMiddleware.authenticate).toHaveBeenCalledWith(event);
      expect(mockValidateWithdrawWallet).toHaveBeenCalledWith({
        amount: 5000,
        bankAccount: '**********',
        bankCode: '044',
        accountName: 'John Doe'
      });
      expect(mockWalletService.withdrawFromWallet).toHaveBeenCalledWith('user123', {
        amount: 5000,
        bankAccount: '**********',
        bankCode: '044',
        accountName: 'John Doe'
      });
      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: withdrawalResult
      });
    });

    it('should handle different bank details', async () => {
      const event = {
        body: JSON.stringify({
          amount: 10000,
          bankAccount: '**********',
          bankCode: '058',
          accountName: 'Jane Smith'
        }),
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user456', email: '<EMAIL>' };      const withdrawalResult = {
        transactionId: 'txn_456',
        status: 'PENDING',
        amount: 10000,
        bankCode: '058',
        accountNumber: '**********',
        message: 'Withdrawal initiated successfully. Processing may take 1-3 business days.'
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue({ error: undefined } as any);
      mockWalletService.withdrawFromWallet.mockResolvedValue(withdrawalResult);

      const result = await main(event);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: withdrawalResult
      });
    });
  });

  describe('validation errors', () => {
    it('should return 400 for missing amount', async () => {
      const event = {
        body: JSON.stringify({
          bankAccount: '**********',
          bankCode: '044',
          accountName: 'John Doe'
        }),
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };
      const validationError = {
        error: {
          details: [{ message: '"amount" is required' }]
        }
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue(validationError as any);      const result = await main(event);

      expect(result.statusCode).toBe(400);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('VALIDATION_ERROR');
      expect(response.error.message).toBe('"amount" is required');
    });

    it('should return 400 for invalid amount', async () => {
      const event = {
        body: JSON.stringify({
          amount: -1000,
          bankAccount: '**********',
          bankCode: '044',
          accountName: 'John Doe'
        }),
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };
      const validationError = {
        error: {
          details: [{ message: '"amount" must be greater than 0' }]
        }
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue(validationError as any);      const result = await main(event);

      expect(result.statusCode).toBe(400);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('VALIDATION_ERROR');
      expect(response.error.message).toBe('"amount" must be greater than 0');
    });

    it('should return 400 for missing bank details', async () => {
      const event = {
        body: JSON.stringify({
          amount: 5000
        }),
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };
      const validationError = {
        error: {
          details: [{ message: '"bankAccount" is required' }]
        }
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue(validationError as any);      const result = await main(event);

      expect(result.statusCode).toBe(400);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('VALIDATION_ERROR');
      expect(response.error.message).toBe('"bankAccount" is required');
    });
  });

  describe('authentication errors', () => {
    it('should return 500 for missing authorization', async () => {
      const event = {
        body: JSON.stringify({
          amount: 5000,
          bankAccount: '**********',
          bankCode: '044',
          accountName: 'John Doe'
        })
      } as any;

      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });      const result = await main(event);

      expect(result.statusCode).toBe(500);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('INTERNAL_ERROR');
      expect(response.error.message).toBe('An error occurred during wallet withdrawal');
    });
  });

  describe('service errors', () => {
    it('should handle wallet service errors', async () => {
      const event = {
        body: JSON.stringify({
          amount: 5000,
          bankAccount: '**********',
          bankCode: '044',
          accountName: 'John Doe'
        }),
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue({ error: undefined } as any);
      mockWalletService.withdrawFromWallet.mockRejectedValue(new Error('Insufficient balance'));      const result = await main(event);

      expect(result.statusCode).toBe(500);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('INTERNAL_ERROR');
      expect(response.error.message).toBe('An error occurred during wallet withdrawal');
    });

    it('should handle database connection errors', async () => {
      const event = {
        body: JSON.stringify({
          amount: 5000,
          bankAccount: '**********',
          bankCode: '044',
          accountName: 'John Doe'
        }),
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue({ error: undefined } as any);
      mockWalletService.withdrawFromWallet.mockRejectedValue(new Error('Database connection failed'));      const result = await main(event);

      expect(result.statusCode).toBe(500);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('INTERNAL_ERROR');
      expect(response.error.message).toBe('An error occurred during wallet withdrawal');
    });
  });

  describe('edge cases', () => {
    it('should handle invalid JSON in request body', async () => {
      const event = {
        body: 'invalid json',
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };
      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);      const result = await main(event);

      expect(result.statusCode).toBe(500);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('INTERNAL_ERROR');
      expect(response.error.message).toBe('An error occurred during wallet withdrawal');
    });

    it('should handle empty request body', async () => {
      const event = {
        body: '{}',
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };
      const validationError = {
        error: {
          details: [{ message: '"amount" is required' }]
        }
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue(validationError as any);      const result = await main(event);

      expect(result.statusCode).toBe(400);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('VALIDATION_ERROR');
      expect(response.error.message).toBe('"amount" is required');
    });

    it('should handle null request body', async () => {
      const event = {
        body: null,
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };
      const validationError = {
        error: {
          details: [{ message: '"amount" is required' }]
        }
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue(validationError as any);      const result = await main(event);

      expect(result.statusCode).toBe(400);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('VALIDATION_ERROR');
      expect(response.error.message).toBe('"amount" is required');
    });

    it('should handle large withdrawal amounts', async () => {
      const event = {
        body: JSON.stringify({
          amount: 1000000,
          bankAccount: '**********',
          bankCode: '044',
          accountName: 'John Doe'
        }),
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };      const withdrawalResult = {
        transactionId: 'txn_large',
        status: 'PENDING',
        amount: 1000000,
        bankCode: '044',
        accountNumber: '**********',
        message: 'Withdrawal initiated successfully. Processing may take 1-3 business days.'
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockValidateWithdrawWallet.mockReturnValue({ error: undefined } as any);
      mockWalletService.withdrawFromWallet.mockResolvedValue(withdrawalResult);

      const result = await main(event);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: withdrawalResult
      });
    });
  });
});
