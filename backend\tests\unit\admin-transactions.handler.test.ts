import { APIGatewayProxyEvent } from 'aws-lambda';
import { main } from '../../src/api/v1/admin/transactions.handler';
import { AdminService } from '../../src/services/admin.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';
import { DatabaseConfig } from '../../src/config/database';

// Mock dependencies
jest.mock('../../src/services/admin.service');
jest.mock('../../src/common/middlewares/auth.middleware');
jest.mock('../../src/config/database');

describe('Admin Transactions Handler', () => {
  let mockEvent: APIGatewayProxyEvent;
  let mockAdminServiceInstance: any;
  let mockAuthMiddlewareInstance: any;
  let mockDatabaseConfig: any;

  beforeEach(() => {
    mockEvent = {
      queryStringParameters: null,
      headers: { Authorization: 'Bearer admin-token' }
    } as any;

    mockAdminServiceInstance = {
      getTransactions: jest.fn()
    };

    mockAuthMiddlewareInstance = {
      authenticate: jest.fn()
    };

    mockDatabaseConfig = {
      connect: jest.fn()
    };

    (AdminService as jest.MockedClass<typeof AdminService>).mockImplementation(() => mockAdminServiceInstance);
    (AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>).mockImplementation(() => mockAuthMiddlewareInstance);
    (DatabaseConfig.getInstance as jest.Mock).mockReturnValue(mockDatabaseConfig);

    jest.clearAllMocks();
  });

  describe('successful transactions retrieval', () => {
    it('should return admin transactions successfully', async () => {
      const mockUser = { 
        userId: 'admin123', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      };
      const mockTransactions = {
        transactions: [
          {
            id: 'txn1',
            amount: 10000,
            type: 'TRANSFER',
            status: 'COMPLETED',
            createdAt: '2024-01-01T00:00:00.000Z'
          }
        ],
        pagination: {
          total: 1,
          pages: 1,
          currentPage: 1,
          limit: 10
        }
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockAdminServiceInstance.getTransactions.mockResolvedValue(mockTransactions);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockTransactions
      });
      expect(mockAdminServiceInstance.getTransactions).toHaveBeenCalledWith({});
      expect(mockDatabaseConfig.connect).toHaveBeenCalled();
    });

    it('should handle query parameters', async () => {
      const mockUser = { 
        userId: 'admin123', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      };
      const mockTransactions = {
        transactions: [],
        pagination: {
          total: 0,
          pages: 0,
          currentPage: 1,
          limit: 20
        }
      };

      mockEvent.queryStringParameters = {
        page: '2',
        limit: '20',
        status: 'COMPLETED',
        type: 'TRANSFER'
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockAdminServiceInstance.getTransactions.mockResolvedValue(mockTransactions);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(mockAdminServiceInstance.getTransactions).toHaveBeenCalledWith({
        page: '2',
        limit: '20',
        status: 'COMPLETED',
        type: 'TRANSFER'
      });
    });
  });

  describe('authentication failures', () => {
    it('should return 500 when authentication fails', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Missing or invalid authorization header');
    });
  });

  describe('service errors', () => {
    it('should handle admin service errors', async () => {
      const mockUser = { 
        userId: 'admin123', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockAdminServiceInstance.getTransactions.mockRejectedValue(new Error('Database connection failed'));

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Database connection failed');
    });
  });
});
