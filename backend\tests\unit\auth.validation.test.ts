import { validateRegister } from '../../src/common/validation/auth.validation';

describe('Auth Validation', () => {
  describe('validateRegister', () => {
    const validRegisterData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+2348123456789',
      password: 'StrongPassword123!',
      dateOfBirth: '1990-01-01',
      agreeToTerms: true
    };

    it('should validate correct registration data', () => {
      const { error } = validateRegister(validRegisterData);
      expect(error).toBeUndefined();
    });

    it('should reject missing required fields', () => {
      const invalidData = { email: '<EMAIL>' };
      const { error } = validateRegister(invalidData);
      
      expect(error).toBeDefined();
      expect(error?.details).toBeDefined();
      expect(error?.details.length).toBeGreaterThan(0);
    });

    it('should reject invalid email format', () => {
      const invalidData = {
        ...validRegisterData,
        email: 'invalid-email'
      };
      const { error } = validateRegister(invalidData);
      
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('email');
    });

    it('should reject weak passwords', () => {
      const invalidData = {
        ...validRegisterData,
        password: '123'
      };
      const { error } = validateRegister(invalidData);
      
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('password');
    });    it('should reject invalid phone number format', () => {
      const invalidData = {
        ...validRegisterData,
        phone: '123'
      };
      const { error } = validateRegister(invalidData);
      
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('phone');
    });

    it('should reject empty strings for required fields', () => {
      const invalidData = {
        ...validRegisterData,
        firstName: '',
        lastName: ''
      };
      const { error } = validateRegister(invalidData);
      
      expect(error).toBeDefined();
    });    it('should handle null and undefined values', () => {
      const invalidData = {
        firstName: null,
        lastName: undefined,
        email: '<EMAIL>',
        password: 'ValidPassword123!',
        phone: '+2348123456789'
      };
      const { error } = validateRegister(invalidData);
      
      expect(error).toBeDefined();
    });

    it('should reject extremely long field values', () => {
      const invalidData = {
        ...validRegisterData,
        firstName: 'A'.repeat(1000)
      };
      const { error } = validateRegister(invalidData);
      
      expect(error).toBeDefined();
    });    it('should handle additional properties', () => {
      const dataWithExtra = {
        ...validRegisterData,
        extraField: 'should be ignored'
      };
      const { error } = validateRegister(dataWithExtra);
      
      // Joi will reject extra fields by default unless allowUnknown is set
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('not allowed');
    });
  });
});
