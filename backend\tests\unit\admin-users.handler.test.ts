import { APIGatewayProxyEvent } from 'aws-lambda';
import { main } from '../../src/api/v1/admin/users.handler';
import { AdminService } from '../../src/services/admin.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';
import { DatabaseConfig } from '../../src/config/database';

// Mock dependencies
jest.mock('../../src/services/admin.service');
jest.mock('../../src/common/middlewares/auth.middleware');
jest.mock('../../src/config/database');

describe('Admin Users Handler', () => {
  let mockEvent: APIGatewayProxyEvent;
  let mockAdminServiceInstance: jest.Mocked<AdminService>;
  let mockAuthMiddlewareInstance: jest.Mocked<AuthMiddleware>;
  let mockDatabaseConfig: jest.Mocked<DatabaseConfig>;

  beforeEach(() => {
    mockEvent = {
      queryStringParameters: null,
      headers: { Authorization: 'Bearer admin-token' }
    } as APIGatewayProxyEvent;

    mockAdminServiceInstance = {
      getUsers: jest.fn(),
      getDashboardStats: jest.fn(),
      getTransactions: jest.fn(),
      updateUserStatus: jest.fn(),
      approveKYC: jest.fn(),
      rejectKYC: jest.fn(),
      getUserDetails: jest.fn()
    } as jest.Mocked<AdminService>;

    mockAuthMiddlewareInstance = {
      authenticate: jest.fn()
    } as jest.Mocked<AuthMiddleware>;

    mockDatabaseConfig = {
      connect: jest.fn()
    } as jest.Mocked<DatabaseConfig>;

    (AdminService as jest.MockedClass<typeof AdminService>).mockImplementation(() => mockAdminServiceInstance);
    (AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>).mockImplementation(() => mockAuthMiddlewareInstance);
    (DatabaseConfig.getInstance as jest.Mock).mockReturnValue(mockDatabaseConfig);

    jest.clearAllMocks();
  });

  describe('successful users retrieval', () => {
    it('should return admin users successfully', async () => {
      const mockUser = { 
        userId: 'admin123', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      };
      const mockUsers = {
        users: [
          {
            id: 'user1',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            status: 'ACTIVE',
            createdAt: '2024-01-01T00:00:00.000Z'
          },
          {
            id: 'user2',
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            status: 'SUSPENDED',
            createdAt: '2024-01-02T00:00:00.000Z'
          }
        ],
        pagination: {
          total: 2,
          pages: 1,
          currentPage: 1,
          limit: 10
        }
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockAdminServiceInstance.getUsers.mockResolvedValue(mockUsers);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockUsers
      });
      expect(mockAdminServiceInstance.getUsers).toHaveBeenCalledWith({});
      expect(mockDatabaseConfig.connect).toHaveBeenCalled();
    });

    it('should handle query parameters', async () => {
      const mockUser = { 
        userId: 'admin123', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      };
      const mockUsers = {
        users: [],
        pagination: {
          total: 0,
          pages: 0,
          currentPage: 1,
          limit: 20
        }
      };

      mockEvent.queryStringParameters = {
        page: '2',
        limit: '20',
        search: 'john',
        status: 'ACTIVE'
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockAdminServiceInstance.getUsers.mockResolvedValue(mockUsers);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(mockAdminServiceInstance.getUsers).toHaveBeenCalledWith({
        page: '2',
        limit: '20',
        search: 'john',
        status: 'ACTIVE'
      });
    });
  });

  describe('authentication failures', () => {
    it('should return 500 when authentication fails', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Missing or invalid authorization header');
    });

    it('should return 500 for invalid token', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Invalid or expired token');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Invalid or expired token');
    });
  });

  describe('service errors', () => {
    it('should handle admin service errors', async () => {
      const mockUser = { 
        userId: 'admin123', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockAdminServiceInstance.getUsers.mockRejectedValue(new Error('Database connection failed'));

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Database connection failed');
    });

    it('should handle database connection errors', async () => {
      mockDatabaseConfig.connect.mockRejectedValue(new Error('Database connection failed'));

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Database connection failed');
    });
  });

  describe('edge cases', () => {
    it('should handle missing Authorization header', async () => {
      mockEvent.headers = {};

      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Missing or invalid authorization header');
    });

    it('should handle malformed Authorization header', async () => {
      mockEvent.headers = { Authorization: 'InvalidFormat' };

      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Malformed authorization header');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Malformed authorization header');
    });
  });
});
