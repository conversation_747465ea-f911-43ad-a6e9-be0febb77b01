import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { AuthService } from '../../../services/auth.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { DatabaseConfig } from '../../../config/database';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Ensure database connection
    await DatabaseConfig.getInstance().connect();
    
    const refreshToken = event.headers['Authorization'] || event.headers['authorization'] || '';
    if (!refreshToken) {
      return errorResponse(401, 'NO_REFRESH_TOKEN', 'Refresh token is required');
    }
    const authService = new AuthService();
    const result = await authService.refresh(refreshToken.replace('Bearer ', ''));
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: result })
    };
  } catch (err: any) {
    logger.error('Token refresh error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during token refresh');
  }
};
