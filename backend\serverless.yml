service: zapwallet-backend
frameworkVersion: '3'
provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  stage: dev
  environment:
    NODE_ENV: production
    DB_HOST: ${env:DB_HOST}
    DB_USER: ${env:DB_USER}
    DB_PASS: ${env:DB_PASS}
    DB_NAME: ${env:DB_NAME}
    REDIS_URL: ${env:REDIS_URL}
    JWT_SECRET: ${env:JWT_SECRET}
    JWT_REFRESH_SECRET: ${env:JWT_REFRESH_SECRET}
    LOG_LEVEL: info
functions:
  register:
    handler: src/api/v1/auth/register.handler.main
    events:
      - http:
          path: api/v1/auth/register
          method: post
          cors: true
  login:
    handler: src/api/v1/auth/login.handler.main
    events:
      - http:
          path: api/v1/auth/login
          method: post
          cors: true
  refresh:
    handler: src/api/v1/auth/refresh.handler.main
    events:
      - http:
          path: api/v1/auth/refresh
          method: post
          cors: true

  # User Management endpoints
  getUserProfile:
    handler: src/api/v1/users/profile.handler.main
    events:
      - http:
          path: api/v1/users/profile
          method: get
          cors: true
  updateUserProfile:
    handler: src/api/v1/users/updateProfile.handler.main
    events:
      - http:
          path: api/v1/users/profile
          method: put
          cors: true
  kycVerification:
    handler: src/api/v1/users/kyc.handler.main
    events:
      - http:
          path: api/v1/users/kyc/verify
          method: post
          cors: true

  # Wallet Operations
  getWalletBalance:
    handler: src/api/v1/wallets/balance.handler.main
    events:
      - http:
          path: api/v1/wallets/balance
          method: get
          cors: true
  fundWallet:
    handler: src/api/v1/wallets/fund.handler.main
    events:
      - http:
          path: api/v1/wallets/fund
          method: post
          cors: true
  withdrawFromWallet:
    handler: src/api/v1/wallets/withdraw.handler.main
    events:
      - http:
          path: api/v1/wallets/withdraw
          method: post
          cors: true

  # Transaction Management
  getTransactions:
    handler: src/api/v1/transactions/list.handler.main
    events:
      - http:
          path: api/v1/transactions
          method: get
          cors: true
  getTransactionById:
    handler: src/api/v1/transactions/getById.handler.main
    events:
      - http:
          path: api/v1/transactions/{id}
          method: get
          cors: true
  getTransactionStats:
    handler: src/api/v1/transactions/stats.handler.main
    events:
      - http:
          path: api/v1/transactions/stats
          method: get
          cors: true

  # Transfer Operations
  validateRecipient:
    handler: src/api/v1/transfers/validateRecipient.handler.main
    events:
      - http:
          path: api/v1/transfers/validate-recipient
          method: post
          cors: true
  initiateTransfer:
    handler: src/api/v1/transfers/transfer.handler.main
    events:
      - http:
          path: api/v1/transfers
          method: post
          cors: true

  # Bill Payment Services
  getBillCategories:
    handler: src/api/v1/bills/categories.handler.main
    events:
      - http:
          path: api/v1/bills/categories
          method: get
          cors: true
  getBillProviders:
    handler: src/api/v1/bills/providers.handler.main
    events:
      - http:
          path: api/v1/bills/providers/{categoryId}
          method: get
          cors: true
  validateBill:
    handler: src/api/v1/bills/validate.handler.main
    events:
      - http:
          path: api/v1/bills/validate
          method: post
          cors: true
  payBill:
    handler: src/api/v1/bills/pay.handler.main
    events:
      - http:
          path: api/v1/bills/pay
          method: post
          cors: true

  # Notifications
  getNotifications:
    handler: src/api/v1/notifications/list.handler.main
    events:
      - http:
          path: api/v1/notifications
          method: get
          cors: true
  markNotificationRead:
    handler: src/api/v1/notifications/markRead.handler.main
    events:
      - http:
          path: api/v1/notifications/{id}/read
          method: put
          cors: true
  markAllNotificationsRead:
    handler: src/api/v1/notifications/markAllRead.handler.main
    events:
      - http:
          path: api/v1/notifications/mark-all-read
          method: post
          cors: true

  # Admin APIs
  getAdminStats:
    handler: src/api/v1/admin/stats.handler.main
    events:
      - http:
          path: api/v1/admin/dashboard/stats
          method: get
          cors: true
  getAdminUsers:
    handler: src/api/v1/admin/users.handler.main
    events:
      - http:
          path: api/v1/admin/users
          method: get
          cors: true
  getAdminTransactions:
    handler: src/api/v1/admin/transactions.handler.main
    events:
      - http:
          path: api/v1/admin/transactions
          method: get
          cors: true
