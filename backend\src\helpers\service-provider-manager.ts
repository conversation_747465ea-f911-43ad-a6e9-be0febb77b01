import { TransferApiService } from './transfer-api.service';
import { BillPaymentApiService } from './bill-payment-api.service';
import { ApiHelperService } from './api-helper.service';
import {
  ServiceProvider,
  ProviderCapability,
  ApiConfig,
  ApiResponse
} from '../interfaces/common';
import { logger } from '../common/logging/logger';

/**
 * Service Provider Manager
 * Manages multiple third-party service providers for transfers and bill payments
 * Provides unified interface with automatic failover and load balancing
 */
export class ServiceProviderManager {
  private transferService: TransferApiService;
  private billPaymentService: BillPaymentApiService;
  private providers: Map<string, ServiceProvider> = new Map();
  private healthChecks: Map<string, boolean> = new Map();
  private lastHealthCheck: Map<string, number> = new Map();
  private healthCheckInterval = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.transferService = new TransferApiService();
    this.billPaymentService = new BillPaymentApiService();
    this.initializeProviders();
    this.startHealthChecking();
  }

  /**
   * Initialize service providers with their capabilities
   */
  private initializeProviders(): void {
    const providers: ServiceProvider[] = [
      {
        id: 'flutterwave',
        name: 'Flutterwave',
        type: 'TRANSFER',
        config: {
          baseUrl: process.env.FLUTTERWAVE_BASE_URL || 'https://api.flutterwave.com/v3',
          apiKey: process.env.FLUTTERWAVE_SECRET_KEY || '',
          timeout: 30000
        },
        isActive: true,
        priority: 1
      },
      {
        id: 'paystack',
        name: 'Paystack',
        type: 'TRANSFER',
        config: {
          baseUrl: process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co',
          apiKey: process.env.PAYSTACK_SECRET_KEY || '',
          timeout: 30000
        },
        isActive: true,
        priority: 2
      },
      {
        id: 'vtpass',
        name: 'VTPass',
        type: 'BILL_PAYMENT',
        config: {
          baseUrl: process.env.VTPASS_BASE_URL || 'https://vtpass.com/api',
          apiKey: process.env.VTPASS_API_KEY || '',
          secretKey: process.env.VTPASS_SECRET_KEY || '',
          timeout: 30000
        },
        isActive: true,
        priority: 1
      },
      {
        id: 'baxi',
        name: 'Baxi',
        type: 'BILL_PAYMENT',
        config: {
          baseUrl: process.env.BAXI_BASE_URL || 'https://payments.baxipay.com.ng/api/baxipay',
          apiKey: process.env.BAXI_API_KEY || '',
          secretKey: process.env.BAXI_SECRET_KEY || '',
          timeout: 30000
        },
        isActive: true,
        priority: 2
      }
    ];

    providers.forEach(provider => {
      if (provider.config.apiKey) {
        this.providers.set(provider.id, provider);
        this.healthChecks.set(provider.id, true);
        this.lastHealthCheck.set(provider.id, Date.now());
      }
    });

    logger.info('Service providers initialized', {
      providers: Array.from(this.providers.keys())
    });
  }

  /**
   * Get provider capabilities
   */
  getProviderCapabilities(providerId: string): ProviderCapability | null {
    const capabilities: Record<string, ProviderCapability> = {
      flutterwave: {
        transfers: {
          bankTransfer: true,
          walletTransfer: true,
          internationalTransfer: true
        },
        bills: {
          airtime: true,
          data: true,
          electricity: true,
          cableTv: true,
          internet: true
        }
      },
      paystack: {
        transfers: {
          bankTransfer: true,
          walletTransfer: false,
          internationalTransfer: false
        },
        bills: {
          airtime: true,
          data: true,
          electricity: true,
          cableTv: true,
          internet: false
        }
      },
      vtpass: {
        transfers: {
          bankTransfer: false,
          walletTransfer: false,
          internationalTransfer: false
        },
        bills: {
          airtime: true,
          data: true,
          electricity: true,
          cableTv: true,
          internet: true
        }
      },
      baxi: {
        transfers: {
          bankTransfer: false,
          walletTransfer: false,
          internationalTransfer: false
        },
        bills: {
          airtime: true,
          data: true,
          electricity: true,
          cableTv: true,
          internet: true
        }
      }
    };

    return capabilities[providerId] || null;
  }

  /**
   * Get optimal provider for a specific service type
   */
  getOptimalProvider(serviceType: 'TRANSFER' | 'BILL_PAYMENT', capability?: string): ServiceProvider | null {
    const eligibleProviders = Array.from(this.providers.values())
      .filter(provider => {
        // Check service type
        if (provider.type !== serviceType && serviceType !== 'BILL_PAYMENT') return false;
        
        // Check if provider is active and healthy
        if (!provider.isActive || !this.healthChecks.get(provider.id)) return false;
        
        // Check specific capability if provided
        if (capability) {
          const capabilities = this.getProviderCapabilities(provider.id);
          if (!capabilities) return false;
          
          if (serviceType === 'TRANSFER') {
            return capabilities.transfers[capability as keyof typeof capabilities.transfers];
          } else {
            return capabilities.bills[capability as keyof typeof capabilities.bills];
          }
        }
        
        return true;
      })
      .sort((a, b) => {
        // Sort by priority (lower number = higher priority)
        return a.priority - b.priority;
      });

    return eligibleProviders[0] || null;
  }

  /**
   * Get transfer service instance
   */
  getTransferService(): TransferApiService {
    return this.transferService;
  }

  /**
   * Get bill payment service instance
   */
  getBillPaymentService(): BillPaymentApiService {
    return this.billPaymentService;
  }

  /**
   * Add new service provider
   */
  addProvider(provider: ServiceProvider): void {
    this.providers.set(provider.id, provider);
    this.healthChecks.set(provider.id, true);
    this.lastHealthCheck.set(provider.id, Date.now());

    // Add to appropriate service
    if (provider.type === 'TRANSFER') {
      this.transferService.addProvider(provider.id, provider.config);
    } else {
      this.billPaymentService.addProvider(provider.id, provider.config);
    }

    logger.info('Provider added', { providerId: provider.id, type: provider.type });
  }

  /**
   * Remove service provider
   */
  removeProvider(providerId: string): boolean {
    const provider = this.providers.get(providerId);
    if (!provider) return false;

    this.providers.delete(providerId);
    this.healthChecks.delete(providerId);
    this.lastHealthCheck.delete(providerId);

    // Remove from appropriate service
    if (provider.type === 'TRANSFER') {
      this.transferService.removeProvider(providerId);
    } else {
      this.billPaymentService.removeProvider(providerId);
    }

    logger.info('Provider removed', { providerId });
    return true;
  }

  /**
   * Update provider configuration
   */
  updateProviderConfig(providerId: string, config: Partial<ApiConfig>): boolean {
    const provider = this.providers.get(providerId);
    if (!provider) return false;

    provider.config = { ...provider.config, ...config };
    this.providers.set(providerId, provider);

    logger.info('Provider config updated', { providerId });
    return true;
  }

  /**
   * Enable/disable provider
   */
  setProviderStatus(providerId: string, isActive: boolean): boolean {
    const provider = this.providers.get(providerId);
    if (!provider) return false;

    provider.isActive = isActive;
    this.providers.set(providerId, provider);

    logger.info('Provider status updated', { providerId, isActive });
    return true;
  }

  /**
   * Get all providers with their status
   */
  getAllProviders(): Array<ServiceProvider & { isHealthy: boolean; lastHealthCheck: number }> {
    return Array.from(this.providers.values()).map(provider => ({
      ...provider,
      isHealthy: this.healthChecks.get(provider.id) || false,
      lastHealthCheck: this.lastHealthCheck.get(provider.id) || 0
    }));
  }

  /**
   * Get provider statistics
   */
  getProviderStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    this.providers.forEach((provider, id) => {
      stats[id] = {
        name: provider.name,
        type: provider.type,
        isActive: provider.isActive,
        isHealthy: this.healthChecks.get(id),
        priority: provider.priority,
        lastHealthCheck: this.lastHealthCheck.get(id),
        capabilities: this.getProviderCapabilities(id)
      };
    });

    return stats;
  }

  /**
   * Start periodic health checking
   */
  private startHealthChecking(): void {
    setInterval(async () => {
      await this.performHealthChecks();
    }, this.healthCheckInterval);

    // Initial health check
    this.performHealthChecks();
  }

  /**
   * Perform health checks on all providers
   */
  private async performHealthChecks(): Promise<void> {
    const healthCheckPromises = Array.from(this.providers.entries()).map(
      async ([providerId, provider]) => {
        try {
          const helperService = new ApiHelperService(provider.config);
          const result = await helperService.healthCheck();
          
          this.healthChecks.set(providerId, result.success);
          this.lastHealthCheck.set(providerId, Date.now());
          
          if (!result.success) {
            logger.warn('Provider health check failed', { 
              providerId, 
              error: result.error 
            });
          }
        } catch (error: any) {
          this.healthChecks.set(providerId, false);
          this.lastHealthCheck.set(providerId, Date.now());
          
          logger.error('Provider health check error', { 
            providerId, 
            error: error.message 
          });
        }
      }
    );

    await Promise.allSettled(healthCheckPromises);
    
    const healthyProviders = Array.from(this.healthChecks.entries())
      .filter(([_, isHealthy]) => isHealthy)
      .map(([providerId]) => providerId);
    
    logger.info('Health check completed', { 
      total: this.providers.size,
      healthy: healthyProviders.length,
      healthyProviders
    });
  }

  /**
   * Force health check for specific provider
   */
  async checkProviderHealth(providerId: string): Promise<boolean> {
    const provider = this.providers.get(providerId);
    if (!provider) return false;

    try {
      const helperService = new ApiHelperService(provider.config);
      const result = await helperService.healthCheck();
      
      this.healthChecks.set(providerId, result.success);
      this.lastHealthCheck.set(providerId, Date.now());
      
      return result.success;
    } catch (error: any) {
      this.healthChecks.set(providerId, false);
      this.lastHealthCheck.set(providerId, Date.now());
      
      logger.error('Provider health check error', { 
        providerId, 
        error: error.message 
      });
      
      return false;
    }
  }
}
