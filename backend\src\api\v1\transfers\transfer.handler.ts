import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { TransferService } from '../../../services/transfer.service';
import { validateTransfer } from '../../../common/validation/transfer.validation';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const body = JSON.parse(event.body || '{}');
    const { error } = validateTransfer(body);
    if (error) {
      logger.warn('Validation failed', { details: error.details });
      return errorResponse(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    
    const transferService = new TransferService();
    const transferResult = await transferService.initiateTransfer(user.userId, body);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: transferResult })
    };
  } catch (err: any) {
    logger.error('Transfer error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during transfer');
  }
};
