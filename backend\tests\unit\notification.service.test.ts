import { NotificationService, Notification } from '../../src/services/notification.service';

describe('NotificationService', () => {
  let notificationService: NotificationService;

  beforeEach(() => {
    notificationService = new NotificationService();
    // Clear the static notifications array before each test
    (NotificationService as any).notifications = [];
  });

  describe('createNotification', () => {
    it('should create a notification successfully', async () => {
      const notificationData = {
        userId: 'user123',
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'SYSTEM' as const,
        priority: 'MEDIUM' as const
      };

      const notification = await notificationService.createNotification(notificationData);

      expect(notification).toBeDefined();
      expect(notification.id).toBeDefined();
      expect(notification.userId).toBe(notificationData.userId);
      expect(notification.title).toBe(notificationData.title);
      expect(notification.message).toBe(notificationData.message);
      expect(notification.type).toBe(notificationData.type);
      expect(notification.priority).toBe(notificationData.priority);
      expect(notification.read).toBe(false);
      expect(notification.createdAt).toBeDefined();
    });

    it('should create notification with additional data', async () => {
      const notificationData = {
        userId: 'user123',
        title: 'Transaction Alert',
        message: 'Transaction completed',
        type: 'TRANSACTION' as const,
        priority: 'HIGH' as const,
        data: { amount: 1000, reference: 'TXN123' }
      };

      const notification = await notificationService.createNotification(notificationData);

      expect(notification.data).toEqual(notificationData.data);
    });

    it('should generate unique IDs for different notifications', async () => {
      const notificationData1 = {
        userId: 'user123',
        title: 'Notification 1',
        message: 'First notification',
        type: 'SYSTEM' as const,
        priority: 'LOW' as const
      };

      const notificationData2 = {
        userId: 'user123',
        title: 'Notification 2',
        message: 'Second notification',
        type: 'SYSTEM' as const,
        priority: 'LOW' as const
      };

      const notification1 = await notificationService.createNotification(notificationData1);
      const notification2 = await notificationService.createNotification(notificationData2);

      expect(notification1.id).not.toBe(notification2.id);
    });
  });

  describe('createTransactionNotification', () => {
    it('should create transaction notification with correct format', async () => {
      const userId = 'user123';
      const transactionType = 'CREDIT';
      const amount = 5000;
      const reference = 'TXN123456';

      const notification = await notificationService.createTransactionNotification(
        userId, transactionType, amount, reference
      );

      expect(notification.userId).toBe(userId);
      expect(notification.title).toBe('Transaction CREDIT');
      expect(notification.message).toBe('Your credit of ₦5,000 has been processed. Reference: TXN123456');
      expect(notification.type).toBe('TRANSACTION');
      expect(notification.priority).toBe('MEDIUM');
      expect(notification.data).toEqual({
        transactionType,
        amount,
        reference
      });
    });

    it('should handle different transaction types', async () => {
      const debitNotification = await notificationService.createTransactionNotification(
        'user123', 'DEBIT', 2000, 'TXN001'
      );

      const creditNotification = await notificationService.createTransactionNotification(
        'user123', 'CREDIT', 3000, 'TXN002'
      );

      expect(debitNotification.title).toBe('Transaction DEBIT');
      expect(debitNotification.message).toContain('debit of ₦2,000');
      expect(creditNotification.title).toBe('Transaction CREDIT');
      expect(creditNotification.message).toContain('credit of ₦3,000');
    });

    it('should format amounts with commas', async () => {
      const notification = await notificationService.createTransactionNotification(
        'user123', 'CREDIT', 1000000, 'TXN789'
      );

      expect(notification.message).toContain('₦1,000,000');
    });
  });

  describe('createSecurityNotification', () => {
    it('should create security notification without device info', async () => {
      const userId = 'user123';
      const action = 'Login';

      const notification = await notificationService.createSecurityNotification(userId, action);

      expect(notification.userId).toBe(userId);
      expect(notification.title).toBe('Security Alert');
      expect(notification.message).toBe('Login detected on your account');
      expect(notification.type).toBe('SECURITY');
      expect(notification.priority).toBe('HIGH');
      expect(notification.data).toEqual({
        action,
        deviceInfo: undefined
      });
    });

    it('should create security notification with device info', async () => {
      const userId = 'user123';
      const action = 'Password Change';
      const deviceInfo = { platform: 'iOS', browser: 'Safari' };

      const notification = await notificationService.createSecurityNotification(
        userId, action, deviceInfo
      );

      expect(notification.message).toBe('Password Change detected on your account from iOS');
      expect(notification.data).toEqual({
        action,
        deviceInfo
      });
    });
  });

  describe('getNotifications', () => {
    beforeEach(async () => {
      // Create some test notifications
      await notificationService.createNotification({
        userId: 'user123',
        title: 'Notification 1',
        message: 'First notification',
        type: 'SYSTEM',
        priority: 'LOW'
      });

      await notificationService.createNotification({
        userId: 'user123',
        title: 'Notification 2',
        message: 'Second notification',
        type: 'TRANSACTION',
        priority: 'MEDIUM'
      });

      await notificationService.createNotification({
        userId: 'user456',
        title: 'Notification 3',
        message: 'Third notification',
        type: 'SECURITY',
        priority: 'HIGH'
      });

      // Mark one notification as read
      const notifications = (NotificationService as any).notifications;
      notifications[0].read = true;
      notifications[0].readAt = new Date().toISOString();
    });

    it('should return user notifications with pagination', async () => {
      const result = await notificationService.getNotifications('user123', { page: 1, limit: 10 });

      expect(result.notifications).toHaveLength(2);
      expect(result.notifications[0].userId).toBe('user123');
      expect(result.notifications[1].userId).toBe('user123');
      expect(result.pagination).toEqual({
        total: 2,
        pages: 1,
        currentPage: 1,
        limit: 10
      });
      expect(result.unreadCount).toBe(1);
    });

    it('should filter by read status', async () => {
      const unreadResult = await notificationService.getNotifications('user123', { read: 'false' });
      const readResult = await notificationService.getNotifications('user123', { read: 'true' });

      expect(unreadResult.notifications).toHaveLength(1);
      expect(unreadResult.notifications[0].read).toBe(false);
      expect(readResult.notifications).toHaveLength(1);
      expect(readResult.notifications[0].read).toBe(true);
    });

    it('should handle pagination correctly', async () => {
      const result = await notificationService.getNotifications('user123', { page: 1, limit: 1 });

      expect(result.notifications).toHaveLength(1);
      expect(result.pagination).toEqual({
        total: 2,
        pages: 2,
        currentPage: 1,
        limit: 1
      });
    });

    it('should sort notifications by creation date (newest first)', async () => {
      const result = await notificationService.getNotifications('user123', {});

      const dates = result.notifications.map(n => new Date(n.createdAt).getTime());
      expect(dates[0]).toBeGreaterThanOrEqual(dates[1]);
    });

    it('should return empty array for user with no notifications', async () => {
      const result = await notificationService.getNotifications('nonexistent', {});

      expect(result.notifications).toHaveLength(0);
      expect(result.unreadCount).toBe(0);
      expect(result.pagination.total).toBe(0);
    });

    it('should use default pagination values', async () => {
      const result = await notificationService.getNotifications('user123', {});

      expect(result.pagination.currentPage).toBe(1);
      expect(result.pagination.limit).toBe(20);
    });
  });

  describe('markAsRead', () => {
    let notificationId: string;

    beforeEach(async () => {
      const notification = await notificationService.createNotification({
        userId: 'user123',
        title: 'Test Notification',
        message: 'Test message',
        type: 'SYSTEM',
        priority: 'LOW'
      });
      notificationId = notification.id;
    });

    it('should mark notification as read', async () => {
      const updatedNotification = await notificationService.markAsRead('user123', notificationId);

      expect(updatedNotification.read).toBe(true);
      expect(updatedNotification.readAt).toBeDefined();
    });

    it('should not change already read notification', async () => {
      // Mark as read first time
      await notificationService.markAsRead('user123', notificationId);
      const firstReadAt = (NotificationService as any).notifications[0].readAt;

      // Mark as read second time
      const result = await notificationService.markAsRead('user123', notificationId);

      expect(result.readAt).toBe(firstReadAt);
    });

    it('should throw error for non-existent notification', async () => {
      await expect(
        notificationService.markAsRead('user123', 'nonexistent')
      ).rejects.toThrow('Notification not found');
    });

    it('should throw error when accessing other user notification', async () => {
      await expect(
        notificationService.markAsRead('user456', notificationId)
      ).rejects.toThrow('Notification not found');
    });
  });

  describe('markAllAsRead', () => {
    beforeEach(async () => {
      // Create multiple notifications for user
      await notificationService.createNotification({
        userId: 'user123',
        title: 'Notification 1',
        message: 'Test message 1',
        type: 'SYSTEM',
        priority: 'LOW'
      });

      await notificationService.createNotification({
        userId: 'user123',
        title: 'Notification 2',
        message: 'Test message 2',
        type: 'TRANSACTION',
        priority: 'MEDIUM'
      });

      await notificationService.createNotification({
        userId: 'user456',
        title: 'Notification 3',
        message: 'Test message 3',
        type: 'SECURITY',
        priority: 'HIGH'
      });
    });

    it('should mark all user notifications as read', async () => {
      const result = await notificationService.markAllAsRead('user123');

      expect(result.markedCount).toBe(2);
      expect(result.message).toBe('2 notifications marked as read');

      // Verify notifications are marked as read
      const userNotifications = (NotificationService as any).notifications
        .filter((n: Notification) => n.userId === 'user123');
      
      userNotifications.forEach((notification: Notification) => {
        expect(notification.read).toBe(true);
        expect(notification.readAt).toBeDefined();
      });
    });

    it('should not affect other users notifications', async () => {
      await notificationService.markAllAsRead('user123');

      const otherUserNotification = (NotificationService as any).notifications
        .find((n: Notification) => n.userId === 'user456');
      
      expect(otherUserNotification.read).toBe(false);
      expect(otherUserNotification.readAt).toBeUndefined();
    });

    it('should return 0 when user has no unread notifications', async () => {
      await notificationService.markAllAsRead('user123');
      const result = await notificationService.markAllAsRead('user123');

      expect(result.markedCount).toBe(0);
      expect(result.message).toBe('0 notifications marked as read');
    });

    it('should handle user with no notifications', async () => {
      const result = await notificationService.markAllAsRead('nonexistent');

      expect(result.markedCount).toBe(0);
      expect(result.message).toBe('0 notifications marked as read');
    });
  });
});
