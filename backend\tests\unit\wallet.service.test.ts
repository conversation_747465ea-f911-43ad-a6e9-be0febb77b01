import { Types } from 'mongoose';
import { WalletService } from '../../src/services/wallet.service';

describe('WalletService', () => {
  let walletService: WalletService;

  beforeEach(() => {
    walletService = new WalletService();
    // Clear static wallets array before each test
    (WalletService as any).wallets = [];
  });

  describe('getBalance', () => {
    it('should create and return default wallet for new user', async () => {
      const userId = new Types.ObjectId().toString();

      const result = await walletService.getBalance(userId);

      expect(result).toBeDefined();
      expect(result.available).toBe(0);
      expect(result.pending).toBe(0);
      expect(result.currency).toBe('NGN');
      expect(result.limits).toBeDefined();
      expect(result.limits.dailyTransfer).toBe(1000000);
      expect(result.limits.dailyTransferUsed).toBe(0);
      expect(result.limits.singleTransferMax).toBe(500000);
    });

    it('should return existing wallet balance', async () => {
      const userId = new Types.ObjectId().toString();

      // First call creates the wallet
      await walletService.getBalance(userId);
      
      // Manually update the wallet for testing
      const wallets = (WalletService as any).wallets;
      if (wallets.length > 0) {
        wallets[0].available = 1500;
        wallets[0].pending = 300;
      }

      // Second call should return updated balance
      const result = await walletService.getBalance(userId);

      expect(result.available).toBe(1500);
      expect(result.pending).toBe(300);
    });

    it('should create separate wallets for different users', async () => {
      const userId1 = new Types.ObjectId().toString();
      const userId2 = new Types.ObjectId().toString();

      const result1 = await walletService.getBalance(userId1);
      const result2 = await walletService.getBalance(userId2);

      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
      
      const wallets = (WalletService as any).wallets;
      expect(wallets.length).toBe(2);
      expect(wallets[0].userId).toBe(userId1);
      expect(wallets[1].userId).toBe(userId2);
    });
  });

  describe('fundWallet', () => {
    it('should initiate wallet funding successfully', async () => {
      const userId = new Types.ObjectId().toString();
      const fundingData = {
        amount: 5000,
        paymentMethod: 'CARD'
      };

      const result = await walletService.fundWallet(userId, fundingData);

      expect(result).toBeDefined();
      expect(result.transactionId).toBeDefined();
      expect(result.status).toBe('PENDING');
      expect(result.amount).toBe(5000);
      expect(result.paymentMethod).toBe('CARD');
      expect(result.message).toBe('Funding initiated successfully. Please complete payment.');
    });

    it('should generate unique transaction IDs', async () => {
      const userId = new Types.ObjectId().toString();
      const fundingData = {
        amount: 1000,
        paymentMethod: 'BANK_TRANSFER'
      };

      const result1 = await walletService.fundWallet(userId, fundingData);
      const result2 = await walletService.fundWallet(userId, fundingData);

      expect(result1.transactionId).toBeDefined();
      expect(result2.transactionId).toBeDefined();
      expect(result1.transactionId).not.toBe(result2.transactionId);
    });

    it('should handle different payment methods', async () => {
      const userId = new Types.ObjectId().toString();
      
      const cardFunding = {
        amount: 2000,
        paymentMethod: 'CARD'
      };
      
      const bankFunding = {
        amount: 3000,
        paymentMethod: 'BANK_TRANSFER'
      };

      const cardResult = await walletService.fundWallet(userId, cardFunding);
      const bankResult = await walletService.fundWallet(userId, bankFunding);

      expect(cardResult.paymentMethod).toBe('CARD');
      expect(cardResult.amount).toBe(2000);
      expect(bankResult.paymentMethod).toBe('BANK_TRANSFER');
      expect(bankResult.amount).toBe(3000);
    });
  });

  describe('withdrawFromWallet', () => {
    it('should initiate wallet withdrawal successfully', async () => {
      const userId = new Types.ObjectId().toString();
      const withdrawalData = {
        amount: 2000,
        bankCode: '058',
        accountNumber: '**********'
      };

      const result = await walletService.withdrawFromWallet(userId, withdrawalData);

      expect(result).toBeDefined();
      expect(result.transactionId).toBeDefined();
      expect(result.status).toBe('PENDING');
      expect(result.amount).toBe(2000);
      expect(result.bankCode).toBe('058');
      expect(result.accountNumber).toBe('**********');
      expect(result.message).toBe('Withdrawal initiated successfully. Processing may take 1-3 business days.');
    });

    it('should generate unique transaction IDs for withdrawals', async () => {
      const userId = new Types.ObjectId().toString();
      const withdrawalData = {
        amount: 1000,
        bankCode: '044',
        accountNumber: '**********'
      };

      const result1 = await walletService.withdrawFromWallet(userId, withdrawalData);
      const result2 = await walletService.withdrawFromWallet(userId, withdrawalData);

      expect(result1.transactionId).toBeDefined();
      expect(result2.transactionId).toBeDefined();
      expect(result1.transactionId).not.toBe(result2.transactionId);
    });

    it('should handle different bank details', async () => {
      const userId = new Types.ObjectId().toString();
      
      const withdrawal1 = {
        amount: 1500,
        bankCode: '058',
        accountNumber: '**********'
      };
      
      const withdrawal2 = {
        amount: 2500,
        bankCode: '044',
        accountNumber: '**********'
      };

      const result1 = await walletService.withdrawFromWallet(userId, withdrawal1);
      const result2 = await walletService.withdrawFromWallet(userId, withdrawal2);

      expect(result1.bankCode).toBe('058');
      expect(result1.accountNumber).toBe('**********');
      expect(result1.amount).toBe(1500);
      
      expect(result2.bankCode).toBe('044');
      expect(result2.accountNumber).toBe('**********');
      expect(result2.amount).toBe(2500);
    });
  });

  describe('createDefaultWallet', () => {
    it('should create wallet with correct default values', async () => {
      const userId = new Types.ObjectId().toString();

      // Trigger wallet creation by getting balance
      await walletService.getBalance(userId);

      const wallets = (WalletService as any).wallets;
      expect(wallets.length).toBe(1);
      
      const wallet = wallets[0];
      expect(wallet.id).toBeDefined();
      expect(wallet.userId).toBe(userId);
      expect(wallet.available).toBe(0);
      expect(wallet.pending).toBe(0);
      expect(wallet.currency).toBe('NGN');
      expect(wallet.limits.dailyTransfer).toBe(1000000);
      expect(wallet.limits.dailyTransferUsed).toBe(0);
      expect(wallet.limits.singleTransferMax).toBe(500000);
      expect(wallet.createdAt).toBeDefined();
      expect(wallet.updatedAt).toBeDefined();
    });

    it('should create unique IDs for different wallets', async () => {
      const userId1 = new Types.ObjectId().toString();
      const userId2 = new Types.ObjectId().toString();

      await walletService.getBalance(userId1);
      await walletService.getBalance(userId2);

      const wallets = (WalletService as any).wallets;
      expect(wallets.length).toBe(2);
      expect(wallets[0].id).not.toBe(wallets[1].id);
      expect(wallets[0].userId).not.toBe(wallets[1].userId);
    });
  });
});
