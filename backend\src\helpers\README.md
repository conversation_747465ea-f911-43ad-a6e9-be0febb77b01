# Third-Party API Helper Services Configuration

## Environment Variables

Create a `.env` file in the backend directory with the following configuration:

```env
# Flutterwave Configuration
FLUTTERWAVE_BASE_URL=https://api.flutterwave.com/v3
FLUTTERWAVE_PUBLIC_KEY=your_flutterwave_public_key
FLUTTERWAVE_SECRET_KEY=your_flutterwave_secret_key

# Paystack Configuration
PAYSTACK_BASE_URL=https://api.paystack.co
PAYSTACK_PUBLIC_KEY=your_paystack_public_key
PAYSTACK_SECRET_KEY=your_paystack_secret_key

# VTPass Configuration
VTPASS_BASE_URL=https://vtpass.com/api
VTPASS_API_KEY=your_vtpass_api_key
VTPASS_SECRET_KEY=your_vtpass_secret_key
VTPASS_USERNAME=your_vtpass_username
VTPASS_PASSWORD=your_vtpass_password

# Baxi Configuration
BAXI_BASE_URL=https://payments.baxipay.com.ng/api/baxipay
BAXI_API_KEY=your_baxi_api_key
BAXI_SECRET_KEY=your_baxi_secret_key

# Monnify Configuration
MONNIFY_BASE_URL=https://api.monnify.com/api/v1
MONNIFY_API_KEY=your_monnify_api_key
MONNIFY_SECRET_KEY=your_monnify_secret_key
MONNIFY_CONTRACT_CODE=your_monnify_contract_code
```

## Usage Examples

### 1. Basic Setup

```typescript
import { 
  getServiceProviderManager, 
  getTransferService, 
  getBillPaymentService 
} from '../helpers';

// Get the service provider manager
const manager = getServiceProviderManager();

// Or get services directly
const transferService = getTransferService();
const billPaymentService = getBillPaymentService();
```

### 2. Bank Transfer

```typescript
import { getTransferService } from '../helpers';

const transferService = getTransferService();

// Validate bank account
const validationResult = await transferService.validateBankAccount({
  accountNumber: '**********',
  bankCode: '044' // Access Bank
});

if (validationResult.success) {
  console.log('Account Name:', validationResult.data?.accountName);
  
  // Initiate transfer
  const transferResult = await transferService.initiateTransfer({
    amount: 10000, // ₦100.00
    recipientType: 'BANK',
    recipientAccount: '**********',
    recipientBank: '044',
    narration: 'Payment for services',
    reference: 'TXN_' + Date.now(),
    currency: 'NGN'
  });
  
  if (transferResult.success) {
    console.log('Transfer Status:', transferResult.data?.status);
    console.log('Reference:', transferResult.data?.reference);
  }
}
```

### 3. Bill Payment

```typescript
import { getBillPaymentService } from '../helpers';

const billService = getBillPaymentService();

// Get bill categories
const categories = await billService.getBillCategories();

// Get billers for electricity
const billers = await billService.getBillers('electricity');

// Validate customer
const customerValidation = await billService.validateCustomer({
  billerId: 'eko-electricity',
  customerCode: '**********'
});

if (customerValidation.success) {
  console.log('Customer Name:', customerValidation.data?.customerName);
  
  // Pay bill
  const paymentResult = await billService.payBill({
    billerId: 'eko-electricity',
    customerCode: '**********',
    amount: 5000, // ₦50.00
    reference: 'BILL_' + Date.now()
  });
  
  if (paymentResult.success) {
    console.log('Payment Status:', paymentResult.data?.status);
    console.log('Token:', paymentResult.data?.token);
  }
}
```

### 4. Provider Management

```typescript
import { getServiceProviderManager } from '../helpers';

const manager = getServiceProviderManager();

// Get all providers with status
const providers = manager.getAllProviders();
console.log('Available Providers:', providers);

// Get provider statistics
const stats = manager.getProviderStats();
console.log('Provider Stats:', stats);

// Set active provider for transfers
const transferService = manager.getTransferService();
transferService.setActiveProvider('paystack');

// Check provider health
const isHealthy = await manager.checkProviderHealth('flutterwave');
console.log('Flutterwave Health:', isHealthy);

// Add custom provider
manager.addProvider({
  id: 'custom-provider',
  name: 'Custom Provider',
  type: 'TRANSFER',
  config: {
    baseUrl: 'https://api.custom-provider.com',
    apiKey: 'your_api_key',
    timeout: 30000
  },
  isActive: true,
  priority: 3
});
```

### 5. Error Handling

```typescript
import { getTransferService } from '../helpers';

const transferService = getTransferService();

try {
  const result = await transferService.validateBankAccount({
    accountNumber: '**********',
    bankCode: '044'
  });
  
  if (!result.success) {
    switch (result.code) {
      case 'NO_PROVIDER':
        console.error('No active provider available');
        break;
      case 'VALIDATION_ERROR':
        console.error('Validation failed:', result.error);
        break;
      case 'ALL_PROVIDERS_FAILED':
        console.error('All providers failed, check configurations');
        break;
      default:
        console.error('Unknown error:', result.error);
    }
  }
} catch (error) {
  console.error('Service error:', error);
}
```

### 6. Custom Retry Configuration

```typescript
import { ApiHelperService } from '../helpers';

const customService = new ApiHelperService(
  {
    baseUrl: 'https://api.example.com',
    apiKey: 'your_api_key',
    timeout: 30000
  },
  {
    attempts: 5,
    delay: 2000,
    backoff: 1.5
  }
);

const result = await customService.makeRequest({
  endpoint: '/custom-endpoint',
  method: 'POST',
  data: { key: 'value' }
});
```

## Integration with Existing Services

### Update Transfer Service

```typescript
// In src/services/transfer.service.ts
import { getTransferService } from '../helpers';

export class TransferService {
  private apiTransferService = getTransferService();
  
  async validateRecipient(validationData: any) {
    if (validationData.type === 'BANK') {
      // Use the helper service instead of mock data
      const result = await this.apiTransferService.validateBankAccount({
        accountNumber: validationData.identifier,
        bankCode: validationData.bankCode
      });
      
      if (result.success) {
        return {
          valid: true,
          recipientName: result.data?.accountName,
          recipientId: validationData.identifier,
          bankName: result.data?.bankName,
          fee: 50
        };
      }
    }
    
    // Handle other types...
  }
}
```

### Update Bill Service

```typescript
// In src/services/bill.service.ts
import { getBillPaymentService } from '../helpers';

export class BillService {
  private apiBillService = getBillPaymentService();
  
  async validateCustomer(providerId: string, customerCode: string) {
    const result = await this.apiBillService.validateCustomer({
      billerId: providerId,
      customerCode: customerCode
    });
    
    if (result.success) {
      return {
        valid: true,
        customerName: result.data?.customerName,
        amount: result.data?.amount,
        dueDate: result.data?.dueDate
      };
    }
    
    return { valid: false, error: result.error };
  }
}
```

## Features

- **Multiple Provider Support**: Seamlessly switch between Flutterwave, Paystack, VTPass, Baxi, and Monnify
- **Automatic Failover**: If one provider fails, automatically try others
- **Health Monitoring**: Continuous health checks for all providers
- **Retry Logic**: Built-in retry with exponential backoff
- **Unified Interface**: Same API regardless of underlying provider
- **Configuration Management**: Easy provider addition and configuration updates
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Type Safety**: Full TypeScript support with proper type definitions

## Provider Capabilities

| Provider    | Bank Transfers | Wallet Transfers | International | Airtime | Data | Electricity | Cable TV | Internet |
|-------------|---------------|------------------|---------------|---------|------|-------------|----------|----------|
| Flutterwave | ✅             | ✅                | ✅             | ✅       | ✅    | ✅           | ✅        | ✅        |
| Paystack    | ✅             | ❌                | ❌             | ✅       | ✅    | ✅           | ✅        | ❌        |
| VTPass      | ❌             | ❌                | ❌             | ✅       | ✅    | ✅           | ✅        | ✅        |
| Baxi        | ❌             | ❌                | ❌             | ✅       | ✅    | ✅           | ✅        | ✅        |
| Monnify     | ✅             | ❌                | ❌             | ❌       | ❌    | ❌           | ❌        | ❌        |

## Error Codes

- `NO_PROVIDER`: No active provider available
- `VALIDATION_ERROR`: Customer/account validation failed
- `TRANSFER_ERROR`: Transfer initiation failed
- `PAYMENT_ERROR`: Bill payment failed
- `NETWORK_ERROR`: Network connectivity issues
- `ALL_PROVIDERS_FAILED`: All configured providers failed
- `HEALTH_CHECK_FAILED`: Provider health check failed
- `STATUS_CHECK_ERROR`: Unable to check transaction status
