import Joi from 'joi';

export const registerSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  phone: Joi.string().pattern(/^\+234[0-9]{10}$/).required(),
  password: Joi.string().min(8).required(),
  dateOfBirth: Joi.string().isoDate().required(),
  agreeToTerms: Joi.boolean().valid(true).required()
});

export function validateRegister(data: any) {
  return registerSchema.validate(data, { abortEarly: true });
}

export const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required(),
  deviceInfo: Joi.object({
    deviceId: Joi.string().required(),
    platform: Joi.string().required(),
    appVersion: Joi.string().required()
  }).required()
});

export function validateLogin(data: any) {
  return loginSchema.validate(data, { abortEarly: true });
}
