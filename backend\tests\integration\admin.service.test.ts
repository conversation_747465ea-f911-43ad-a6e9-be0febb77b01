import { AdminService } from '../../src/services/admin.service';
import { UserRepository } from '../../src/repositories/user.repository';
import { TransactionRepository } from '../../src/repositories/transaction.repository';
import { WalletRepository } from '../../src/repositories/wallet.repository';

describe('AdminService Integration Tests', () => {
  let adminService: AdminService;
  let userRepository: UserRepository;
  let transactionRepository: TransactionRepository;
  let walletRepository: WalletRepository;

  beforeEach(() => {
    adminService = new AdminService();
    userRepository = new UserRepository();
    transactionRepository = new TransactionRepository();
    walletRepository = new WalletRepository();
  });

  describe('getDashboardStats', () => {
    it('should return dashboard statistics', async () => {
      // Create test data
      const user1 = await userRepository.create({
        firstName: 'User1',
        lastName: 'Test',
        email: '<EMAIL>',
        phone: '+**********',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01'
      });

      const user2 = await userRepository.create({
        firstName: 'User2',
        lastName: 'Test',
        email: '<EMAIL>',
        phone: '+**********',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01'
      });      // Create wallets
      await walletRepository.create({ 
        userId: user1.id,
        accountNumber: `ZW${Date.now()}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        status: 'ACTIVE'
      });
      await walletRepository.create({ 
        userId: user2.id,
        accountNumber: `ZW${Date.now() + 1}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        status: 'ACTIVE'
      });

      // Create transactions
      await transactionRepository.create({
        userId: user1.id,
        type: 'CREDIT',
        amount: 100,
        fee: 0,
        description: 'Test transaction 1',
        status: 'COMPLETED',
        category: 'transfer',
        reference: 'ref1'
      });

      await transactionRepository.create({
        userId: user2.id,
        type: 'DEBIT',
        amount: 50,
        fee: 1,
        description: 'Test transaction 2',
        status: 'COMPLETED',
        category: 'transfer',
        reference: 'ref2'
      });

      const stats = await adminService.getDashboardStats();

      expect(stats).toBeDefined();
      expect(stats.totalUsers).toBeGreaterThanOrEqual(2);
      expect(stats.activeUsers).toBeGreaterThanOrEqual(0);
      expect(stats.totalTransactions).toBeGreaterThanOrEqual(2);
      expect(stats.totalVolume).toBeGreaterThanOrEqual(150);
      expect(stats.averageTransactionValue).toBeGreaterThan(0);
      expect(Array.isArray(stats.topTransactionCategories)).toBe(true);
      expect(Array.isArray(stats.userRegistrationTrend)).toBe(true);
    });

    it('should handle empty database', async () => {
      const stats = await adminService.getDashboardStats();

      expect(stats).toBeDefined();
      expect(stats.totalUsers).toBe(0);
      expect(stats.activeUsers).toBe(0);
      expect(stats.totalTransactions).toBe(0);
      expect(stats.totalVolume).toBe(0);
      expect(stats.averageTransactionValue).toBe(0);
      expect(stats.revenueThisMonth).toBe(0);
      expect(stats.growthRate).toBe(0);
    });
  });

  describe('getUsers', () => {
    beforeEach(async () => {
      // Create test users
      for (let i = 1; i <= 5; i++) {
        await userRepository.create({
          firstName: `User${i}`,
          lastName: 'Test',
          email: `user${i}@example.com`,
          phone: `+123456789${i}`,
          password: 'hashedpassword',
          dateOfBirth: '1990-01-01'
        });
      }
    });

    it('should return paginated users', async () => {
      const result = await adminService.getUsers({
        page: 1,
        limit: 3
      });      expect(result).toBeDefined();
      expect(result.users).toBeDefined();
      expect(Array.isArray(result.users)).toBe(true);
      expect(result.users.length).toBeLessThanOrEqual(3);
      expect(result.pagination.total).toBeGreaterThanOrEqual(5);
      expect(result.pagination.currentPage).toBe(1);
      expect(result.pagination.pages).toBeGreaterThanOrEqual(2);
    });

    it('should search users by name', async () => {
      const result = await adminService.getUsers({
        page: 1,
        limit: 10,
        search: 'User1'
      });

      expect(result).toBeDefined();
      expect(result.users.length).toBeGreaterThanOrEqual(1);
      expect(result.users[0].firstName).toContain('User1');
    });

    it('should filter users by status', async () => {
      const result = await adminService.getUsers({
        page: 1,
        limit: 10,
        status: 'PENDING_VERIFICATION'
      });

      expect(result).toBeDefined();
      expect(Array.isArray(result.users)).toBe(true);
      // All new users should have PENDING_VERIFICATION status
      result.users.forEach(user => {
        expect(user.status).toBe('PENDING_VERIFICATION');
      });
    });
  });

  describe('getTransactions', () => {
    beforeEach(async () => {
      // Create test user and transactions
      const user = await userRepository.create({
        firstName: 'TestUser',
        lastName: 'Test',
        email: '<EMAIL>',
        phone: '+**********',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01'
      });

      for (let i = 1; i <= 5; i++) {
        await transactionRepository.create({
          userId: user.id,
          type: i % 2 === 0 ? 'CREDIT' : 'DEBIT',
          amount: i * 10,
          fee: 1,
          description: `Test transaction ${i}`,
          status: 'COMPLETED',
          category: 'transfer',
          reference: `ref${i}`
        });
      }
    });

    it('should return paginated transactions', async () => {
      const result = await adminService.getTransactions({
        page: 1,
        limit: 3
      });      expect(result).toBeDefined();
      expect(result.transactions).toBeDefined();
      expect(Array.isArray(result.transactions)).toBe(true);
      expect(result.transactions.length).toBeLessThanOrEqual(3);
      expect(result.pagination.total).toBeGreaterThanOrEqual(5);
      expect(result.pagination.currentPage).toBe(1);
      expect(result.pagination.pages).toBeGreaterThanOrEqual(2);
    });

    it('should filter transactions by status', async () => {
      const result = await adminService.getTransactions({
        page: 1,
        limit: 10,
        status: 'COMPLETED'
      });

      expect(result).toBeDefined();
      expect(Array.isArray(result.transactions)).toBe(true);
      result.transactions.forEach(transaction => {
        expect(transaction.status).toBe('COMPLETED');
      });
    });

    it('should filter transactions by type', async () => {
      const result = await adminService.getTransactions({
        page: 1,
        limit: 10,
        type: 'CREDIT'
      });

      expect(result).toBeDefined();
      expect(Array.isArray(result.transactions)).toBe(true);
      result.transactions.forEach(transaction => {
        expect(transaction.type).toBe('CREDIT');
      });
    });
  });

  describe('user management', () => {
    let testUserId: string;

    beforeEach(async () => {
      const user = await userRepository.create({
        firstName: 'TestUser',
        lastName: 'Management',
        email: '<EMAIL>',
        phone: '+**********',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01'
      });
      testUserId = user.id;
    });

    it('should suspend user', async () => {
      const result = await adminService.suspendUser(testUserId, 'Test suspension');

      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      const updatedUser = await userRepository.findById(testUserId);
      expect(updatedUser?.status).toBe('SUSPENDED');
    });

    it('should activate user', async () => {
      // First suspend the user
      await adminService.suspendUser(testUserId, 'Test suspension');

      const result = await adminService.activateUser(testUserId);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      const updatedUser = await userRepository.findById(testUserId);
      expect(updatedUser?.status).toBe('ACTIVE');
    });

    it('should approve KYC', async () => {
      const result = await adminService.approveKYC(testUserId);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      const updatedUser = await userRepository.findById(testUserId);
      expect(updatedUser?.kycVerified).toBe(true);
      expect(updatedUser?.status).toBe('ACTIVE');
    });

    it('should reject KYC', async () => {
      const result = await adminService.rejectKYC(testUserId, 'Invalid documents');

      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      const updatedUser = await userRepository.findById(testUserId);
      expect(updatedUser?.kycVerified).toBe(false);
      expect(updatedUser?.status).toBe('PENDING_VERIFICATION');
    });    it('should get user details', async () => {
      const result = await adminService.getUserDetails(testUserId);

      expect(result).toBeDefined();
      expect(result.user).toBeDefined();
      expect(result.user.id).toEqual(testUserId);
      expect(result.transactionSummary).toBeDefined();
      expect(result.transactionSummary.recentTransactions).toBeDefined();
      expect(Array.isArray(result.transactionSummary.recentTransactions)).toBe(true);
    });
  });
});
