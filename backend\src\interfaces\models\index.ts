import { Document } from 'mongoose';

/**
 * Base User interface - used in services that work with transformed user data
 */
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  dateOfBirth: string;
  kycVerified: boolean;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  status: 'ACTIVE' | 'SUSPENDED' | 'PENDING_VERIFICATION';
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Extended User interface for Mongoose documents
 */
export interface IUser extends Document {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  dateOfBirth: string;
  kycVerified: boolean;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  status: 'ACTIVE' | 'SUSPENDED' | 'PENDING_VERIFICATION';
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Base Wallet interface - used in services that work with transformed wallet data
 */
export interface Wallet {
  id: string;
  userId: string;
  accountNumber: string;
  customerID?: string;
  accountName?: string;
  available: number;
  pending: number;
  currency: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'CLOSED';
  limits: {
    daily: number;
    monthly: number;
    single: number;
    dailyTransfer?: number;
    dailyTransferUsed?: number;
    singleTransferMax?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Extended Wallet interface for Mongoose documents
 */
export interface IWallet extends Document {
  _id: string;
  userId: string;
  accountNumber: string;
  customerID?: string;
  accountName?: string;
  available: number;
  pending: number;
  currency: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'CLOSED';
  limits: {
    daily: number;
    monthly: number;
    single: number;
    dailyTransfer?: number;
    dailyTransferUsed?: number;
    singleTransferMax?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Base Transaction interface - used in services that work with transformed transaction data
 */
export interface Transaction {
  id: string;
  userId: string;
  type: 'CREDIT' | 'DEBIT';
  amount: number;
  fee: number;
  description: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  category: string;
  reference: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Extended Transaction interface for Mongoose documents
 */
export interface ITransaction extends Document {
  _id: string;
  userId: string;
  type: 'CREDIT' | 'DEBIT';
  amount: number;
  fee: number;
  description: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  category: string;
  reference: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Extended Notification interface for Mongoose documents
 */
export interface INotification extends Document {
  _id: string;
  userId: string;
  title: string;
  message: string;
  type: 'TRANSACTION' | 'SECURITY' | 'PROMOTION' | 'SYSTEM';
  read: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  data?: any;
  readAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
