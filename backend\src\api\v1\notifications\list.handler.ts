import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { NotificationService } from '../../../services/notification.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const queryParams = event.queryStringParameters || {};
    const notificationService = new NotificationService();
    const notifications = await notificationService.getNotifications(user.userId, queryParams);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: notifications })
    };
  } catch (err: any) {
    logger.error('Get notifications error', { error: err.message });
    return errorResponse(401, 'UNAUTHORIZED', err.message);
  }
};
