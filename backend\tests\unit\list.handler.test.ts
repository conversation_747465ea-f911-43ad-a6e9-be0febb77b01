import { APIGatewayProxyEvent } from 'aws-lambda';
import { main } from '../../src/api/v1/notifications/list.handler';
import { NotificationService } from '../../src/services/notification.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';
import { TokenPayload } from '../../src/common/security/jwt.service';

// Mock dependencies
jest.mock('../../src/services/notification.service');
jest.mock('../../src/common/middlewares/auth.middleware');

const mockNotificationService = NotificationService as jest.MockedClass<typeof NotificationService>;
const mockAuthMiddleware = AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>;

describe('List Notifications Handler', () => {
  let mockEvent: APIGatewayProxyEvent;
  let mockNotificationServiceInstance: jest.Mocked<NotificationService>;
  let mockAuthMiddlewareInstance: jest.Mocked<AuthMiddleware>;

  beforeEach(() => {
    jest.clearAllMocks();

    mockEvent = {
      headers: {
        Authorization: 'Bearer valid-token'
      },
      body: null,
      pathParameters: null,
      queryStringParameters: {
        page: '1',
        limit: '10'
      },
      httpMethod: 'GET',
      path: '/notifications',
      resource: '/notifications',
      requestContext: {} as any,
      isBase64Encoded: false,
      multiValueHeaders: {},
      multiValueQueryStringParameters: null,
      stageVariables: null
    };

    // Mock NotificationService instance
    mockNotificationServiceInstance = {
      createNotification: jest.fn(),
      createTransactionNotification: jest.fn(),
      createSecurityNotification: jest.fn(),
      getNotifications: jest.fn(),
      markAsRead: jest.fn(),
      markAllAsRead: jest.fn(),
    } as unknown as jest.Mocked<NotificationService>;

    mockNotificationService.mockImplementation(() => mockNotificationServiceInstance);

    // Mock AuthMiddleware instance
    mockAuthMiddlewareInstance = {
      authenticate: jest.fn(),
    } as unknown as jest.Mocked<AuthMiddleware>;

    mockAuthMiddleware.mockImplementation(() => mockAuthMiddlewareInstance);
  });

  describe('successful notification retrieval', () => {
    it('should return user notifications with pagination', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };      const mockNotifications = {
        notifications: [
          {
            id: 'notif1',
            userId: 'user123',
            title: 'Transaction Successful',
            message: 'Your transfer of ₦10,000 was successful',
            type: 'TRANSACTION',
            read: false,
            priority: 'HIGH',
            createdAt: new Date().toISOString()
          },
          {
            id: 'notif2',
            userId: 'user123',
            title: 'Welcome to ZapWallet',
            message: 'Your account has been created successfully',
            type: 'SYSTEM',
            read: true,
            priority: 'LOW',
            createdAt: new Date().toISOString()
          }
        ],
        pagination: {
          total: 15,
          pages: 2,
          currentPage: 1,
          limit: 10
        },
        unreadCount: 5
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.getNotifications.mockResolvedValue(mockNotifications);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockNotifications
      });
      expect(mockAuthMiddlewareInstance.authenticate).toHaveBeenCalledWith(mockEvent);
      expect(mockNotificationServiceInstance.getNotifications).toHaveBeenCalledWith(
        'user123',
        { page: '1', limit: '10' }
      );
    });

    it('should handle empty query parameters', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const mockNotifications = {
        notifications: [],
        totalCount: 0,
        currentPage: 1,
        totalPages: 0,
        hasNext: false,
        hasPrevious: false
      };

      mockEvent.queryStringParameters = null;
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.getNotifications.mockResolvedValue(mockNotifications);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockNotifications
      });
      expect(mockNotificationServiceInstance.getNotifications).toHaveBeenCalledWith(
        'user123',
        {}
      );
    });

    it('should filter notifications by read status', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const mockNotifications = {
        notifications: [
          {
            id: 'notif1',
            userId: 'user123',
            title: 'Unread Notification',
            message: 'This is an unread notification',
            type: 'SYSTEM',
            isRead: false,
            createdAt: new Date().toISOString()
          }
        ],
        totalCount: 5,
        currentPage: 1,
        totalPages: 1,
        hasNext: false,
        hasPrevious: false
      };

      mockEvent.queryStringParameters = { isRead: 'false' };
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.getNotifications.mockResolvedValue(mockNotifications);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockNotifications
      });
      expect(mockNotificationServiceInstance.getNotifications).toHaveBeenCalledWith(
        'user123',
        { isRead: 'false' }
      );
    });

    it('should handle different notification types', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const mockNotifications = {
        notifications: [
          {
            id: 'notif1',
            userId: 'user123',
            title: 'Security Alert',
            message: 'New login detected from Lagos, Nigeria',
            type: 'SECURITY',
            isRead: false,
            createdAt: new Date().toISOString(),
            data: {
              device: 'iPhone 13',
              location: 'Lagos, Nigeria'
            }
          },
          {
            id: 'notif2',
            userId: 'user123',
            title: 'Transaction Completed',
            message: 'Bill payment of ₦5,000 completed',
            type: 'TRANSACTION',
            isRead: false,
            createdAt: new Date().toISOString(),
            data: {
              transactionId: 'txn123',
              amount: 5000
            }
          }
        ],
        totalCount: 2,
        currentPage: 1,
        totalPages: 1,
        hasNext: false,
        hasPrevious: false
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.getNotifications.mockResolvedValue(mockNotifications);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockNotifications
      });
    });
  });

  describe('authentication errors', () => {
    it('should return 401 for missing authorization', async () => {
      mockEvent.headers = {};

      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('UNAUTHORIZED');
      expect(body.error.message).toBe('Missing or invalid authorization header');
    });

    it('should return 401 for invalid token', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Invalid or expired token');
      });

      const result = await main(mockEvent);      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('Invalid or expired token');
    });

    it('should return 401 for expired token', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Token expired');
      });

      const result = await main(mockEvent);      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('Token expired');
    });
  });

  describe('service errors', () => {
    it('should handle notification service errors', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.getNotifications.mockRejectedValue(new Error('Database connection failed'));

      const result = await main(mockEvent);      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('Database connection failed');
    });

    it('should handle timeout errors', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.getNotifications.mockRejectedValue(new Error('Request timeout'));

      const result = await main(mockEvent);      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('Request timeout');
    });
  });

  describe('edge cases', () => {
    it('should handle malformed Authorization header', async () => {
      mockEvent.headers = { Authorization: 'Invalid header' };

      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Malformed authorization header');
      });

      const result = await main(mockEvent);      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('Malformed authorization header');
    });

    it('should handle very large page numbers', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const mockNotifications = {
        notifications: [],
        totalCount: 0,
        currentPage: 999,
        totalPages: 0,
        hasNext: false,
        hasPrevious: true
      };

      mockEvent.queryStringParameters = { page: '999', limit: '10' };
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.getNotifications.mockResolvedValue(mockNotifications);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockNotifications
      });
    });

    it('should handle invalid query parameters gracefully', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const mockNotifications = {
        notifications: [],
        totalCount: 0,
        currentPage: 1,
        totalPages: 0,
        hasNext: false,
        hasPrevious: false
      };

      mockEvent.queryStringParameters = { 
        page: 'invalid', 
        limit: 'invalid',
        type: 'UNKNOWN'
      };
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.getNotifications.mockResolvedValue(mockNotifications);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockNotifications
      });
      expect(mockNotificationServiceInstance.getNotifications).toHaveBeenCalledWith(
        'user123',
        { page: 'invalid', limit: 'invalid', type: 'UNKNOWN' }
      );
    });
  });
});
