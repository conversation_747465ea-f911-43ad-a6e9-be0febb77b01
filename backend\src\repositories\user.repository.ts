import { UserModel, IUser } from '../models/user.schema';
import { DatabaseConfig } from '../config/database';
import { User, UserQueryOptions } from '../interfaces';

export class UserRepository {
  constructor() {
    // Only ensure database connection if not in test mode
    if (process.env.NODE_ENV !== 'test') {
      DatabaseConfig.getInstance().connect();
    }
  }

  async create(user: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'kycVerified' | 'role' | 'status'>): Promise<User> {
    const newUser = new UserModel({
      ...user,
      kycVerified: false,
      role: 'USER',
      status: 'PENDING_VERIFICATION'
    });
    
    const savedUser = await newUser.save();
    return savedUser.toJSON() as User;
  }

  async findByEmail(email: string): Promise<User | null> {
    const user = await UserModel.findOne({ email: email.toLowerCase() });
    return user ? (user.toJSON() as User) : null;
  }

  async findById(id: string): Promise<User | null> {
    const user = await UserModel.findById(id);
    return user ? (user.toJSON() as User) : null;
  }

  async findByPhone(phone: string): Promise<User | null> {
    const user = await UserModel.findOne({ phone });
    return user ? (user.toJSON() as User) : null;
  }

  async update(id: string, updateData: Partial<User>): Promise<User> {
    const user = await UserModel.findByIdAndUpdate(
      id, 
      updateData, 
      { new: true, runValidators: true }
    );
    
    if (!user) {
      throw new Error('User not found');
    }
    
    return user.toJSON() as User;
  }

  async updateLastLogin(id: string): Promise<void> {
    await UserModel.findByIdAndUpdate(id, { lastLoginAt: new Date() });
  }

  async findMany(options: UserQueryOptions = {}): Promise<{
    users: User[];
    total: number;
    page: number;
    pages: number;
  }> {
    const {
      page = 1,
      limit = 50,
      search,
      status,
      role,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options;

    const query: any = {};

    // Add search filter
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    // Add status filter
    if (status) {
      query.status = status;
    }

    // Add role filter
    if (role) {
      query.role = role;
    }

    const skip = (page - 1) * limit;
    const sortObj: any = {};
    sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const [users, total] = await Promise.all([
      UserModel.find(query)
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .lean(),
      UserModel.countDocuments(query)
    ]);

    return {
      users: users.map(user => ({
        ...user,
        id: user._id.toString(),
        _id: undefined
      })) as User[],
      total,
      page,
      pages: Math.ceil(total / limit)
    };
  }

  async getStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    verifiedUsers: number;
    recentRegistrations: number;
  }> {
    const [
      totalUsers,
      activeUsers, 
      verifiedUsers,
      recentRegistrations
    ] = await Promise.all([
      UserModel.countDocuments(),
      UserModel.countDocuments({ status: 'ACTIVE' }),
      UserModel.countDocuments({ kycVerified: true }),
      UserModel.countDocuments({ 
        createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } 
      })
    ]);

    return {
      totalUsers,
      activeUsers,
      verifiedUsers,
      recentRegistrations
    };
  }
  async getUserRegistrationTrend(months: number = 6): Promise<Array<{
    date: string;
    count: number;
  }>> {
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - months);

    const pipeline: any[] = [
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ];

    const results = await UserModel.aggregate(pipeline);
    
    return results.map(result => ({
      date: `${result._id.year}-${String(result._id.month).padStart(2, '0')}`,
      count: result.count
    }));
  }

  async delete(id: string): Promise<boolean> {
    const result = await UserModel.findByIdAndDelete(id);
    return !!result;
  }

  async findByEmailWithPassword(email: string): Promise<(User & { password: string }) | null> {
    const user = await UserModel.findOne({ email: email.toLowerCase() });
    if (!user) return null;
    
    // Convert to object but keep password field
    const userObj = user.toObject();
    return {
      id: userObj._id.toString(),
      firstName: userObj.firstName,
      lastName: userObj.lastName,
      email: userObj.email,
      phone: userObj.phone,
      password: userObj.password, // Keep password for authentication
      dateOfBirth: userObj.dateOfBirth,
      kycVerified: userObj.kycVerified,
      role: userObj.role,
      status: userObj.status,
      lastLoginAt: userObj.lastLoginAt,
      createdAt: userObj.createdAt,
      updatedAt: userObj.updatedAt
    };
  }
}
