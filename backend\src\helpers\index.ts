/**
 * Third-party API Helper Services
 * 
 * This module provides comprehensive helper services for integrating with
 * multiple third-party APIs for transfer and bill payment processing.
 * 
 * Features:
 * - Multiple provider support with automatic failover
 * - Unified API interface across different providers
 * - Built-in retry logic and error handling
 * - Health monitoring and provider management
 * - Easy configuration and provider switching
 * 
 * Usage:
 * ```typescript
 * import { ServiceProviderManager } from '../helpers';
 * 
 * const manager = new ServiceProviderManager();
 * const transferService = manager.getTransferService();
 * const billService = manager.getBillPaymentService();
 * ```
 */

export { ApiHelperService } from './api-helper.service';
export { TransferApiService } from './transfer-api.service';
export { BillPaymentApiService } from './bill-payment-api.service';
export { ServiceProviderManager } from './service-provider-manager';

// Re-export interfaces for convenience
export type {
  ApiConfig,
  ApiRequest,
  ApiResponse,
  RetryConfig,
  ServiceProvider,
  ProviderCapability,
  TransferApiRequest,
  TransferApiResponse,
  BankValidationRequest,
  BankValidationResponse,
  BillValidationRequest,
  BillValidationResponse,
  BillPaymentRequest,
  BillPaymentResponse
} from '../interfaces/common';

// Import types for function signatures
import type { ServiceProviderManager } from './service-provider-manager';
import type { TransferApiService } from './transfer-api.service';
import type { BillPaymentApiService } from './bill-payment-api.service';

/**
 * Create a singleton instance of ServiceProviderManager
 * This ensures consistent provider management across the application
 */
let serviceProviderManager: ServiceProviderManager | null = null;

export function getServiceProviderManager(): ServiceProviderManager {
  if (!serviceProviderManager) {
    const { ServiceProviderManager: SPM } = require('./service-provider-manager');
    serviceProviderManager = new SPM();
  }
  return serviceProviderManager!;
}

/**
 * Utility function to get transfer service
 */
export function getTransferService(): TransferApiService {
  return getServiceProviderManager().getTransferService();
}

/**
 * Utility function to get bill payment service
 */
export function getBillPaymentService(): BillPaymentApiService {
  return getServiceProviderManager().getBillPaymentService();
}

/**
 * Environment configuration helper
 */
export const ProviderConfig = {
  /**
   * Get provider configuration from environment variables
   */
  getProviderConfig(providerId: string): any {
    const configs: Record<string, any> = {
      flutterwave: {
        baseUrl: process.env.FLUTTERWAVE_BASE_URL || 'https://api.flutterwave.com/v3',
        apiKey: process.env.FLUTTERWAVE_SECRET_KEY,
        publicKey: process.env.FLUTTERWAVE_PUBLIC_KEY,
        timeout: 30000
      },
      paystack: {
        baseUrl: process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co',
        apiKey: process.env.PAYSTACK_SECRET_KEY,
        publicKey: process.env.PAYSTACK_PUBLIC_KEY,
        timeout: 30000
      },
      vtpass: {
        baseUrl: process.env.VTPASS_BASE_URL || 'https://vtpass.com/api',
        apiKey: process.env.VTPASS_API_KEY,
        secretKey: process.env.VTPASS_SECRET_KEY,
        username: process.env.VTPASS_USERNAME,
        password: process.env.VTPASS_PASSWORD,
        timeout: 30000
      },
      baxi: {
        baseUrl: process.env.BAXI_BASE_URL || 'https://payments.baxipay.com.ng/api/baxipay',
        apiKey: process.env.BAXI_API_KEY,
        secretKey: process.env.BAXI_SECRET_KEY,
        timeout: 30000
      },
      monnify: {
        baseUrl: process.env.MONNIFY_BASE_URL || 'https://api.monnify.com/api/v1',
        apiKey: process.env.MONNIFY_API_KEY,
        secretKey: process.env.MONNIFY_SECRET_KEY,
        contractCode: process.env.MONNIFY_CONTRACT_CODE,
        timeout: 30000
      }
    };

    return configs[providerId] || null;
  },

  /**
   * Validate required environment variables for a provider
   */
  validateProviderConfig(providerId: string): boolean {
    const config = this.getProviderConfig(providerId);
    if (!config) return false;

    const requiredFields: Record<string, string[]> = {
      flutterwave: ['apiKey'],
      paystack: ['apiKey'],
      vtpass: ['apiKey', 'secretKey'],
      baxi: ['apiKey', 'secretKey'],
      monnify: ['apiKey', 'secretKey']
    };

    const required = requiredFields[providerId] || [];
    return required.every(field => config[field]);
  },

  /**
   * Get all valid provider configurations
   */
  getValidProviders(): string[] {
    const providers = ['flutterwave', 'paystack', 'vtpass', 'baxi', 'monnify'];
    return providers.filter(provider => this.validateProviderConfig(provider));
  }
};

/**
 * API endpoint mappings for different providers
 */
export const ApiEndpoints = {
  flutterwave: {
    transfers: '/transfers',
    bankValidation: '/accounts/resolve',
    bills: '/bills',
    billCategories: '/bill-categories',
    health: '/status'
  },
  paystack: {
    transfers: '/transfer',
    bankValidation: '/bank/resolve',
    bills: '/charge',
    billCategories: '/bank',
    health: '/bank'
  },
  vtpass: {
    transfers: '/pay',
    bankValidation: '/verify-merchant',
    bills: '/pay',
    billCategories: '/service-variations',
    health: '/balance'
  },
  baxi: {
    transfers: '/services/transfer/request',
    bankValidation: '/services/nameverification/request',
    bills: '/services/{service}/request',
    billCategories: '/services',
    health: '/services'
  }
};

/**
 * Response status mappings for different providers
 */
export const StatusMappings = {
  success: ['success', 'successful', 'completed', 'delivered'],
  pending: ['pending', 'processing', 'initiated'],
  failed: ['failed', 'error', 'cancelled', 'declined']
};
