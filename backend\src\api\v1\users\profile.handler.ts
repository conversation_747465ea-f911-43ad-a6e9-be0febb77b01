import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { UserService } from '../../../services/user.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const userService = new UserService();
    const profile = await userService.getProfile(user.userId);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: profile })
    };
  } catch (err: any) {
    logger.error('Get profile error', { error: err.message });
    return errorResponse(401, 'UNAUTHORIZED', err.message);
  }
};
