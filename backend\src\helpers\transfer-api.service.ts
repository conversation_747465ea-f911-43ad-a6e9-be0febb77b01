import { ApiHelperService } from './api-helper.service';
import {
  ApiConfig,
  TransferApiRequest,
  TransferApiResponse,
  BankValidationRequest,
  BankValidationResponse,
  ApiResponse
} from '../interfaces/common';
import { logger } from '../common/logging/logger';

/**
 * Transfer API helper service for handling bank transfers and wallet transfers
 * Supports multiple providers with automatic failover and provider selection
 */
export class TransferApiService {
  private providers: Map<string, ApiHelperService> = new Map();
  private activeProvider: string | null = null;

  constructor() {
    this.initializeProviders();
  }

  /**
   * Initialize transfer service providers
   * Configure multiple providers for redundancy
   */
  private initializeProviders(): void {
    // Example provider configurations
    const providers = [
      {
        id: 'flutterwave',
        name: 'Flutterwave',
        config: {
          baseUrl: process.env.FLUTTERWAVE_BASE_URL || 'https://api.flutterwave.com/v3',
          apiKey: process.env.FLUTTERWAVE_SECRET_KEY || '',
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      },
      {
        id: 'paystack',
        name: 'Paystack',
        config: {
          baseUrl: process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co',
          apiKey: process.env.PAYSTACK_SECRET_KEY || '',
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      },
      {
        id: 'monnify',
        name: 'Monnify',
        config: {
          baseUrl: process.env.MONNIFY_BASE_URL || 'https://api.monnify.com/api/v1',
          apiKey: process.env.MONNIFY_API_KEY || '',
          secretKey: process.env.MONNIFY_SECRET_KEY || '',
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      }
    ];

    providers.forEach(provider => {
      if (provider.config.apiKey) {
        this.providers.set(provider.id, new ApiHelperService(provider.config));
        if (!this.activeProvider) {
          this.activeProvider = provider.id;
        }
      }
    });

    logger.info('Transfer providers initialized', {
      providers: Array.from(this.providers.keys()),
      activeProvider: this.activeProvider
    });
  }

  /**
   * Validate bank account details
   */
  async validateBankAccount(request: BankValidationRequest): Promise<ApiResponse<BankValidationResponse>> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        success: false,
        error: 'No active transfer provider available',
        code: 'NO_PROVIDER'
      };
    }

    try {
      // Try with current active provider
      let response = await this.validateWithProvider(provider, request);
      
      // If failed, try with other providers
      if (!response.success) {
        response = await this.tryOtherProviders('validate', request);
      }

      return response;
    } catch (error: any) {
      logger.error('Bank validation error', { error: error.message, request });
      return {
        success: false,
        error: 'Bank validation failed',
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Initiate bank transfer
   */
  async initiateTransfer(request: TransferApiRequest): Promise<ApiResponse<TransferApiResponse>> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        success: false,
        error: 'No active transfer provider available',
        code: 'NO_PROVIDER'
      };
    }

    try {
      // Try with current active provider
      let response = await this.transferWithProvider(provider, request);
      
      // If failed, try with other providers
      if (!response.success) {
        response = await this.tryOtherProviders('transfer', request);
      }

      return response;
    } catch (error: any) {
      logger.error('Transfer initiation error', { error: error.message, request });
      return {
        success: false,
        error: 'Transfer initiation failed',
        code: 'TRANSFER_ERROR'
      };
    }
  }

  /**
   * Check transfer status
   */
  async checkTransferStatus(reference: string): Promise<ApiResponse<TransferApiResponse>> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        success: false,
        error: 'No active transfer provider available',
        code: 'NO_PROVIDER'
      };
    }

    try {
      const response = await provider.makeRequest({
        endpoint: `/transfers/${reference}`,
        method: 'GET'
      });

      return this.transformTransferResponse(response);
    } catch (error: any) {
      logger.error('Transfer status check error', { error: error.message, reference });
      return {
        success: false,
        error: 'Failed to check transfer status',
        code: 'STATUS_CHECK_ERROR'
      };
    }
  }

  /**
   * Validate bank account with specific provider
   */
  private async validateWithProvider(
    provider: ApiHelperService, 
    request: BankValidationRequest
  ): Promise<ApiResponse<BankValidationResponse>> {
    
    // This would be customized based on each provider's API format
    const response = await provider.makeRequest({
      endpoint: '/accounts/resolve',
      method: 'POST',
      data: {
        account_number: request.accountNumber,
        account_bank: request.bankCode
      }
    });

    if (response.success && response.data) {
      return {
        success: true,
        data: {
          accountName: response.data.account_name || response.data.accountName,
          accountNumber: request.accountNumber,
          bankCode: request.bankCode,
          bankName: response.data.bank_name || response.data.bankName
        }
      };
    }

    return response;
  }

  /**
   * Initiate transfer with specific provider
   */
  private async transferWithProvider(
    provider: ApiHelperService, 
    request: TransferApiRequest
  ): Promise<ApiResponse<TransferApiResponse>> {
    
    // Transform request based on provider format
    const providerRequest = this.transformTransferRequest(request);
    
    const response = await provider.makeRequest({
      endpoint: '/transfers',
      method: 'POST',
      data: providerRequest
    });

    return this.transformTransferResponse(response);
  }

  /**
   * Try other providers if current one fails
   */
  private async tryOtherProviders(operation: 'validate' | 'transfer', request: any): Promise<ApiResponse> {
    const providers = Array.from(this.providers.entries());
    
    for (const [providerId, provider] of providers) {
      if (providerId === this.activeProvider) continue;
      
      try {
        logger.info(`Trying provider ${providerId} for ${operation}`);
        
        let response;
        if (operation === 'validate') {
          response = await this.validateWithProvider(provider, request);
        } else {
          response = await this.transferWithProvider(provider, request);
        }
        
        if (response.success) {
          this.activeProvider = providerId;
          logger.info(`Switched to provider ${providerId}`);
          return response;
        }
      } catch (error: any) {
        logger.warn(`Provider ${providerId} failed`, { error: error.message });
        continue;
      }
    }

    return {
      success: false,
      error: 'All providers failed',
      code: 'ALL_PROVIDERS_FAILED'
    };
  }

  /**
   * Transform transfer request to provider format
   */
  private transformTransferRequest(request: TransferApiRequest): any {
    // This would be customized based on the active provider
    return {
      account_bank: request.recipientBank,
      account_number: request.recipientAccount,
      amount: request.amount,
      narration: request.narration,
      reference: request.reference,
      currency: request.currency || 'NGN',
      beneficiary_name: request.narration
    };
  }

  /**
   * Transform provider response to standard format
   */
  private transformTransferResponse(response: ApiResponse): ApiResponse<TransferApiResponse> {
    if (!response.success) {
      return response;
    }

    const data = response.data;
    return {
      success: true,
      data: {
        status: this.mapTransferStatus(data?.status),
        reference: data?.reference || data?.tx_ref,
        fee: data?.fee || 0,
        recipientName: data?.full_name || data?.recipient_name,
        sessionId: data?.id?.toString(),
        estimatedTime: data?.estimated_time
      }
    };
  }

  /**
   * Map provider-specific status to standard status
   */
  private mapTransferStatus(status: string): 'SUCCESS' | 'FAILED' | 'PENDING' {
    const statusMap: Record<string, 'SUCCESS' | 'FAILED' | 'PENDING'> = {
      'successful': 'SUCCESS',
      'success': 'SUCCESS',
      'completed': 'SUCCESS',
      'failed': 'FAILED',
      'error': 'FAILED',
      'pending': 'PENDING',
      'processing': 'PENDING'
    };

    return statusMap[status?.toLowerCase()] || 'PENDING';
  }

  /**
   * Get active provider instance
   */
  private getActiveProvider(): ApiHelperService | null {
    if (!this.activeProvider) return null;
    return this.providers.get(this.activeProvider) || null;
  }

  /**
   * Set active provider
   */
  setActiveProvider(providerId: string): boolean {
    if (this.providers.has(providerId)) {
      this.activeProvider = providerId;
      return true;
    }
    return false;
  }

  /**
   * Get available providers
   */
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Add new provider
   */
  addProvider(providerId: string, config: ApiConfig): void {
    this.providers.set(providerId, new ApiHelperService(config));
    if (!this.activeProvider) {
      this.activeProvider = providerId;
    }
  }

  /**
   * Remove provider
   */
  removeProvider(providerId: string): boolean {
    const removed = this.providers.delete(providerId);
    if (this.activeProvider === providerId) {
      this.activeProvider = this.providers.size > 0 ? Array.from(this.providers.keys())[0] : null;
    }
    return removed;
  }
}
