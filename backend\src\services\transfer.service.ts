import { v4 as uuidv4 } from 'uuid';
import { TransactionService } from './transaction.service';
import { WalletService } from './wallet.service';
import { WalletApiService } from './wallet-api.service';
import { UserRepository } from '../repositories/user.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import { Transfer } from '../interfaces/services';
import { ApiConfig } from '../interfaces/common';
import { logger } from '../common/logging/logger';

export class TransferService {
  private transactionService = new TransactionService();
  private walletService = new WalletService();
  private userRepository = new UserRepository();
  private walletRepository = new WalletRepository();
  private walletApiService: WalletApiService;
  
  // In-memory store for demonstration. Replace with PostgreSQL logic.
  private static transfers: Transfer[] = [];

  constructor() {
    // Initialize wallet API service with configuration
    const walletConfig: ApiConfig = {
      baseUrl: process.env.WALLET_API_BASE_URL || 'http://**************:9090/waas',
      apiKey: process.env.WALLET_API_KEY || '',
      secretKey: process.env.WALLET_SECRET_KEY || '',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    this.walletApiService = new WalletApiService(walletConfig);
  }

  async validateRecipient(validationData: any) {
    const { type, identifier, bankCode } = validationData;
    
    try {
      if (type === 'ZAPWALLET') {
        // Check if ZapWallet user exists by email/phone
        const user = await this.userRepository.findByEmail(identifier) || 
                     await this.userRepository.findByPhone(identifier);
        
        if (!user) {
          throw new Error('ZapWallet user not found');
        }

        const wallet = await this.walletRepository.findByUserId(user.id);
        if (!wallet) {
          throw new Error('Recipient wallet not found');
        }

        return {
          valid: true,
          recipientName: `${user.firstName} ${user.lastName}`,
          recipientId: user.id,
          accountNumber: wallet.accountNumber,
          fee: 0 // No fee for ZapWallet transfers
        };
      } else if (type === 'BANK') {
        // Authenticate with wallet service first
        await this.authenticateWalletService();
        
        // Use wallet API to validate bank account
        const enquiryResponse = await this.walletApiService.otherBankEnquiry(
          bankCode,
          identifier
        );

        if (!enquiryResponse.success) {
          throw new Error('Invalid bank account details');
        }

        return {
          valid: true,
          recipientName: enquiryResponse.data?.accountName || 'Valid Account',
          recipientId: identifier,
          bankCode,
          bankName: enquiryResponse.data?.bankName || 'Nigerian Bank',
          fee: 50 // Bank transfer fee
        };
      }
      
      throw new Error('Invalid recipient type');
    } catch (error: any) {
      logger.error('Recipient validation failed', { validationData, error: error.message });
      throw new Error(error.message || 'Recipient validation failed');
    }
  }

  async initiateTransfer(senderId: string, transferData: any) {
    const { recipientType, recipientId, amount, description, pin } = transferData;
    
    try {
      // TODO: Validate PIN
      // TODO: Check sender balance
      // TODO: Apply daily transfer limits
      
      const transferId = uuidv4();
      const reference = `TXN_${Date.now()}`;
      const fee = recipientType === 'ZAPWALLET' ? 0 : 50;
      
      let transferResult;
      
      if (recipientType === 'ZAPWALLET') {
        // Internal wallet transfer
        const recipientUser = await this.userRepository.findById(recipientId);
        if (!recipientUser) {
          throw new Error('Recipient not found');
        }

        transferResult = await this.walletService.transferBetweenWallets(
          senderId,
          recipientId,
          { amount, description }
        );
      } else if (recipientType === 'BANK') {
        // External bank transfer
        transferResult = await this.walletService.withdrawFromWallet(senderId, {
          ...transferData,
          narration: description
        });
      } else {
        throw new Error('Invalid transfer type');
      }

      const transfer: Transfer = {
        id: transferId,
        senderId,
        recipientType,
        recipientId,
        amount,
        fee,
        status: 'COMPLETED',
        reference,
        description: description || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      TransferService.transfers.push(transfer);

      // Record transaction
      await this.transactionService.createTransaction({
        userId: senderId,
        type: 'DEBIT',
        amount: amount + fee,
        fee,
        description: `Transfer to ${recipientType === 'ZAPWALLET' ? 'ZapWallet user' : 'bank account'}`,
        category: 'TRANSFER',
        reference,
        status: 'COMPLETED',
        metadata: {
          transferId,
          recipientType,
          recipientId,
          originalAmount: amount
        }
      });

      return {
        transferId,
        reference,
        status: transferResult.status,
        message: transferResult.message,
        fee,
        estimatedDelivery: recipientType === 'ZAPWALLET' ? 'Instant' : '1-3 business days'
      };
    } catch (error: any) {
      logger.error('Transfer initiation failed', { senderId, transferData, error: error.message });
      throw new Error('Failed to initiate transfer');
    }
  }

  async getTransferHistory(userId: string, limit = 50, offset = 0) {
    try {
      // Get transfers from static store (replace with database query)
      const userTransfers = TransferService.transfers
        .filter(t => t.senderId === userId)
        .slice(offset, offset + limit);

      return {
        transfers: userTransfers,
        total: TransferService.transfers.filter(t => t.senderId === userId).length,
        limit,
        offset
      };
    } catch (error: any) {
      logger.error('Get transfer history failed', { userId, error: error.message });
      throw new Error('Failed to get transfer history');
    }
  }

  async getTransferStatus(transferId: string) {
    try {
      const transfer = TransferService.transfers.find(t => t.id === transferId);
      
      if (!transfer) {
        throw new Error('Transfer not found');
      }

      return {
        transferId,
        status: transfer.status,
        reference: transfer.reference,
        amount: transfer.amount,
        fee: transfer.fee,
        recipientType: transfer.recipientType,
        createdAt: transfer.createdAt,
        updatedAt: transfer.updatedAt
      };
    } catch (error: any) {
      logger.error('Get transfer status failed', { transferId, error: error.message });
      throw new Error('Failed to get transfer status');
    }
  }

  async getTransferById(transferId: string): Promise<Transfer | undefined> {
    return TransferService.transfers.find(t => t.id === transferId);
  }

  async getUserTransfers(userId: string): Promise<Transfer[]> {
    return TransferService.transfers.filter(t => t.senderId === userId);
  }

  private async authenticateWalletService() {
    try {
      const authResponse = await this.walletApiService.authenticate({
        username: process.env.WALLET_USERNAME || '',
        password: process.env.WALLET_PASSWORD || '',
        clientId: process.env.WALLET_CLIENT_ID || '',
        clientSecret: process.env.WALLET_CLIENT_SECRET || ''
      });

      if (!authResponse.success) {
        throw new Error('Wallet service authentication failed');
      }
    } catch (error: any) {
      logger.error('Wallet service authentication failed', { error: error.message });
      throw new Error('Failed to authenticate with wallet service');
    }
  }
}
