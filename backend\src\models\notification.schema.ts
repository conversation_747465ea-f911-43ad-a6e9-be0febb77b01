import mongoose, { Schema, Document } from 'mongoose';
import { INotification } from '../interfaces/models';

export { INotification };

const NotificationSchema: Schema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  title: { type: String, required: true },
  message: { type: String, required: true },
  type: { type: String, enum: ['TRANSACTION', 'SECURITY', 'PROMOTION', 'SYSTEM'], required: true },
  read: { type: Boolean, default: false },
  priority: { type: String, enum: ['LOW', 'MEDIUM', 'HIGH'], default: 'MEDIUM' },
  data: { type: Schema.Types.Mixed }, // Additional metadata
  readAt: { type: Date }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
NotificationSchema.index({ userId: 1, createdAt: -1 });
NotificationSchema.index({ userId: 1, read: 1 });

export const NotificationModel = mongoose.model<INotification>('Notification', NotificationSchema);
