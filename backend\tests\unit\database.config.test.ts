import { DatabaseConfig } from '../../src/config/database';
import mongoose from 'mongoose';

// Mock mongoose completely
jest.mock('mongoose', () => {
  const mockConnection = {
    readyState: 0,
    close: jest.fn(),
  };
  
  return {
    connect: jest.fn(),
    disconnect: jest.fn(),
    connection: mockConnection,
    default: {
      connect: jest.fn(),
      disconnect: jest.fn(),
      connection: mockConnection,
    }
  };
});

describe('DatabaseConfig', () => {
  let databaseConfig: DatabaseConfig;
  const mockMongoose = mongoose as jest.Mocked<typeof mongoose>;
  
  // Mock console methods to suppress test output
  const consoleSpy = {
    log: jest.spyOn(console, 'log').mockImplementation(),
    error: jest.spyOn(console, 'error').mockImplementation(),
  };

  beforeEach(() => {
    databaseConfig = DatabaseConfig.getInstance();
    jest.clearAllMocks();
    consoleSpy.log.mockClear();
    consoleSpy.error.mockClear();
    // Reset connection state
    Object.defineProperty(mongoose.connection, 'readyState', {
      value: 0,
      writable: true,
      configurable: true,
    });
  });

  afterAll(() => {
    consoleSpy.log.mockRestore();
    consoleSpy.error.mockRestore();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = DatabaseConfig.getInstance();
      const instance2 = DatabaseConfig.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('connect', () => {
    it('should connect to database successfully', async () => {
      mockMongoose.connect.mockResolvedValue(mongoose as any);
      
      await databaseConfig.connect();
      
      expect(mockMongoose.connect).toHaveBeenCalledWith(
        expect.stringContaining('mongodb://'),
        expect.objectContaining({
          bufferCommands: true,
          maxPoolSize: 10,
          serverSelectionTimeoutMS: 10000,
        })
      );
      expect(consoleSpy.log).toHaveBeenCalledWith('Connected to MongoDB successfully');
    });

    it('should not reconnect if already connected', async () => {
      Object.defineProperty(mongoose.connection, 'readyState', {
        value: 1, // Connected state
        writable: true,
        configurable: true,
      });
      
      await databaseConfig.connect();
      
      expect(mockMongoose.connect).not.toHaveBeenCalled();
      expect(consoleSpy.log).toHaveBeenCalledWith('Already connected to MongoDB');
    });    it('should handle connection errors gracefully', async () => {
      // Make sure isConnected is false first
      databaseConfig['isConnected'] = false;
      
      const mockError = new Error('Connection failed');
      mockMongoose.connect.mockRejectedValue(mockError);
      
      await expect(databaseConfig.connect()).rejects.toThrow('Connection failed');
      expect(consoleSpy.error).toHaveBeenCalledWith('MongoDB connection error:', mockError);
    });
  });

  describe('disconnect', () => {    it('should disconnect from database', async () => {
      // Set the DatabaseConfig to think it's connected
      (databaseConfig as any).isConnected = true;
      
      mockMongoose.disconnect.mockResolvedValue();
      
      await databaseConfig.disconnect();
      
      expect(mockMongoose.disconnect).toHaveBeenCalled();
      expect(consoleSpy.log).toHaveBeenCalledWith('Disconnected from MongoDB');
    });

    it('should handle disconnect when not connected', async () => {
      Object.defineProperty(mongoose.connection, 'readyState', {
        value: 0, // Disconnected state
        writable: true,
        configurable: true,
      });
      
      await databaseConfig.disconnect();
      
      expect(mockMongoose.disconnect).not.toHaveBeenCalled();
      expect(consoleSpy.log).toHaveBeenCalledWith('Not connected to MongoDB');
    });
  });

  describe('getConnection', () => {
    it('should return mongoose connection', () => {
      const connection = databaseConfig.getConnection();
      expect(connection).toBe(mongoose.connection);
    });
  });
});
