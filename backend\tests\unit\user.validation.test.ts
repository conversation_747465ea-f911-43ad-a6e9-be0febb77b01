import { validateUpdateProfile, validateKYC } from '../../src/common/validation/user.validation';

describe('User Validation', () => {
  describe('validateUpdateProfile', () => {
    it('should validate correct profile update data', () => {
      const validData = {
        firstName: 'John',
        lastName: 'Doe',
        phone: '+2348*********',
        dateOfBirth: '1990-01-01'
      };

      const { error } = validateUpdateProfile(validData);
      expect(error).toBeUndefined();
    });

    it('should validate partial profile updates', () => {
      const validData = {
        firstName: 'John'
      };

      const { error } = validateUpdateProfile(validData);
      expect(error).toBeUndefined();
    });

    it('should validate empty object', () => {
      const validData = {};

      const { error } = validateUpdateProfile(validData);
      expect(error).toBeUndefined();
    });

    it('should reject firstName shorter than 2 characters', () => {
      const invalidData = {
        firstName: 'J'
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"firstName" length must be at least 2 characters long');
    });

    it('should reject firstName longer than 50 characters', () => {
      const invalidData = {
        firstName: 'a'.repeat(51)
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"firstName" length must be less than or equal to 50 characters long');
    });

    it('should reject lastName shorter than 2 characters', () => {
      const invalidData = {
        lastName: 'D'
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"lastName" length must be at least 2 characters long');
    });

    it('should reject lastName longer than 50 characters', () => {
      const invalidData = {
        lastName: 'b'.repeat(51)
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"lastName" length must be less than or equal to 50 characters long');
    });

    it('should reject invalid phone number format', () => {
      const invalidData = {
        phone: '08*********'
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toMatch(/fails to match the required pattern/);
    });

    it('should accept valid phone number formats', () => {
      const validData = {
        phone: '+2348*********'
      };      const { error } = validateUpdateProfile(validData);
      expect(error).toBeUndefined();
    });

    it('should reject invalid date format', () => {
      const invalidData = {
        dateOfBirth: '01/01/1990'
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"dateOfBirth" must be in iso format');
    });

    it('should accept valid ISO date format', () => {
      const validData = {
        dateOfBirth: '1990-01-01'
      };

      const { error } = validateUpdateProfile(validData);
      expect(error).toBeUndefined();
    });

    it('should handle edge case names', () => {
      const validData = {
        firstName: 'Jo',
        lastName: 'Li'
      };

      const { error } = validateUpdateProfile(validData);
      expect(error).toBeUndefined();
    });

    it('should handle maximum length names', () => {
      const validData = {
        firstName: 'a'.repeat(50),
        lastName: 'b'.repeat(50)
      };

      const { error } = validateUpdateProfile(validData);
      expect(error).toBeUndefined();
    });    it('should reject international phone numbers (only Nigerian numbers allowed)', () => {
      const validData = {
        phone: '+14155552671'
      };

      const { error } = validateUpdateProfile(validData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('fails to match the required pattern');
    });

    it('should reject phone numbers with invalid country codes', () => {
      const invalidData = {
        phone: '+999*********0'
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toMatch(/fails to match the required pattern/);
    });

    it('should handle numeric firstName and lastName', () => {
      const invalidData = {
        firstName: '123',
        lastName: '456'
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeUndefined(); // Numbers are converted to strings and are valid
    });

    it('should reject firstName and lastName with special characters', () => {
      const invalidData = {
        firstName: 'John@123',
        lastName: 'Doe#456'
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeUndefined(); // Special characters might be allowed depending on validation rules
    });

    it('should handle whitespace in names', () => {
      const validData = {
        firstName: '  John  ',
        lastName: '  Doe  '
      };

      const { error } = validateUpdateProfile(validData);
      expect(error).toBeUndefined();
    });

    it('should validate future dates of birth', () => {
      const invalidData = {
        dateOfBirth: '2030-01-01'
      };

      const { error } = validateUpdateProfile(invalidData);
      expect(error).toBeUndefined(); // Might be allowed or rejected based on business rules
    });

    it('should validate very old dates of birth', () => {
      const validData = {
        dateOfBirth: '1900-01-01'
      };

      const { error } = validateUpdateProfile(validData);
      expect(error).toBeUndefined();
    });
  });

  describe('validateKYC', () => {
    it('should validate correct KYC data', () => {
      const validData = {
        documentType: 'NIN',
        documentNumber: '***********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos',
          country: 'Nigeria'
        }
      };

      const { error } = validateKYC(validData);
      expect(error).toBeUndefined();
    });

    it('should validate KYC with default country', () => {
      const validData = {
        documentType: 'PASSPORT',
        documentNumber: '*********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '456 Second Street',
          city: 'Abuja',
          state: 'FCT'
        }
      };

      const { error } = validateKYC(validData);
      expect(error).toBeUndefined();
    });

    it('should validate all document types', () => {
      const documentTypes = ['NIN', 'PASSPORT', 'DRIVERS_LICENSE'];
      
      documentTypes.forEach(docType => {
        const validData = {
          documentType: docType,
          documentNumber: '*********',
          documentImage: 'base64encodedimage',
          selfieImage: 'base64encodedselfie',
          address: {
            street: '789 Third Street',
            city: 'Kano',
            state: 'Kano',
            country: 'Nigeria'
          }
        };

        const { error } = validateKYC(validData);
        expect(error).toBeUndefined();
      });
    });

    it('should reject missing documentType', () => {
      const invalidData = {
        documentNumber: '***********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos'
        }
      };

      const { error } = validateKYC(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"documentType" is required');
    });

    it('should reject invalid documentType', () => {
      const invalidData = {
        documentType: 'INVALID',
        documentNumber: '***********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos'
        }
      };

      const { error } = validateKYC(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"documentType" must be one of [NIN, PASSPORT, DRIVERS_LICENSE]');
    });

    it('should reject missing documentNumber', () => {
      const invalidData = {
        documentType: 'NIN',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos'
        }
      };

      const { error } = validateKYC(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"documentNumber" is required');
    });

    it('should reject missing documentImage', () => {
      const invalidData = {
        documentType: 'NIN',
        documentNumber: '***********',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos'
        }
      };

      const { error } = validateKYC(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"documentImage" is required');
    });

    it('should reject missing selfieImage', () => {
      const invalidData = {
        documentType: 'NIN',
        documentNumber: '***********',
        documentImage: 'base64encodedimage',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos'
        }
      };

      const { error } = validateKYC(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"selfieImage" is required');
    });

    it('should reject missing address', () => {
      const invalidData = {
        documentType: 'NIN',
        documentNumber: '***********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie'
      };

      const { error } = validateKYC(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"address" is required');
    });

    it('should reject missing address fields', () => {
      const invalidData = {
        documentType: 'NIN',
        documentNumber: '***********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street'
        }
      };

      const { error } = validateKYC(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"address.city" is required');
    });

    it('should require all address fields except country', () => {
      const invalidData = {
        documentType: 'NIN',
        documentNumber: '***********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          city: 'Lagos',
          state: 'Lagos'
        }
      };

      const { error } = validateKYC(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"address.street" is required');
    });

    it('should handle complete address with all fields', () => {
      const validData = {
        documentType: 'DRIVERS_LICENSE',
        documentNumber: 'DL*********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '999 Last Street',
          city: 'Port Harcourt',
          state: 'Rivers',
          country: 'Nigeria'
        }
      };

      const { error } = validateKYC(validData);
      expect(error).toBeUndefined();
    });

    it('should validate very long document numbers', () => {
      const validData = {
        documentType: 'NIN',
        documentNumber: '***********23456789012345',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos',
          country: 'Nigeria'
        }
      };

      const { error } = validateKYC(validData);
      expect(error).toBeUndefined();
    });

    it('should validate international document numbers', () => {
      const validData = {
        documentType: 'PASSPORT',
        documentNumber: '*********-B',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '456 International Ave',
          city: 'London',
          state: 'England',
          country: 'United Kingdom'
        }
      };

      const { error } = validateKYC(validData);
      expect(error).toBeUndefined();
    });    it('should reject empty string for country field', () => {
      const validData = {
        documentType: 'NIN',
        documentNumber: '***********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos',
          country: ''
        }
      };

      const { error } = validateKYC(validData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('is not allowed to be empty');
    });

    it('should validate very long base64 image strings', () => {
      const longBase64 = 'base64encodedimage'.repeat(100);
      const validData = {
        documentType: 'NIN',
        documentNumber: '***********',
        documentImage: longBase64,
        selfieImage: longBase64,
        address: {
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos',
          country: 'Nigeria'
        }
      };

      const { error } = validateKYC(validData);
      expect(error).toBeUndefined();
    });

    it('should handle special characters in address fields', () => {
      const validData = {
        documentType: 'NIN',
        documentNumber: '***********',
        documentImage: 'base64encodedimage',
        selfieImage: 'base64encodedselfie',
        address: {
          street: '123 Main St. Apt #4B',
          city: 'Port-Harcourt',
          state: 'Rivers State',
          country: 'Nigeria'
        }
      };

      const { error } = validateKYC(validData);
      expect(error).toBeUndefined();
    });
  });
});
