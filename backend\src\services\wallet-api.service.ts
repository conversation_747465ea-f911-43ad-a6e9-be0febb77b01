import { ApiHelperService } from '../helpers/api-helper.service';
import { ApiConfig, ApiResponse } from '../interfaces/common';
import { logger } from '../common/logging/logger';

// Wallet API Request Interfaces
export interface WalletAuthRequest {
  username: string;
  password: string;
  clientId: string;
  clientSecret: string;
}

export interface WalletAuthResponse {
  message: string;
  accessToken: string;
  expiresIn: string;
  refreshToken: string;
  refreshExpiresIn: string;
  jwt: string;
  tokenType: string;
}

export interface WalletOpenRequest {
  bvn: string;
  dateOfBirth: string;
  gender: number;
  lastName: string;
  otherNames: string;
  phoneNo: string;
  transactionTrackingRef: string;
  placeOfBirth: string;
  address: string;
  nationalIdentityNo: string;
  ninUserId?: string;
  nextOfKinPhoneNo: string;
  nextOfKinName: string;
  email: string;
}

export interface WalletOpenResponse {
  status: string;
  responseCode: string;
  message: string;
  data: {
    responseCode: string;
    orderRef: string;
    fullName: string;
    creationMessage: string;
    accountNumber: string;
    ledgerBalance: string;
    availableBalance: string;
    customerID: string;
    mfbcode: string;
    financialDate: string;
    withdrawableAmount: string;
  };
  error?: string;
  fieldErrors?: Record<string, string>;
  jwt: string;
}

export interface WalletTransferRequest {
  accountNo: string;
  narration: string;
  totalAmount: number;
  transactionId: string;
  merchant: {
    isFee: boolean;
    merchantFeeAccount?: string;
    merchantFeeAmount?: string;
  };
  transactionType?: string;
}

export interface WalletOtherBankTransferRequest {
  customer: {
    account: {
      bank: string;
      name: string;
      number: string;
      senderaccountnumber: string;
      sendername: string;
    };
  };
  narration: string;
  order: {
    amount: string;
    country: string;
    currency: string;
    description: string;
  };
  transaction: {
    reference: string;
  };
}

export interface WalletEnquiryRequest {
  accountNo: string;
}

export interface WalletRequeryRequest {
  transactionId: string;
}

export interface WalletUpgradeRequest {
  bvn: string;
  idType: string;
  idNumber: string;
  dateOfBirth: string;
  motherMaidenName: string;
  address: string;
  documentUploadRequest: {
    documentBase64: string;
    documentName: string;
  };
}

/**
 * Wallet API Service for 9PSB Wallet As A Service
 */
export class WalletApiService {
  private apiHelper: ApiHelperService;
  private baseConfig: ApiConfig;
  private accessToken?: string;

  constructor(config: ApiConfig) {
    this.baseConfig = config;
    this.apiHelper = new ApiHelperService(config);
  }

  /**
   * Authenticate with the wallet service
   */
  async authenticate(credentials: WalletAuthRequest): Promise<ApiResponse<WalletAuthResponse>> {
    try {
      logger.info('Authenticating with wallet service', { username: credentials.username });

      const response = await this.apiHelper.makeRequest<WalletAuthResponse>({
        endpoint: '/api/v1/authenticate',
        method: 'POST',
        data: credentials
      });

      if (response.success && response.data) {
        this.accessToken = response.data.accessToken;
        // Update the API helper with the new token
        this.apiHelper.updateConfig({
          ...this.baseConfig,
          headers: {
            ...this.baseConfig.headers,
            'Authorization': `Bearer ${this.accessToken}`
          }
        });
      }

      return response;
    } catch (error: any) {
      logger.error('Wallet authentication failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Authentication failed'
      };
    }
  }

  /**
   * Open a new wallet account
   */
  async openWallet(walletData: WalletOpenRequest): Promise<ApiResponse<WalletOpenResponse>> {
    try {
      logger.info('Opening wallet', { phoneNo: walletData.phoneNo, email: walletData.email });

      return await this.apiHelper.makeRequest<WalletOpenResponse>({
        endpoint: '/api/v1/open_wallet',
        method: 'POST',
        data: walletData,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Wallet opening failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Wallet opening failed'
      };
    }
  }

  /**
   * Debit wallet account
   */
  async debitWallet(transferData: WalletTransferRequest): Promise<ApiResponse> {
    try {
      logger.info('Debiting wallet', { 
        accountNo: transferData.accountNo, 
        amount: transferData.totalAmount,
        transactionId: transferData.transactionId
      });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/debit/transfer',
        method: 'POST',
        data: transferData,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Wallet debit failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Wallet debit failed'
      };
    }
  }

  /**
   * Credit wallet account
   */
  async creditWallet(transferData: WalletTransferRequest): Promise<ApiResponse> {
    try {
      logger.info('Crediting wallet', { 
        accountNo: transferData.accountNo, 
        amount: transferData.totalAmount,
        transactionId: transferData.transactionId
      });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/credit/transfer',
        method: 'POST',
        data: transferData,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Wallet credit failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Wallet credit failed'
      };
    }
  }

  /**
   * Enquiry for other bank accounts
   */
  async otherBankEnquiry(bankCode: string, accountNumber: string): Promise<ApiResponse> {
    try {
      logger.info('Other bank enquiry', { bankCode, accountNumber });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/other_banks_enquiry',
        method: 'POST',
        data: {
          bankCode,
          accountNumber
        },
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Other bank enquiry failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Other bank enquiry failed'
      };
    }
  }

  /**
   * Transfer to other bank accounts
   */
  async transferToOtherBank(transferData: WalletOtherBankTransferRequest): Promise<ApiResponse> {
    try {
      logger.info('Transfer to other bank', { 
        bankCode: transferData.customer.account.bank,
        accountNumber: transferData.customer.account.number,
        amount: transferData.order.amount
      });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/wallet_other_banks',
        method: 'POST',
        data: transferData,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Other bank transfer failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Other bank transfer failed'
      };
    }
  }

  /**
   * Requery wallet transaction
   */
  async walletRequery(requeryData: WalletRequeryRequest): Promise<ApiResponse> {
    try {
      logger.info('Wallet requery', { transactionId: requeryData.transactionId });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/wallet_requery',
        method: 'POST',
        data: requeryData,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Wallet requery failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Wallet requery failed'
      };
    }
  }

  /**
   * Get wallet enquiry/balance
   */
  async walletEnquiry(enquiryData: WalletEnquiryRequest): Promise<ApiResponse> {
    try {
      logger.info('Wallet enquiry', { accountNo: enquiryData.accountNo });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/wallet_enquiry',
        method: 'POST',
        data: enquiryData,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Wallet enquiry failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Wallet enquiry failed'
      };
    }
  }

  /**
   * Get wallet status
   */
  async walletStatus(accountNo: string): Promise<ApiResponse> {
    try {
      logger.info('Wallet status check', { accountNo });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/wallet_status',
        method: 'POST',
        data: { accountNo },
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Wallet status check failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Wallet status check failed'
      };
    }
  }

  /**
   * Get wallet details
   */
  async getWallet(accountNo: string): Promise<ApiResponse> {
    try {
      logger.info('Get wallet details', { accountNo });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/get_wallet',
        method: 'POST',
        data: { accountNo },
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Get wallet failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Get wallet failed'
      };
    }
  }

  /**
   * Get wallet transactions
   */
  async getWalletTransactions(accountNo: string, pageSize?: number, pageNumber?: number): Promise<ApiResponse> {
    try {
      logger.info('Get wallet transactions', { accountNo, pageSize, pageNumber });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/wallet_transactions',
        method: 'POST',
        data: {
          accountNo,
          ...(pageSize && { pageSize }),
          ...(pageNumber && { pageNumber })
        },
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Get wallet transactions failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Get wallet transactions failed'
      };
    }
  }

  /**
   * Upload documents for wallet upgrade
   */
  async walletUpgradeFileUpload(files: FormData): Promise<ApiResponse> {
    try {
      logger.info('Wallet upgrade file upload');

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/wallet_upgrade_file_upload',
        method: 'POST',
        data: files,
        headers: {
          'Accept': 'application/json'
          // Don't set Content-Type for FormData, let browser set it
        }
      });
    } catch (error: any) {
      logger.error('Wallet upgrade file upload failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Wallet upgrade file upload failed'
      };
    }
  }

  /**
   * Upgrade wallet
   */
  async walletUpgrade(upgradeData: WalletUpgradeRequest): Promise<ApiResponse> {
    try {
      logger.info('Wallet upgrade', { bvn: upgradeData.bvn, idType: upgradeData.idType });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/wallet_upgrade',
        method: 'POST',
        data: upgradeData,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Wallet upgrade failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Wallet upgrade failed'
      };
    }
  }

  /**
   * Get upgrade status
   */
  async getUpgradeStatus(transactionRef: string): Promise<ApiResponse> {
    try {
      logger.info('Get upgrade status', { transactionRef });

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/upgrade_status',
        method: 'POST',
        data: { transactionRef },
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Get upgrade status failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Get upgrade status failed'
      };
    }
  }

  /**
   * Open corporate account with file upload
   */
  async openCorporateAccountFileUpload(files: FormData): Promise<ApiResponse> {
    try {
      logger.info('Corporate account file upload');

      return await this.apiHelper.makeRequest({
        endpoint: '/api/v1/open_corporate_account_file_upload',
        method: 'POST',
        data: files,
        headers: {
          'Accept': 'application/json'
        }
      });
    } catch (error: any) {
      logger.error('Corporate account file upload failed', { error: error.message });
      return {
        success: false,
        error: error.message || 'Corporate account file upload failed'
      };
    }
  }
}
