import { APIGatewayProxyEvent } from 'aws-lambda';
import { main } from '../../src/api/v1/transfers/validateRecipient.handler';
import { TransferService } from '../../src/services/transfer.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';
import { TokenPayload } from '../../src/common/security/jwt.service';

// Mock dependencies
jest.mock('../../src/services/transfer.service');
jest.mock('../../src/common/middlewares/auth.middleware');

const mockTransferService = TransferService as jest.MockedClass<typeof TransferService>;
const mockAuthMiddleware = AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>;

describe('Validate Recipient Handler', () => {
  let mockEvent: APIGatewayProxyEvent;
  let mockTransferServiceInstance: jest.Mocked<TransferService>;
  let mockAuthMiddlewareInstance: jest.Mocked<AuthMiddleware>;

  beforeEach(() => {
    jest.clearAllMocks();

    mockEvent = {
      headers: {
        Authorization: 'Bearer valid-token'
      },
      body: JSON.stringify({
        type: 'ZAPWALLET',
        identifier: '<EMAIL>'
      }),
      pathParameters: null,
      queryStringParameters: null,
      httpMethod: 'POST',
      path: '/transfers/validate-recipient',
      resource: '/transfers/validate-recipient',
      requestContext: {} as any,
      isBase64Encoded: false,
      multiValueHeaders: {},
      multiValueQueryStringParameters: null,
      stageVariables: null
    };

    // Mock TransferService instance
    mockTransferServiceInstance = {
      initiateTransfer: jest.fn(),
      processTransfer: jest.fn(),
      validateRecipient: jest.fn(),
      getTransferHistory: jest.fn(),
      getTransferById: jest.fn(),
      cancelTransfer: jest.fn(),
    } as unknown as jest.Mocked<TransferService>;

    mockTransferService.mockImplementation(() => mockTransferServiceInstance);

    // Mock AuthMiddleware instance
    mockAuthMiddlewareInstance = {
      authenticate: jest.fn(),
    } as unknown as jest.Mocked<AuthMiddleware>;

    mockAuthMiddleware.mockImplementation(() => mockAuthMiddlewareInstance);
  });

  describe('successful validation', () => {
    it('should validate ZapWallet recipient successfully', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const mockValidationResult = {
        valid: true,
        recipientName: 'John Doe',
        recipientId: '<EMAIL>',
        fee: 0
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.validateRecipient.mockResolvedValue(mockValidationResult);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockValidationResult
      });
      expect(mockAuthMiddlewareInstance.authenticate).toHaveBeenCalledWith(mockEvent);
      expect(mockTransferServiceInstance.validateRecipient).toHaveBeenCalledWith({
        type: 'ZAPWALLET',
        identifier: '<EMAIL>'
      });
    });

    it('should validate bank recipient successfully', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const bankValidationData = {
        type: 'BANK',
        identifier: '**********',
        bankCode: '044'
      };
      const mockValidationResult = {
        valid: true,
        recipientName: 'Jane Smith',
        recipientId: '**********',
        bankName: 'First Bank of Nigeria',
        fee: 50
      };

      mockEvent.body = JSON.stringify(bankValidationData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.validateRecipient.mockResolvedValue(mockValidationResult);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockValidationResult
      });
      expect(mockTransferServiceInstance.validateRecipient).toHaveBeenCalledWith(bankValidationData);
    });    it('should handle recipient not found', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.validateRecipient.mockRejectedValue(new Error('Recipient not found'));

      const result = await main(mockEvent);      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred during recipient validation');
    });
  });

  describe('validation errors', () => {
    it('should return 400 for missing type', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const invalidData = {
        identifier: '<EMAIL>'
      };

      mockEvent.body = JSON.stringify(invalidData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('"type" is required');
    });

    it('should return 400 for invalid type', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const invalidData = {
        type: 'INVALID',
        identifier: '<EMAIL>'
      };

      mockEvent.body = JSON.stringify(invalidData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('"type" must be one of [ZAPWALLET, BANK]');
    });

    it('should return 400 for missing identifier', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const invalidData = {
        type: 'ZAPWALLET'
      };

      mockEvent.body = JSON.stringify(invalidData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('"identifier" is required');
    });

    it('should return 400 for missing bankCode when type is BANK', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const invalidData = {
        type: 'BANK',
        identifier: '**********'
      };

      mockEvent.body = JSON.stringify(invalidData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('"bankCode" is required');
    });
  });

  describe('authentication errors', () => {
    it('should return 500 for missing authorization', async () => {
      mockEvent.headers = {};

      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {        throw new Error('Missing or invalid authorization header');
      });      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('An error occurred during recipient validation');
    });

    it('should return 500 for invalid token', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Invalid or expired token');
      });      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('An error occurred during recipient validation');
    });
  });

  describe('service errors', () => {
    it('should handle validation service errors', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);      mockTransferServiceInstance.validateRecipient.mockRejectedValue(new Error('Bank API unavailable'));      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('An error occurred during recipient validation');
    });

    it('should handle network errors', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.validateRecipient.mockRejectedValue(new Error('Network timeout'));      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('An error occurred during recipient validation');
    });
  });

  describe('edge cases', () => {
    it('should handle invalid JSON in request body', async () => {
      mockEvent.body = 'invalid json';      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('An error occurred during recipient validation');
    });

    it('should handle empty request body', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };      mockEvent.body = '';
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);      const result = await main(mockEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('"type" is required');
    });

    it('should handle null request body', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };      mockEvent.body = null;
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);      const result = await main(mockEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error.message).toBe('"type" is required');
    });

    it('should handle email identifier format', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const emailData = {
        type: 'ZAPWALLET',
        identifier: '<EMAIL>'
      };
      const mockValidationResult = {
        valid: true,
        recipientName: 'Alice Johnson',
        recipientId: '<EMAIL>',
        fee: 0
      };

      mockEvent.body = JSON.stringify(emailData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.validateRecipient.mockResolvedValue(mockValidationResult);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockValidationResult
      });
    });

    it('should handle phone identifier format', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const phoneData = {
        type: 'ZAPWALLET',
        identifier: '+2348012345678'
      };
      const mockValidationResult = {
        valid: true,
        recipientName: 'Bob Wilson',
        recipientId: '+2348012345678',
        fee: 0
      };

      mockEvent.body = JSON.stringify(phoneData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.validateRecipient.mockResolvedValue(mockValidationResult);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockValidationResult
      });
    });
  });
});
