import { v4 as uuidv4 } from 'uuid';
import { Notification } from '../interfaces/services';

export class NotificationService {
  // In-memory store for demonstration. Replace with PostgreSQL logic.
  private static notifications: Notification[] = [];

  async getNotifications(userId: string, queryParams: any) {
    const { page = 1, limit = 20, read } = queryParams;
    
    let userNotifications = NotificationService.notifications.filter(n => n.userId === userId);
    
    // Filter by read status if specified
    if (read !== undefined) {
      const isRead = read === 'true';
      userNotifications = userNotifications.filter(n => n.read === isRead);
    }
    
    // Sort by creation date (newest first)
    userNotifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    const total = userNotifications.length;
    const pages = Math.ceil(total / limit);
    const offset = (page - 1) * limit;
    const notifications = userNotifications.slice(offset, offset + parseInt(limit));
    
    const unreadCount = NotificationService.notifications.filter(
      n => n.userId === userId && !n.read
    ).length;
    
    return {
      notifications,
      pagination: {
        total,
        pages,
        currentPage: parseInt(page),
        limit: parseInt(limit)
      },
      unreadCount
    };
  }

  async markAsRead(userId: string, notificationId: string) {
    const notification = NotificationService.notifications.find(
      n => n.id === notificationId && n.userId === userId
    );
    
    if (!notification) {
      throw new Error('Notification not found');
    }
    
    if (!notification.read) {
      notification.read = true;
      notification.readAt = new Date().toISOString();
    }
    
    return notification;
  }

  async markAllAsRead(userId: string) {
    const userNotifications = NotificationService.notifications.filter(
      n => n.userId === userId && !n.read
    );
    
    const now = new Date().toISOString();
    let markedCount = 0;
    
    userNotifications.forEach(notification => {
      notification.read = true;
      notification.readAt = now;
      markedCount++;
    });
    
    return {
      markedCount,
      message: `${markedCount} notifications marked as read`
    };
  }

  // Helper method to create notifications (used by other services)
  async createNotification(notificationData: Omit<Notification, 'id' | 'read' | 'createdAt'>): Promise<Notification> {
    const notification: Notification = {
      ...notificationData,
      id: uuidv4(),
      read: false,
      createdAt: new Date().toISOString()
    };
    
    NotificationService.notifications.push(notification);
    
    // TODO: Send push notification, email, SMS based on user preferences
    
    return notification;
  }

  // Sample method to create common notification types
  async createTransactionNotification(userId: string, transactionType: string, amount: number, reference: string) {
    return this.createNotification({
      userId,
      title: `Transaction ${transactionType}`,
      message: `Your ${transactionType.toLowerCase()} of ₦${amount.toLocaleString()} has been processed. Reference: ${reference}`,
      type: 'TRANSACTION',
      priority: 'MEDIUM',
      data: { transactionType, amount, reference }
    });
  }

  async createSecurityNotification(userId: string, action: string, deviceInfo?: any) {
    return this.createNotification({
      userId,
      title: 'Security Alert',
      message: `${action} detected on your account${deviceInfo ? ` from ${deviceInfo.platform}` : ''}`,
      type: 'SECURITY',
      priority: 'HIGH',
      data: { action, deviceInfo }
    });
  }
}
