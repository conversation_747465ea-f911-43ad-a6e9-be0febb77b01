import { APIGatewayProxyEvent } from 'aws-lambda';
import { main as loginHandler } from '../../src/api/v1/auth/login.handler';
import { AuthService } from '../../src/services/auth.service';
import { DatabaseConfig } from '../../src/config/database';

// Mock dependencies
jest.mock('../../src/services/auth.service');
jest.mock('../../src/config/database');

const mockDatabaseConfig = {
  getInstance: jest.fn().mockReturnValue({
    connect: jest.fn().mockResolvedValue(undefined)
  })
};

(DatabaseConfig as any) = mockDatabaseConfig;

describe('Login Handler', () => {
  let mockAuthService: jest.Mocked<AuthService>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockAuthService = {
      login: jest.fn(),
      register: jest.fn(),
      refresh: jest.fn()
    } as any;
    (AuthService as jest.MockedClass<typeof AuthService>).mockImplementation(() => mockAuthService);
  });

  const createMockEvent = (body: any): Partial<APIGatewayProxyEvent> => ({
    body: JSON.stringify(body),
    headers: {},
    multiValueHeaders: {},
    httpMethod: 'POST',
    isBase64Encoded: false,
    path: '/api/v1/auth/login',
    pathParameters: null,
    queryStringParameters: null,
    multiValueQueryStringParameters: null,
    stageVariables: null,
    requestContext: {
      requestId: 'test-request-id',
      stage: 'test',
      resourceId: 'test-resource',
      httpMethod: 'POST',
      resourcePath: '/api/v1/auth/login',
      path: '/api/v1/auth/login',
      accountId: 'test-account',
      apiId: 'test-api',
      protocol: 'HTTP/1.1',
      requestTime: '01/Jan/2025:00:00:00 +0000',
      requestTimeEpoch: **********,
      identity: {
        accessKey: null,
        accountId: null,
        apiKey: null,
        apiKeyId: null,
        caller: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '127.0.0.1',
        user: null,
        userAgent: 'test-user-agent',
        userArn: null,
        clientCert: null
      },
      authorizer: null
    }
  });
  describe('successful login', () => {
    it('should login user successfully', async () => {
      const mockResult = {
        user: { 
          id: 'user123', 
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phone: '+*************',
          kycVerified: false,
          status: 'ACTIVE' as const,
          role: 'USER' as const
        },
        accessToken: 'access-token-123',
        refreshToken: 'refresh-token-123'
      };
      
      mockAuthService.login.mockResolvedValue(mockResult);
      
      const mockEvent = createMockEvent({
        email: '<EMAIL>',
        password: 'password123',
        deviceInfo: {
          deviceId: 'device123',
          platform: 'web',
          appVersion: '1.0.0'
        }
      });

      const result = await loginHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data).toEqual(mockResult);
      expect(mockAuthService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        deviceInfo: {
          deviceId: 'device123',
          platform: 'web',
          appVersion: '1.0.0'
        }
      });
    });
  });

  describe('validation errors', () => {
    it('should return 400 for invalid request body', async () => {
      const mockEvent = createMockEvent({
        email: 'invalid-email'
      });

      const result = await loginHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for missing required fields', async () => {
      const mockEvent = createMockEvent({
        email: '<EMAIL>'
        // missing password and deviceInfo
      });

      const result = await loginHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid email format', async () => {
      const mockEvent = createMockEvent({
        email: 'invalid-email',
        password: 'password123',
        deviceInfo: {
          deviceId: 'device123',
          platform: 'web',
          appVersion: '1.0.0'
        }
      });

      const result = await loginHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('authentication errors', () => {
    it('should handle login service errors', async () => {
      mockAuthService.login.mockRejectedValue(new Error('Invalid credentials'));
      
      const mockEvent = createMockEvent({
        email: '<EMAIL>',
        password: 'wrongpassword',
        deviceInfo: {
          deviceId: 'device123',
          platform: 'web',
          appVersion: '1.0.0'
        }
      });

      const result = await loginHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
    });
  });

  describe('edge cases', () => {
    it('should handle empty request body', async () => {
      const mockEvent = createMockEvent({});

      const result = await loginHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });

    it('should handle null request body', async () => {
      const mockEvent = {
        ...createMockEvent({}),
        body: null
      };

      const result = await loginHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });    it('should handle undefined request body', async () => {
      const mockEvent = {
        ...createMockEvent({}),
        body: null
      };

      const result = await loginHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });
});
