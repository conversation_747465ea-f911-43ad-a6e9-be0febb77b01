import { WalletModel, IWallet } from '../models/wallet.schema';
import { DatabaseConfig } from '../config/database';
import { Wallet } from '../interfaces/models';

export class WalletRepository {
  constructor() {
    // Only ensure database connection if not in test mode
    if (process.env.NODE_ENV !== 'test') {
      DatabaseConfig.getInstance().connect();
    }
  }
  async create(walletData: {
    userId: string;
    accountNumber: string;
    customerID?: string;
    accountName?: string;
    status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'CLOSED';
  }): Promise<Wallet> {
    const newWallet = new WalletModel({
      ...walletData,
      available: 0,
      pending: 0,
      currency: 'NGN',
      status: walletData.status || 'ACTIVE',
      limits: {
        daily: 100000,
        monthly: 500000,
        single: 50000,
        dailyTransfer: 1000000, // 1M NGN
        dailyTransferUsed: 0,
        singleTransferMax: 500000 // 500K NGN
      }
    });
    
    const savedWallet = await newWallet.save();
    return savedWallet.toJSON() as Wallet;
  }

  async findByUserId(userId: string): Promise<Wallet | null> {
    const wallet = await WalletModel.findOne({ userId });
    return wallet ? (wallet.toJSON() as Wallet) : null;
  }

  async findById(id: string): Promise<Wallet | null> {
    const wallet = await WalletModel.findById(id);
    return wallet ? (wallet.toJSON() as Wallet) : null;
  }

  async updateBalance(userId: string, amount: number, isPending: boolean = false): Promise<Wallet> {
    const updateField = isPending ? 'pending' : 'available';
    const wallet = await WalletModel.findOneAndUpdate(
      { userId },
      { $inc: { [updateField]: amount } },
      { new: true, runValidators: true }
    );

    if (!wallet) {
      throw new Error('Wallet not found');
    }

    return wallet.toJSON() as Wallet;
  }

  async transferFunds(fromUserId: string, toUserId: string, amount: number): Promise<{
    fromWallet: Wallet;
    toWallet: Wallet;
  }> {
    const session = await WalletModel.startSession();
    
    try {
      await session.withTransaction(async () => {
        // Debit from sender
        const fromWallet = await WalletModel.findOneAndUpdate(
          { userId: fromUserId, available: { $gte: amount } },
          { $inc: { available: -amount } },
          { new: true, session }
        );

        if (!fromWallet) {
          throw new Error('Insufficient funds or wallet not found');
        }

        // Credit to receiver
        const toWallet = await WalletModel.findOneAndUpdate(
          { userId: toUserId },
          { $inc: { available: amount } },
          { new: true, session }
        );

        if (!toWallet) {
          throw new Error('Recipient wallet not found');
        }

        return { fromWallet, toWallet };
      });

      // Fetch updated wallets
      const [fromWallet, toWallet] = await Promise.all([
        this.findByUserId(fromUserId),
        this.findByUserId(toUserId)
      ]);

      if (!fromWallet || !toWallet) {
        throw new Error('Failed to fetch updated wallets');
      }

      return { fromWallet, toWallet };
    } finally {
      await session.endSession();
    }
  }

  async movePendingToAvailable(userId: string, amount: number): Promise<Wallet> {
    const wallet = await WalletModel.findOneAndUpdate(
      { userId, pending: { $gte: amount } },
      { 
        $inc: { 
          pending: -amount,
          available: amount 
        } 
      },
      { new: true, runValidators: true }
    );

    if (!wallet) {
      throw new Error('Insufficient pending funds or wallet not found');
    }

    return wallet.toJSON() as Wallet;
  }

  async updateDailyTransferUsed(userId: string, amount: number): Promise<Wallet> {
    const wallet = await WalletModel.findOneAndUpdate(
      { userId },
      { $inc: { 'limits.dailyTransferUsed': amount } },
      { new: true, runValidators: true }
    );

    if (!wallet) {
      throw new Error('Wallet not found');
    }

    return wallet.toJSON() as Wallet;
  }

  async resetDailyLimits(): Promise<number> {
    const result = await WalletModel.updateMany(
      {},
      { $set: { 'limits.dailyTransferUsed': 0 } }
    );

    return result.modifiedCount;
  }

  async updateLimits(userId: string, limits: Partial<Wallet['limits']>): Promise<Wallet> {
    const updateData: any = {};
    
    if (limits.dailyTransfer !== undefined) {
      updateData['limits.dailyTransfer'] = limits.dailyTransfer;
    }
    if (limits.singleTransferMax !== undefined) {
      updateData['limits.singleTransferMax'] = limits.singleTransferMax;
    }
    if (limits.dailyTransferUsed !== undefined) {
      updateData['limits.dailyTransferUsed'] = limits.dailyTransferUsed;
    }

    const wallet = await WalletModel.findOneAndUpdate(
      { userId },
      { $set: updateData },
      { new: true, runValidators: true }
    );

    if (!wallet) {
      throw new Error('Wallet not found');
    }

    return wallet.toJSON() as Wallet;
  }
  async getTotalBalance(): Promise<{
    totalAvailable: number;
    totalPending: number;
    totalWallets: number;
  }> {
    const pipeline: any[] = [
      {
        $group: {
          _id: null,
          totalAvailable: { $sum: '$available' },
          totalPending: { $sum: '$pending' },
          totalWallets: { $sum: 1 }
        }
      }
    ];

    const result = await WalletModel.aggregate(pipeline);
    
    if (result.length === 0) {
      return {
        totalAvailable: 0,
        totalPending: 0,
        totalWallets: 0
      };
    }

    return result[0];
  }

  async getTopWalletsByBalance(limit: number = 10): Promise<Array<{
    userId: string;
    available: number;
    pending: number;
    total: number;
  }>> {
    const pipeline: any[] = [
      {
        $addFields: {
          total: { $add: ['$available', '$pending'] }
        }
      },
      { $sort: { total: -1 } },
      { $limit: limit },
      {
        $project: {
          userId: 1,
          available: 1,
          pending: 1,
          total: 1,
          _id: 0
        }
      }
    ];

    return await WalletModel.aggregate(pipeline);
  }

  async delete(userId: string): Promise<boolean> {
    const result = await WalletModel.findOneAndDelete({ userId });
    return !!result;
  }
}
