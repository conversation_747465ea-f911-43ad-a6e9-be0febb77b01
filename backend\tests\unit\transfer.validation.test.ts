import { validateRecipient, validateTransfer } from '../../src/common/validation/transfer.validation';

describe('Transfer Validation', () => {
  describe('validateRecipient', () => {
    it('should validate correct recipient data for ZAPWALLET', () => {
      const validData = {
        type: 'ZAPWALLET',
        identifier: 'user123@zapwallet'
      };

      const { error } = validateRecipient(validData);
      expect(error).toBeUndefined();
    });

    it('should validate correct recipient data for BANK', () => {
      const validData = {
        type: 'BANK',
        identifier: '**********',
        bankCode: '044'
      };

      const { error } = validateRecipient(validData);
      expect(error).toBeUndefined();
    });

    it('should reject missing type', () => {
      const invalidData = {
        identifier: 'user123@zapwallet'
      };

      const { error } = validateRecipient(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"type" is required');
    });

    it('should reject invalid type', () => {
      const invalidData = {
        type: 'INVALID',
        identifier: 'user123@zapwallet'
      };

      const { error } = validateRecipient(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"type" must be one of [ZAPWALLET, BANK]');
    });

    it('should reject missing identifier', () => {
      const invalidData = {
        type: 'ZAPWALLET'
      };

      const { error } = validateRecipient(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"identifier" is required');
    });

    it('should require bankCode for BANK type', () => {
      const invalidData = {
        type: 'BANK',
        identifier: '**********'
      };

      const { error } = validateRecipient(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"bankCode" is required');
    });

    it('should not require bankCode for ZAPWALLET type', () => {
      const validData = {
        type: 'ZAPWALLET',
        identifier: 'user123@zapwallet'
      };

      const { error } = validateRecipient(validData);
      expect(error).toBeUndefined();
    });

    it('should allow optional bankCode for ZAPWALLET type', () => {
      const validData = {
        type: 'ZAPWALLET',
        identifier: 'user123@zapwallet',
        bankCode: '044'
      };

      const { error } = validateRecipient(validData);
      expect(error).toBeUndefined();
    });
  });

  describe('validateTransfer', () => {
    it('should validate correct transfer data', () => {
      const validData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        amount: 1000,
        description: 'Payment for services',
        pin: '1234'
      };

      const { error } = validateTransfer(validData);
      expect(error).toBeUndefined();
    });

    it('should validate transfer without description', () => {
      const validData = {
        recipientType: 'BANK',
        recipientId: 'acc123',
        amount: 5000,
        pin: '5678'
      };

      const { error } = validateTransfer(validData);
      expect(error).toBeUndefined();
    });

    it('should reject missing recipientType', () => {
      const invalidData = {
        recipientId: 'user123',
        amount: 1000,
        pin: '1234'
      };

      const { error } = validateTransfer(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"recipientType" is required');
    });

    it('should reject invalid recipientType', () => {
      const invalidData = {
        recipientType: 'INVALID',
        recipientId: 'user123',
        amount: 1000,
        pin: '1234'
      };

      const { error } = validateTransfer(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"recipientType" must be one of [ZAPWALLET, BANK]');
    });

    it('should reject missing recipientId', () => {
      const invalidData = {
        recipientType: 'ZAPWALLET',
        amount: 1000,
        pin: '1234'
      };

      const { error } = validateTransfer(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"recipientId" is required');
    });

    it('should reject missing amount', () => {
      const invalidData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        pin: '1234'
      };

      const { error } = validateTransfer(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"amount" is required');
    });

    it('should reject amount below minimum', () => {
      const invalidData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        amount: 5,
        pin: '1234'
      };

      const { error } = validateTransfer(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"amount" must be greater than or equal to 10');
    });

    it('should reject missing pin', () => {
      const invalidData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        amount: 1000
      };

      const { error } = validateTransfer(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"pin" is required');
    });

    it('should reject pin with wrong length', () => {
      const invalidData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        amount: 1000,
        pin: '123'
      };

      const { error } = validateTransfer(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"pin" length must be 4 characters long');
    });

    it('should reject description longer than 255 characters', () => {
      const invalidData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        amount: 1000,
        description: 'a'.repeat(256),
        pin: '1234'
      };

      const { error } = validateTransfer(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"description" length must be less than or equal to 255 characters long');
    });

    it('should handle edge case amounts', () => {
      const validData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        amount: 10,
        pin: '1234'
      };

      const { error } = validateTransfer(validData);
      expect(error).toBeUndefined();
    });

    it('should handle large amounts', () => {
      const validData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        amount: 10000000,
        pin: '1234'
      };

      const { error } = validateTransfer(validData);
      expect(error).toBeUndefined();
    });

    it('should reject non-numeric amount', () => {
      const invalidData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        amount: 'invalid',
        pin: '1234'
      };

      const { error } = validateTransfer(invalidData);
      expect(error).toBeDefined();
      expect(error?.details[0].message).toBe('"amount" must be a number');
    });

    it('should handle both ZAPWALLET and BANK recipient types', () => {
      const zapwalletData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'user123',
        amount: 1000,
        pin: '1234'
      };

      const bankData = {
        recipientType: 'BANK',
        recipientId: 'acc456',
        amount: 2000,
        pin: '5678'
      };

      expect(validateTransfer(zapwalletData).error).toBeUndefined();
      expect(validateTransfer(bankData).error).toBeUndefined();
    });
  });
});
