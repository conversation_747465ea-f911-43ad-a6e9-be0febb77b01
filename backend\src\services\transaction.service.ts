import { v4 as uuidv4 } from 'uuid';
import { Transaction } from '../interfaces/models';

export class TransactionService {
  // In-memory store for demonstration. Replace with PostgreSQL logic.
  private static transactions: Transaction[] = [];

  async getTransactions(userId: string, queryParams: any) {
    const {
      page = 1,
      limit = 20,
      type = 'ALL',
      status = 'ALL',
      startDate,
      endDate,
      category
    } = queryParams;

    let userTransactions = TransactionService.transactions.filter(t => t.userId === userId);

    // Apply filters
    if (type !== 'ALL') {
      userTransactions = userTransactions.filter(t => t.type === type);
    }
    if (status !== 'ALL') {
      userTransactions = userTransactions.filter(t => t.status === status);
    }
    if (startDate) {
      userTransactions = userTransactions.filter(t => t.createdAt >= startDate);
    }
    if (endDate) {
      userTransactions = userTransactions.filter(t => t.createdAt <= endDate);
    }
    if (category) {
      userTransactions = userTransactions.filter(t => t.category === category);
    }

    const total = userTransactions.length;
    const pages = Math.ceil(total / limit);
    const offset = (page - 1) * limit;
    const transactions = userTransactions.slice(offset, offset + parseInt(limit));

    return {
      transactions,
      pagination: {
        total,
        pages,
        currentPage: parseInt(page),
        limit: parseInt(limit)
      }
    };
  }

  async getTransactionById(userId: string, transactionId: string) {
    const transaction = TransactionService.transactions.find(
      t => t.id === transactionId && t.userId === userId
    );
    
    if (!transaction) {
      throw new Error('Transaction not found');
    }
    
    return transaction;
  }

  async getTransactionStats(userId: string) {
    const userTransactions = TransactionService.transactions.filter(t => t.userId === userId);
    
    const totalTransactions = userTransactions.length;
    const totalCredit = userTransactions
      .filter(t => t.type === 'CREDIT' && t.status === 'COMPLETED')
      .reduce((sum, t) => sum + t.amount, 0);
    const totalDebit = userTransactions
      .filter(t => t.type === 'DEBIT' && t.status === 'COMPLETED')
      .reduce((sum, t) => sum + t.amount, 0);
    const netBalance = totalCredit - totalDebit;

    // Generate monthly stats for the last 12 months
    const monthlyStats = this.generateMonthlyStats(userTransactions);

    return {
      totalTransactions,
      totalCredit,
      totalDebit,
      netBalance,
      monthlyStats
    };
  }

  private generateMonthlyStats(transactions: Transaction[]) {
    const monthlyData: { [key: string]: { credit: number; debit: number; count: number } } = {};
    
    transactions.forEach(t => {
      const month = t.createdAt.substring(0, 7); // YYYY-MM format
      if (!monthlyData[month]) {
        monthlyData[month] = { credit: 0, debit: 0, count: 0 };
      }
      
      monthlyData[month].count++;
      if (t.type === 'CREDIT' && t.status === 'COMPLETED') {
        monthlyData[month].credit += t.amount;
      } else if (t.type === 'DEBIT' && t.status === 'COMPLETED') {
        monthlyData[month].debit += t.amount;
      }
    });

    return Object.entries(monthlyData).map(([month, data]) => ({
      month,
      ...data
    }));
  }

  // Helper method to create a transaction (used by other services)
  async createTransaction(transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<Transaction> {
    const transaction: Transaction = {
      ...transactionData,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    TransactionService.transactions.push(transaction);
    return transaction;
  }
}
