import { Types } from 'mongoose';
import { WalletRepository } from '../../src/repositories/wallet.repository';
import { Wallet } from '../../src/interfaces/models';

// Helper function to generate test wallet data
const createTestWalletData = (userId: string) => ({
  userId,
  accountNumber: `ZW${Date.now()}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
  status: 'ACTIVE' as const
});

describe('WalletRepository', () => {
  let walletRepository: WalletRepository;

  beforeEach(() => {
    walletRepository = new WalletRepository();
  });

  describe('create', () => {
    it('should create a new wallet successfully', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      const wallet = await walletRepository.create(createWalletDto);

      expect(wallet).toBeDefined();
      expect(wallet.userId.toString()).toBe(userId);
      expect(wallet.currency).toBe('NGN');
      expect(wallet.available).toBe(0);
      expect(wallet.pending).toBe(0);
      expect(wallet.limits).toBeDefined();
      expect(wallet.limits.dailyTransfer).toBe(1000000);
      expect(wallet.createdAt).toBeDefined();
      expect(wallet.updatedAt).toBeDefined();
    });

    it('should create wallet with default values', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      const wallet = await walletRepository.create(createWalletDto);

      expect(wallet.available).toBe(0);
      expect(wallet.pending).toBe(0);
      expect(wallet.currency).toBe('NGN');
    });
  });

  describe('findByUserId', () => {
    it('should find wallet by user id', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      await walletRepository.create(createWalletDto);
      const wallet = await walletRepository.findByUserId(userId);

      expect(wallet).toBeDefined();
      expect(wallet?.userId.toString()).toBe(userId);
      expect(wallet?.available).toBe(0);
    });

    it('should return null for non-existent user id', async () => {
      const nonExistentUserId = new Types.ObjectId().toString();
      const wallet = await walletRepository.findByUserId(nonExistentUserId);

      expect(wallet).toBeNull();
    });
  });

  describe('findById', () => {
    it('should find wallet by id', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      const createdWallet = await walletRepository.create(createWalletDto);
      const wallet = await walletRepository.findById(createdWallet.id);

      expect(wallet).toBeDefined();
      expect(wallet?.id).toStrictEqual(createdWallet.id);
      expect(wallet?.available).toBe(0);
    });

    it('should return null for non-existent wallet id', async () => {
      const nonExistentWalletId = new Types.ObjectId().toString();
      const wallet = await walletRepository.findById(nonExistentWalletId);

      expect(wallet).toBeNull();
    });
  });

  describe('updateBalance', () => {
    it('should update available balance', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      await walletRepository.create(createWalletDto);
      const updatedWallet = await walletRepository.updateBalance(userId, 1500, false);

      expect(updatedWallet).toBeDefined();
      expect(updatedWallet.available).toBe(1500);
      expect(updatedWallet.pending).toBe(0);
    });

    it('should update pending balance', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      await walletRepository.create(createWalletDto);
      const updatedWallet = await walletRepository.updateBalance(userId, 500, true);

      expect(updatedWallet).toBeDefined();
      expect(updatedWallet.available).toBe(0);
      expect(updatedWallet.pending).toBe(500);
    });
  });

  describe('listAll', () => {
    it('should return all wallets', async () => {
      const userId1 = new Types.ObjectId().toString();
      const userId2 = new Types.ObjectId().toString();

      await walletRepository.create(createTestWalletData(userId1));
      await walletRepository.create(createTestWalletData(userId2));

      const wallets = await walletRepository.listAll();

      expect(wallets).toBeDefined();
      expect(wallets.length).toBeGreaterThanOrEqual(2);
    });

    it('should return empty array when no wallets exist', async () => {
      const userId1 = new Types.ObjectId().toString();
      const userId2 = new Types.ObjectId().toString();

      await walletRepository.create(createTestWalletData(userId1));
      await walletRepository.create(createTestWalletData(userId2));

      const wallets = await walletRepository.listAll();

      expect(wallets).toBeDefined();
      expect(Array.isArray(wallets)).toBe(true);
    });
  });

  describe('delete', () => {
    it('should delete wallet by user id', async () => {
      const userId = new Types.ObjectId().toString();
      await walletRepository.create(createTestWalletData(userId));

      const deleted = await walletRepository.delete(userId);

      expect(deleted).toBe(true);
      const wallet = await walletRepository.findByUserId(userId);
      expect(wallet).toBeNull();
    });

    it('should return false for non-existent user id', async () => {
      const nonExistentUserId = new Types.ObjectId().toString();
      const deleted = await walletRepository.delete(nonExistentUserId);

      expect(deleted).toBe(false);
    });
  });

  describe('findByAccountNumber', () => {
    it('should find wallet by account number', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      await walletRepository.create(createWalletDto);
      await walletRepository.create(createTestWalletData(new Types.ObjectId().toString()));

      const wallets = await walletRepository.findByAccountNumber(createWalletDto.accountNumber);

      expect(wallets).toBeDefined();
      expect(wallets.length).toBeGreaterThanOrEqual(1);
    });

    it('should return empty array for non-existent account number', async () => {
      const userId1 = new Types.ObjectId().toString();
      const userId2 = new Types.ObjectId().toString();

      await walletRepository.create(createTestWalletData(userId1));
      await walletRepository.create(createTestWalletData(userId2));

      const wallets = await walletRepository.findByAccountNumber('non-existent-account');

      expect(wallets).toBeDefined();
      expect(wallets.length).toBe(0);
    });
  });

  describe('calculateTotalBalance', () => {
    it('should calculate total balance for all wallets', async () => {
      const userId = new Types.ObjectId().toString();
      await walletRepository.create(createTestWalletData(userId));

      const totalBalance = await walletRepository.calculateTotalBalance();

      expect(totalBalance).toBeDefined();
      expect(typeof totalBalance).toBe('number');
    });

    it('should return 0 when no wallets exist', async () => {
      const userId = new Types.ObjectId().toString();
      await walletRepository.create(createTestWalletData(userId));

      const totalBalance = await walletRepository.calculateTotalBalance();

      expect(totalBalance).toBeDefined();
      expect(typeof totalBalance).toBe('number');
      expect(totalBalance).toBeGreaterThanOrEqual(0);
    });
  });
});
