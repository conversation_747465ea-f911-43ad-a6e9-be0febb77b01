import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { TransactionService } from '../../../services/transaction.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const transactionId = event.pathParameters?.id;
    if (!transactionId) {
      return errorResponse(400, 'MISSING_PARAMETER', 'Transaction ID is required');
    }
    
    const transactionService = new TransactionService();
    const transaction = await transactionService.getTransactionById(user.userId, transactionId);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: transaction })
    };
  } catch (err: any) {
    logger.error('Get transaction error', { error: err.message });
    return errorResponse(404, 'NOT_FOUND', err.message);
  }
};
