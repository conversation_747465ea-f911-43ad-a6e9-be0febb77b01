import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { WalletService } from '../../../services/wallet.service';
import { validateFundWallet } from '../../../common/validation/wallet.validation';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Parse and validate request body first
    let body;
    try {
      body = JSON.parse(event.body || '{}');
    } catch (parseError) {
      logger.warn('Invalid JSON in request body');
      return errorResponse(400, 'VALIDATION_ERROR', 'Invalid JSON in request body');
    }
    
    const { error } = validateFundWallet(body);
    if (error) {
      logger.warn('Validation failed', { details: error.details });
      return errorResponse(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    
    // Authenticate user after validation
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);const walletService = new WalletService();
    const fundingResult = await walletService.fundWallet(user.userId, body);
    
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'OPTIONS,POST,GET,PUT,DELETE'
      },
      body: JSON.stringify({ success: true, data: fundingResult })
    };
  } catch (err: any) {
    logger.error('Fund wallet error', { error: err.message });
    if (err.message.includes('authorization') || err.message.includes('token')) {
      return errorResponse(401, 'UNAUTHORIZED', err.message);
    }
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during wallet funding');
  }
};
