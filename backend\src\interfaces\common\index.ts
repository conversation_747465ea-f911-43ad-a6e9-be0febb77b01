import { APIGatewayProxyEvent } from 'aws-lambda';

/**
 * JWT related interfaces
 */
export interface TokenPayload {
  userId: string;
  email: string;
  role?: string;
}

/**
 * Authentication middleware interfaces
 */
export interface AuthenticatedEvent extends APIGatewayProxyEvent {
  user?: TokenPayload;
}

/**
 * Third-party API integration interfaces
 */
export interface ApiConfig {
  baseUrl: string;
  apiKey: string;
  secretKey?: string;
  timeout?: number;
  retryAttempts?: number;
  headers?: Record<string, string>;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
  reference?: string;
}

export interface ApiRequest {
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  data?: any;
  params?: Record<string, string>;
  headers?: Record<string, string>;
  timeout?: number;
}

export interface RetryConfig {
  attempts: number;
  delay: number;
  backoff: number;
}

/**
 * Transfer API interfaces
 */
export interface TransferApiRequest {
  amount: number;
  recipientType: 'BANK' | 'WALLET';
  recipientAccount: string;
  recipientBank?: string;
  narration: string;
  reference: string;
  currency?: string;
}

export interface TransferApiResponse {
  status: 'SUCCESS' | 'FAILED' | 'PENDING';
  reference: string;
  fee: number;
  recipientName?: string;
  sessionId?: string;
  estimatedTime?: string;
}

export interface BankValidationRequest {
  accountNumber: string;
  bankCode: string;
}

export interface BankValidationResponse {
  accountName: string;
  accountNumber: string;
  bankCode: string;
  bankName: string;
}

/**
 * Bill payment API interfaces
 */
export interface BillValidationRequest {
  billerId: string;
  customerCode: string;
  paymentCode?: string;
}

export interface BillValidationResponse {
  customerName: string;
  customerCode: string;
  billerId: string;
  billerName: string;
  amount?: number;
  dueDate?: string;
  currency?: string;
}

export interface BillPaymentRequest {
  billerId: string;
  customerCode: string;
  amount: number;
  reference: string;
  paymentCode?: string;
}

export interface BillPaymentResponse {
  status: 'SUCCESS' | 'FAILED' | 'PENDING';
  reference: string;
  transactionId: string;
  fee: number;
  token?: string;
  units?: string;
}

/**
 * Service provider interfaces
 */
export interface ServiceProvider {
  id: string;
  name: string;
  type: 'TRANSFER' | 'BILL_PAYMENT';
  config: ApiConfig;
  isActive: boolean;
  priority: number;
}

export interface ProviderCapability {
  transfers: {
    bankTransfer: boolean;
    walletTransfer: boolean;
    internationalTransfer: boolean;
  };
  bills: {
    airtime: boolean;
    data: boolean;
    electricity: boolean;
    cableTv: boolean;
    internet: boolean;
  };
}
