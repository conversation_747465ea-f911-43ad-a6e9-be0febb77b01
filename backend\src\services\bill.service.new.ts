import { v4 as uuidv4 } from 'uuid';
import { TransactionService } from './transaction.service';
import { VasApiService, AirtimeTopupRequest, DataTopupRequest, BillPaymentRequest } from './vas-api.service';
import { WalletRepository } from '../repositories/wallet.repository';
import { Bill<PERSON>ategory, BillProvider, BillPayment } from '../interfaces/services';
import { ApiConfig } from '../interfaces/common';
import { logger } from '../common/logging/logger';

export class BillService {
  private transactionService = new TransactionService();
  private walletRepository = new WalletRepository();
  private vasApiService: VasApiService;
  
  constructor() {
    // Initialize VAS API service with configuration
    const vasConfig: ApiConfig = {
      baseUrl: process.env.VAS_API_BASE_URL || '',
      apiKey: process.env.VAS_API_KEY || '',
      secretKey: process.env.VAS_SECRET_KEY || '',
      timeout: 30000
    };

    const authConfig: ApiConfig = {
      baseUrl: process.env.VAS_AUTH_BASE_URL || '',
      apiKey: process.env.VAS_API_KEY || '',
      secretKey: process.env.VAS_SECRET_KEY || '',
      timeout: 30000
    };

    this.vasApiService = new VasApiService(vasConfig, authConfig);
  }

  async getBillCategories(): Promise<BillCategory[]> {
    try {
      await this.authenticateVasService();
      
      const categoriesResponse = await this.vasApiService.getBillCategories();
      
      if (categoriesResponse.success && categoriesResponse.data) {
        return categoriesResponse.data.map(cat => ({
          id: cat.id,
          name: cat.name,
          description: cat.description,
          icon: this.getCategoryIcon(cat.name.toLowerCase())
        }));
      }

      // Fallback to static categories
      return this.getStaticCategories();
    } catch (error: any) {
      logger.error('Get bill categories failed', { error: error.message });
      return this.getStaticCategories();
    }
  }

  async getBillProviders(categoryId: string): Promise<BillProvider[]> {
    try {
      await this.authenticateVasService();
      
      const billersResponse = await this.vasApiService.getCategoryBillers(categoryId);
      
      if (billersResponse.success && billersResponse.data) {
        const providers: BillProvider[] = [];
        
        for (const biller of billersResponse.data) {
          const inputFieldsResponse = await this.vasApiService.getBillerInputFields(biller.id);
          
          providers.push({
            id: biller.id,
            categoryId,
            name: biller.name,
            code: biller.shortName || biller.id,
            fee: 0, // Fee will be determined by the API
            validationFields: inputFieldsResponse.data?.map(field => field.name) || []
          });
        }
        
        return providers;
      }

      // Fallback to static providers for the category
      return this.getStaticProviders(categoryId);
    } catch (error: any) {
      logger.error('Get bill providers failed', { categoryId, error: error.message });
      return this.getStaticProviders(categoryId);
    }
  }

  async getNetworks() {
    try {
      await this.authenticateVasService();
      
      const networksResponse = await this.vasApiService.getNetworks();
      
      if (networksResponse.success && networksResponse.data) {
        return networksResponse.data;
      }

      // Fallback to static networks
      return [
        { id: 'MTN', name: 'MTN', code: 'MTN', status: 'active' },
        { id: 'GLO', name: 'Glo', code: 'GLO', status: 'active' },
        { id: 'AIRTEL', name: 'Airtel', code: 'AIRTEL', status: 'active' },
        { id: '9MOBILE', name: '9mobile', code: '9MOBILE', status: 'active' }
      ];
    } catch (error: any) {
      logger.error('Get networks failed', { error: error.message });
      return [];
    }
  }

  async getDataPlans(network: string) {
    try {
      await this.authenticateVasService();
      
      const plansResponse = await this.vasApiService.getDataPlans(network);
      
      if (plansResponse.success && plansResponse.data) {
        return plansResponse.data;
      }

      return [];
    } catch (error: any) {
      logger.error('Get data plans failed', { network, error: error.message });
      return [];
    }
  }

  async validateCustomer(validationData: any) {
    try {
      const { providerId, customerNumber, amount } = validationData;
      
      await this.authenticateVasService();
      
      const validationResponse = await this.vasApiService.validateBillerInput(
        providerId,
        { customerNumber, amount }
      );

      if (validationResponse.success && validationResponse.data) {
        return {
          valid: true,
          customerName: validationResponse.data.customerName || 'Valid Customer',
          customerNumber,
          providerId,
          providerName: validationResponse.data.providerName || '',
          fee: validationResponse.data.fee || 0,
          amount: parseFloat(amount),
          validationId: validationResponse.data.validationId || uuidv4()
        };
      }

      throw new Error(validationResponse.error || 'Customer validation failed');
    } catch (error: any) {
      logger.error('Customer validation failed', { validationData, error: error.message });
      throw new Error('Customer validation failed');
    }
  }

  async purchaseAirtime(userId: string, airtimeData: any) {
    try {
      const wallet = await this.walletRepository.findByUserId(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      await this.authenticateVasService();

      const transactionReference = `AIRTIME_${Date.now()}_${uuidv4().substring(0, 8)}`;

      const topupRequest: AirtimeTopupRequest = {
        phoneNumber: airtimeData.phoneNumber,
        network: airtimeData.network,
        amount: airtimeData.amount.toString(),
        debitAccount: wallet.accountNumber,
        transactionReference
      };

      const topupResponse = await this.vasApiService.purchaseAirtime(topupRequest);

      if (!topupResponse.success) {
        throw new Error(topupResponse.error || 'Airtime purchase failed');
      }

      // Record transaction
      await this.transactionService.createTransaction({
        userId,
        type: 'DEBIT',
        amount: airtimeData.amount,
        fee: airtimeData.fee || 0,
        description: `Airtime purchase - ${airtimeData.network} ${airtimeData.phoneNumber}`,
        category: 'AIRTIME',
        reference: transactionReference,
        status: 'COMPLETED',
        metadata: {
          phoneNumber: airtimeData.phoneNumber,
          network: airtimeData.network,
          provider: 'VAS_API'
        }
      });

      return {
        transactionId: transactionReference,
        status: 'SUCCESS',
        amount: airtimeData.amount,
        phoneNumber: airtimeData.phoneNumber,
        network: airtimeData.network,
        message: 'Airtime purchase successful',
        reference: topupResponse.reference
      };
    } catch (error: any) {
      logger.error('Airtime purchase failed', { userId, airtimeData, error: error.message });
      throw new Error('Failed to purchase airtime');
    }
  }

  async purchaseData(userId: string, dataData: any) {
    try {
      const wallet = await this.walletRepository.findByUserId(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      await this.authenticateVasService();

      const transactionReference = `DATA_${Date.now()}_${uuidv4().substring(0, 8)}`;

      const topupRequest: DataTopupRequest = {
        phoneNumber: dataData.phoneNumber,
        network: dataData.network,
        planId: dataData.planId,
        debitAccount: wallet.accountNumber,
        transactionReference
      };

      const topupResponse = await this.vasApiService.purchaseData(topupRequest);

      if (!topupResponse.success) {
        throw new Error(topupResponse.error || 'Data purchase failed');
      }

      // Record transaction
      await this.transactionService.createTransaction({
        userId,
        type: 'DEBIT',
        amount: dataData.amount,
        fee: dataData.fee || 0,
        description: `Data purchase - ${dataData.network} ${dataData.phoneNumber}`,
        category: 'DATA',
        reference: transactionReference,
        status: 'COMPLETED',
        metadata: {
          phoneNumber: dataData.phoneNumber,
          network: dataData.network,
          planId: dataData.planId,
          provider: 'VAS_API'
        }
      });

      return {
        transactionId: transactionReference,
        status: 'SUCCESS',
        amount: dataData.amount,
        phoneNumber: dataData.phoneNumber,
        network: dataData.network,
        planId: dataData.planId,
        message: 'Data purchase successful',
        reference: topupResponse.reference
      };
    } catch (error: any) {
      logger.error('Data purchase failed', { userId, dataData, error: error.message });
      throw new Error('Failed to purchase data');
    }
  }

  async payBill(userId: string, billData: any) {
    try {
      const wallet = await this.walletRepository.findByUserId(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      await this.authenticateVasService();

      const transactionReference = `BILL_${Date.now()}_${uuidv4().substring(0, 8)}`;

      const paymentRequest: BillPaymentRequest = {
        billerId: billData.providerId,
        amount: billData.amount.toString(),
        debitAccount: wallet.accountNumber,
        transactionReference,
        customerInfo: billData.customerInfo || {}
      };

      const paymentResponse = await this.vasApiService.processBillPayment(paymentRequest);

      if (!paymentResponse.success) {
        throw new Error(paymentResponse.error || 'Bill payment failed');
      }

      // Record transaction
      await this.transactionService.createTransaction({
        userId,
        type: 'DEBIT',
        amount: billData.amount,
        fee: billData.fee || 0,
        description: `Bill payment - ${billData.providerName}`,
        category: 'BILLS',
        reference: transactionReference,
        status: 'COMPLETED',
        metadata: {
          providerId: billData.providerId,
          providerName: billData.providerName,
          customerNumber: billData.customerNumber,
          provider: 'VAS_API'
        }
      });

      return {
        transactionId: transactionReference,
        status: 'SUCCESS',
        amount: billData.amount,
        providerId: billData.providerId,
        providerName: billData.providerName,
        customerNumber: billData.customerNumber,
        message: 'Bill payment successful',
        reference: paymentResponse.reference
      };
    } catch (error: any) {
      logger.error('Bill payment failed', { userId, billData, error: error.message });
      throw new Error('Failed to process bill payment');
    }
  }

  async getTransactionStatus(transactionReference: string, type: 'AIRTIME' | 'DATA' | 'BILLS') {
    try {
      await this.authenticateVasService();

      let statusResponse;
      
      if (type === 'AIRTIME' || type === 'DATA') {
        statusResponse = await this.vasApiService.getTopupStatus(transactionReference);
      } else {
        statusResponse = await this.vasApiService.getBillPaymentStatus(transactionReference);
      }

      if (statusResponse.success) {
        return {
          transactionReference,
          status: statusResponse.data?.status || 'UNKNOWN',
          ...statusResponse.data
        };
      }

      throw new Error(statusResponse.error || 'Failed to get transaction status');
    } catch (error: any) {
      logger.error('Get transaction status failed', { transactionReference, type, error: error.message });
      throw new Error('Failed to get transaction status');
    }
  }

  private async authenticateVasService() {
    try {
      const authResponse = await this.vasApiService.authenticate({
        username: process.env.VAS_USERNAME || process.env.VAS_API_KEY || '',
        password: process.env.VAS_PASSWORD || process.env.VAS_SECRET_KEY || ''
      });

      if (!authResponse.success) {
        throw new Error('VAS service authentication failed');
      }
    } catch (error: any) {
      logger.error('VAS service authentication failed', { error: error.message });
      throw new Error('Failed to authenticate with VAS service');
    }
  }

  private getCategoryIcon(categoryName: string): string {
    const iconMap: Record<string, string> = {
      'airtime': 'phone',
      'data': 'wifi',
      'electricity': 'zap',
      'cable': 'tv',
      'tv': 'tv',
      'internet': 'globe',
      'water': 'droplets',
      'gas': 'flame',
      'insurance': 'shield',
      'education': 'book'
    };

    for (const [key, icon] of Object.entries(iconMap)) {
      if (categoryName.includes(key)) {
        return icon;
      }
    }

    return 'credit-card';
  }

  private getStaticCategories(): BillCategory[] {
    return [
      { id: 'airtime', name: 'Airtime', description: 'Mobile airtime top-up', icon: 'phone' },
      { id: 'data', name: 'Data', description: 'Mobile data bundles', icon: 'wifi' },
      { id: 'electricity', name: 'Electricity', description: 'Electricity bill payments', icon: 'zap' },
      { id: 'cable-tv', name: 'Cable TV', description: 'Cable TV subscriptions', icon: 'tv' },
      { id: 'internet', name: 'Internet', description: 'Internet subscriptions', icon: 'globe' }
    ];
  }

  private getStaticProviders(categoryId: string): BillProvider[] {
    const allProviders: BillProvider[] = [
      { id: 'mtn-airtime', categoryId: 'airtime', name: 'MTN Airtime', code: 'MTN', fee: 0, validationFields: ['phone'] },
      { id: 'glo-airtime', categoryId: 'airtime', name: 'Glo Airtime', code: 'GLO', fee: 0, validationFields: ['phone'] },
      { id: 'airtel-airtime', categoryId: 'airtime', name: 'Airtel Airtime', code: 'AIRTEL', fee: 0, validationFields: ['phone'] },
      { id: 'mtn-data', categoryId: 'data', name: 'MTN Data', code: 'MTN_DATA', fee: 0, validationFields: ['phone'] },
      { id: 'eko-electricity', categoryId: 'electricity', name: 'Eko Electricity', code: 'EKEDC', fee: 100, validationFields: ['meterNumber'] },
      { id: 'dstv', categoryId: 'cable-tv', name: 'DSTV', code: 'DSTV', fee: 100, validationFields: ['smartCardNumber'] }
    ];

    return allProviders.filter(p => p.categoryId === categoryId);
  }
}
