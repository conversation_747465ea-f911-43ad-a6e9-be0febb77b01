import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { TransactionService } from '../../../services/transaction.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const transactionService = new TransactionService();
    const stats = await transactionService.getTransactionStats(user.userId);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: stats })
    };
  } catch (err: any) {
    logger.error('Get transaction stats error', { error: err.message });
    return errorResponse(401, 'UNAUTHORIZED', err.message);
  }
};
