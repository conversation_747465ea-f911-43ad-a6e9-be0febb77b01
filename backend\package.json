{"name": "zapwallet-backend", "version": "1.0.0", "description": "ZAPWALLET backend API - AWS serverless Node.js (TypeScript)", "main": "src/index.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start": "node dist/src/index.js", "dev": "npm run build && npm run start", "dev:watch": "concurrently \"npm run build:watch\" \"nodemon dist/src/index.js\"", "test": "jest --coverage --detect<PERSON><PERSON>Handles --forceExit", "test:watch": "jest --watch", "test:unit": "jest --testPathPatterns=tests/unit --coverage", "test:integration": "jest --testPathPatterns=tests/integration --coverage", "test:ci": "jest --coverage --ci --detectOpenHandles --forceExit --maxWorkers=2", "test:mongodb": "npx ts-node test-mongodb.ts", "test:operations": "npx ts-node test-real-operations.ts", "start:dev": "npx ts-node start-dev.ts", "test:complete": "npx ts-node test-complete.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "serverless:offline": "serverless offline", "serverless:start": "concurrently \"npm run build:watch\" \"npm run serverless:offline\"", "deploy:dev": "serverless deploy --stage dev", "deploy:prod": "serverless deploy --stage prod"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.16.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/axios": "^0.9.36", "@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/mongodb": "^4.0.6", "@types/node": "^24.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "concurrently": "^9.2.0", "dotenv": "^16.5.0", "eslint": "^9.29.0", "jest": "^30.0.2", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "serverless": "^4.17.1", "serverless-offline": "^14.4.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.2.2"}}