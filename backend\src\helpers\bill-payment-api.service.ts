import { ApiHelperService } from './api-helper.service';
import {
  ApiConfig,
  BillValidationRequest,
  BillValidationResponse,
  BillPaymentRequest,
  BillPaymentResponse,
  ApiResponse
} from '../interfaces/common';
import { logger } from '../common/logging/logger';

/**
 * Bill Payment API helper service for handling various bill payments
 * Supports airtime, data, electricity, cable TV, and other utility payments
 */
export class BillPaymentApiService {
  private providers: Map<string, ApiHelperService> = new Map();
  private activeProvider: string | null = null;

  constructor() {
    this.initializeProviders();
  }

  /**
   * Initialize bill payment service providers
   */
  private initializeProviders(): void {
    const providers = [
      {
        id: 'flutterwave',
        name: 'Flutterwave Bills',
        config: {
          baseUrl: process.env.FLUTTERWAVE_BASE_URL || 'https://api.flutterwave.com/v3',
          apiKey: process.env.FLUTTERWAVE_SECRET_KEY || '',
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      },
      {
        id: 'paystack',
        name: 'Paystack Bills',
        config: {
          baseUrl: process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co',
          apiKey: process.env.PAYSTACK_SECRET_KEY || '',
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      },
      {
        id: 'vtpass',
        name: 'VTPass',
        config: {
          baseUrl: process.env.VTPASS_BASE_URL || 'https://vtpass.com/api',
          apiKey: process.env.VTPASS_API_KEY || '',
          secretKey: process.env.VTPASS_SECRET_KEY || '',
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      },
      {
        id: 'baxi',
        name: 'Baxi',
        config: {
          baseUrl: process.env.BAXI_BASE_URL || 'https://payments.baxipay.com.ng/api/baxipay',
          apiKey: process.env.BAXI_API_KEY || '',
          secretKey: process.env.BAXI_SECRET_KEY || '',
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      }
    ];

    providers.forEach(provider => {
      if (provider.config.apiKey) {
        this.providers.set(provider.id, new ApiHelperService(provider.config));
        if (!this.activeProvider) {
          this.activeProvider = provider.id;
        }
      }
    });

    logger.info('Bill payment providers initialized', {
      providers: Array.from(this.providers.keys()),
      activeProvider: this.activeProvider
    });
  }

  /**
   * Get available bill categories
   */
  async getBillCategories(): Promise<ApiResponse<any[]>> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        success: false,
        error: 'No active bill payment provider available',
        code: 'NO_PROVIDER'
      };
    }

    try {
      const response = await provider.makeRequest({
        endpoint: '/bill-categories',
        method: 'GET'
      });

      return response;
    } catch (error: any) {
      logger.error('Get bill categories error', { error: error.message });
      return {
        success: false,
        error: 'Failed to get bill categories',
        code: 'CATEGORIES_ERROR'
      };
    }
  }

  /**
   * Get billers for a specific category
   */
  async getBillers(categoryId: string): Promise<ApiResponse<any[]>> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        success: false,
        error: 'No active bill payment provider available',
        code: 'NO_PROVIDER'
      };
    }

    try {
      const response = await provider.makeRequest({
        endpoint: `/bill-categories/${categoryId}/billers`,
        method: 'GET'
      });

      return response;
    } catch (error: any) {
      logger.error('Get billers error', { error: error.message, categoryId });
      return {
        success: false,
        error: 'Failed to get billers',
        code: 'BILLERS_ERROR'
      };
    }
  }

  /**
   * Validate customer details for bill payment
   */
  async validateCustomer(request: BillValidationRequest): Promise<ApiResponse<BillValidationResponse>> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        success: false,
        error: 'No active bill payment provider available',
        code: 'NO_PROVIDER'
      };
    }

    try {
      let response = await this.validateWithProvider(provider, request);
      
      // If failed, try with other providers
      if (!response.success) {
        response = await this.tryOtherProviders('validate', request);
      }

      return response;
    } catch (error: any) {
      logger.error('Customer validation error', { error: error.message, request });
      return {
        success: false,
        error: 'Customer validation failed',
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Process bill payment
   */
  async payBill(request: BillPaymentRequest): Promise<ApiResponse<BillPaymentResponse>> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        success: false,
        error: 'No active bill payment provider available',
        code: 'NO_PROVIDER'
      };
    }

    try {
      let response = await this.payWithProvider(provider, request);
      
      // If failed, try with other providers
      if (!response.success) {
        response = await this.tryOtherProviders('payment', request);
      }

      return response;
    } catch (error: any) {
      logger.error('Bill payment error', { error: error.message, request });
      return {
        success: false,
        error: 'Bill payment failed',
        code: 'PAYMENT_ERROR'
      };
    }
  }

  /**
   * Check payment status
   */
  async checkPaymentStatus(reference: string): Promise<ApiResponse<BillPaymentResponse>> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        success: false,
        error: 'No active bill payment provider available',
        code: 'NO_PROVIDER'
      };
    }

    try {
      const response = await provider.makeRequest({
        endpoint: `/bill-payments/${reference}`,
        method: 'GET'
      });

      return this.transformPaymentResponse(response);
    } catch (error: any) {
      logger.error('Payment status check error', { error: error.message, reference });
      return {
        success: false,
        error: 'Failed to check payment status',
        code: 'STATUS_CHECK_ERROR'
      };
    }
  }

  /**
   * Validate customer with specific provider
   */
  private async validateWithProvider(
    provider: ApiHelperService,
    request: BillValidationRequest
  ): Promise<ApiResponse<BillValidationResponse>> {
    
    const response = await provider.makeRequest({
      endpoint: '/bill-validate',
      method: 'POST',
      data: {
        biller_id: request.billerId,
        customer_id: request.customerCode,
        payment_code: request.paymentCode
      }
    });

    if (response.success && response.data) {
      return {
        success: true,
        data: {
          customerName: response.data.customer_name || response.data.customerName,
          customerCode: request.customerCode,
          billerId: request.billerId,
          billerName: response.data.biller_name || response.data.billerName,
          amount: response.data.amount,
          dueDate: response.data.due_date || response.data.dueDate,
          currency: response.data.currency || 'NGN'
        }
      };
    }

    return response;
  }

  /**
   * Process payment with specific provider
   */
  private async payWithProvider(
    provider: ApiHelperService,
    request: BillPaymentRequest
  ): Promise<ApiResponse<BillPaymentResponse>> {
    
    const providerRequest = this.transformPaymentRequest(request);
    
    const response = await provider.makeRequest({
      endpoint: '/bill-pay',
      method: 'POST',
      data: providerRequest
    });

    return this.transformPaymentResponse(response);
  }

  /**
   * Try other providers if current one fails
   */
  private async tryOtherProviders(operation: 'validate' | 'payment', request: any): Promise<ApiResponse> {
    const providers = Array.from(this.providers.entries());
    
    for (const [providerId, provider] of providers) {
      if (providerId === this.activeProvider) continue;
      
      try {
        logger.info(`Trying provider ${providerId} for ${operation}`);
        
        let response;
        if (operation === 'validate') {
          response = await this.validateWithProvider(provider, request);
        } else {
          response = await this.payWithProvider(provider, request);
        }
        
        if (response.success) {
          this.activeProvider = providerId;
          logger.info(`Switched to provider ${providerId}`);
          return response;
        }
      } catch (error: any) {
        logger.warn(`Provider ${providerId} failed`, { error: error.message });
        continue;
      }
    }

    return {
      success: false,
      error: 'All providers failed',
      code: 'ALL_PROVIDERS_FAILED'
    };
  }

  /**
   * Transform payment request to provider format
   */
  private transformPaymentRequest(request: BillPaymentRequest): any {
    return {
      biller_id: request.billerId,
      customer_id: request.customerCode,
      amount: request.amount,
      reference: request.reference,
      payment_code: request.paymentCode
    };
  }

  /**
   * Transform provider response to standard format
   */
  private transformPaymentResponse(response: ApiResponse): ApiResponse<BillPaymentResponse> {
    if (!response.success) {
      return response;
    }

    const data = response.data;
    return {
      success: true,
      data: {
        status: this.mapPaymentStatus(data?.status),
        reference: data?.reference || data?.tx_ref,
        transactionId: data?.transaction_id || data?.id?.toString(),
        fee: data?.fee || 0,
        token: data?.token,
        units: data?.units
      }
    };
  }

  /**
   * Map provider-specific status to standard status
   */
  private mapPaymentStatus(status: string): 'SUCCESS' | 'FAILED' | 'PENDING' {
    const statusMap: Record<string, 'SUCCESS' | 'FAILED' | 'PENDING'> = {
      'successful': 'SUCCESS',
      'success': 'SUCCESS',
      'completed': 'SUCCESS',
      'delivered': 'SUCCESS',
      'failed': 'FAILED',
      'error': 'FAILED',
      'pending': 'PENDING',
      'processing': 'PENDING',
      'initiated': 'PENDING'
    };

    return statusMap[status?.toLowerCase()] || 'PENDING';
  }

  /**
   * Get active provider instance
   */
  private getActiveProvider(): ApiHelperService | null {
    if (!this.activeProvider) return null;
    return this.providers.get(this.activeProvider) || null;
  }

  /**
   * Set active provider
   */
  setActiveProvider(providerId: string): boolean {
    if (this.providers.has(providerId)) {
      this.activeProvider = providerId;
      return true;
    }
    return false;
  }

  /**
   * Get available providers
   */
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Add new provider
   */
  addProvider(providerId: string, config: ApiConfig): void {
    this.providers.set(providerId, new ApiHelperService(config));
    if (!this.activeProvider) {
      this.activeProvider = providerId;
    }
  }

  /**
   * Remove provider
   */
  removeProvider(providerId: string): boolean {
    const removed = this.providers.delete(providerId);
    if (this.activeProvider === providerId) {
      this.activeProvider = this.providers.size > 0 ? Array.from(this.providers.keys())[0] : null;
    }
    return removed;
  }

  /**
   * Get provider-specific endpoints for different bill types
   */
  getBillTypeEndpoints(): Record<string, Record<string, string>> {
    return {
      flutterwave: {
        airtime: '/bills',
        data: '/bills',
        electricity: '/bills',
        cabletv: '/bills',
        internet: '/bills'
      },
      vtpass: {
        airtime: '/pay',
        data: '/pay',
        electricity: '/pay',
        cabletv: '/pay',
        internet: '/pay'
      },
      baxi: {
        airtime: '/services/airtime/request',
        data: '/services/databundle/request',
        electricity: '/services/electricity/request',
        cabletv: '/services/multichoice/request',
        internet: '/services/internet/request'
      }
    };
  }
}
