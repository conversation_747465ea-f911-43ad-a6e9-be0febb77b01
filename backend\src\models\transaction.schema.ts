import mongoose, { Schema, Document } from 'mongoose';
import { ITransaction } from '../interfaces/models';

export { ITransaction };

const TransactionSchema: Schema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  type: { type: String, enum: ['CREDIT', 'DEBIT'], required: true },
  amount: { type: Number, required: true, min: 0 },
  fee: { type: Number, default: 0, min: 0 },
  description: { type: String, required: true },
  status: { type: String, enum: ['PENDING', 'COMPLETED', 'FAILED'], default: 'PENDING' },
  category: { type: String, required: true },
  reference: { type: String, required: true, unique: true },
  metadata: { type: Schema.Types.Mixed } // For storing additional transaction data
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// Additional indexes for better query performance (beyond unique indexes)
TransactionSchema.index({ userId: 1, createdAt: -1 });
TransactionSchema.index({ status: 1 });
TransactionSchema.index({ category: 1 });

export const TransactionModel = mongoose.model<ITransaction>('Transaction', TransactionSchema);
