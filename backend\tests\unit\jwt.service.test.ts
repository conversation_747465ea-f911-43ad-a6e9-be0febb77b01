import { JwtService } from '../../src/common/security/jwt.service';

describe('JwtService', () => {
  let jwtService: JwtService;

  beforeEach(() => {
    jwtService = new JwtService();
  });

  describe('generateTokenPair', () => {
    it('should generate access and refresh tokens', () => {
      const payload = {
        userId: 'user123',
        email: '<EMAIL>',
        role: 'USER' as const
      };

      const tokens = jwtService.generateTokenPair(payload);

      expect(tokens).toBeDefined();
      expect(tokens.accessToken).toBeDefined();
      expect(tokens.refreshToken).toBeDefined();
      expect(typeof tokens.accessToken).toBe('string');
      expect(typeof tokens.refreshToken).toBe('string');
    });

    it('should generate different tokens for different payloads', () => {
      const payload1 = {
        userId: 'user1',
        email: '<EMAIL>',
        role: 'USER' as const
      };

      const payload2 = {
        userId: 'user2',
        email: '<EMAIL>',
        role: 'ADMIN' as const
      };

      const tokens1 = jwtService.generateTokenPair(payload1);
      const tokens2 = jwtService.generateTokenPair(payload2);

      expect(tokens1.accessToken).not.toBe(tokens2.accessToken);
      expect(tokens1.refreshToken).not.toBe(tokens2.refreshToken);
    });
  });

  describe('verifyAccessToken', () => {
    it('should verify valid access token', () => {
      const payload = {
        userId: 'user123',
        email: '<EMAIL>',
        role: 'USER' as const
      };

      const tokens = jwtService.generateTokenPair(payload);
      const decoded = jwtService.verifyAccessToken(tokens.accessToken);

      expect(decoded).toBeDefined();
      expect(decoded.userId).toBe(payload.userId);
      expect(decoded.email).toBe(payload.email);
      expect(decoded.role).toBe(payload.role);
    });

    it('should throw error for invalid token', () => {
      const invalidToken = 'invalid.token.here';

      expect(() => {
        jwtService.verifyAccessToken(invalidToken);
      }).toThrow();
    });

    it('should throw error for malformed token', () => {
      const malformedToken = 'not-a-jwt-token';

      expect(() => {
        jwtService.verifyAccessToken(malformedToken);
      }).toThrow();
    });
  });

  describe('verifyRefreshToken', () => {
    it('should verify valid refresh token', () => {
      const payload = {
        userId: 'user123',
        email: '<EMAIL>',
        role: 'USER' as const
      };

      const tokens = jwtService.generateTokenPair(payload);
      const decoded = jwtService.verifyRefreshToken(tokens.refreshToken);

      expect(decoded).toBeDefined();
      expect(decoded.userId).toBe(payload.userId);
      expect(decoded.email).toBe(payload.email);
      expect(decoded.role).toBe(payload.role);
    });

    it('should throw error for invalid refresh token', () => {
      const invalidToken = 'invalid.refresh.token';

      expect(() => {
        jwtService.verifyRefreshToken(invalidToken);
      }).toThrow();
    });
  });

  describe('refreshAccessToken', () => {    it('should generate new access token from valid refresh token', async () => {      const payload = {
        userId: 'user123',
        email: '<EMAIL>',
        role: 'USER' as const
      };

      const tokens = jwtService.generateTokenPair(payload);
      
      // Add small delay to ensure different iat (issued at) timestamp
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Verify refresh token and generate new access token
      const verifiedPayload = jwtService.verifyRefreshToken(tokens.refreshToken);
      // Remove exp and iat claims before generating new token
      const cleanPayload = {
        userId: verifiedPayload.userId,
        email: verifiedPayload.email,
        role: verifiedPayload.role
      };
      const newAccessToken = jwtService.generateAccessToken(cleanPayload);

      expect(newAccessToken).toBeDefined();
      expect(typeof newAccessToken).toBe('string');
      expect(newAccessToken).not.toBe(tokens.accessToken);

      // Verify the new access token is valid
      const decoded = jwtService.verifyAccessToken(newAccessToken);
      expect(decoded.userId).toBe(payload.userId);
    });

    it('should throw error for invalid refresh token', () => {
      const invalidRefreshToken = 'invalid.refresh.token';

      expect(() => {
        jwtService.verifyRefreshToken(invalidRefreshToken);
      }).toThrow();
    });
  });

  describe('edge cases', () => {
    it('should handle empty payload gracefully', () => {
      const emptyPayload = {} as any;

      expect(() => {
        jwtService.generateTokenPair(emptyPayload);
      }).not.toThrow();
    });

    it('should handle undefined values in payload', () => {
      const payloadWithUndefined = {
        userId: 'user123',
        email: undefined,
        role: 'USER' as const
      } as any;

      expect(() => {
        jwtService.generateTokenPair(payloadWithUndefined);
      }).not.toThrow();
    });
  });
});
