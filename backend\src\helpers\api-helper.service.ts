import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { 
  ApiConfig, 
  ApiRequest, 
  ApiResponse, 
  RetryConfig, 
  ServiceProvider 
} from '../interfaces/common';
import { logger } from '../common/logging/logger';

/**
 * Third-party API helper service for processing transfers and bill payments
 * Provides a unified interface for multiple service providers with built-in
 * retry logic, error handling, and configuration management
 */
export class ApiHelperService {
  private axiosInstance: AxiosInstance;
  private config: ApiConfig;
  private retryConfig: RetryConfig;

  constructor(config: ApiConfig, retryConfig?: RetryConfig) {
    this.config = config;
    this.retryConfig = retryConfig || {
      attempts: 3,
      delay: 1000,
      backoff: 2
    };

    this.axiosInstance = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
        ...config.headers
      }
    });

    this.setupInterceptors();
  }

  /**
   * Setup request and response interceptors for logging and authentication
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        logger.info('API Request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          baseURL: config.baseURL,
          timestamp: new Date().toISOString()
        });
        return config;
      },
      (error) => {
        logger.error('API Request Error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => {
        logger.info('API Response', {
          status: response.status,
          url: response.config.url,
          timestamp: new Date().toISOString()
        });
        return response;
      },
      (error) => {
        logger.error('API Response Error', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Make HTTP request with retry logic
   */
  async makeRequest<T = any>(request: ApiRequest): Promise<ApiResponse<T>> {
    const { endpoint, method, data, params, headers, timeout } = request;
    
    for (let attempt = 1; attempt <= this.retryConfig.attempts; attempt++) {
      try {
        const config: AxiosRequestConfig = {
          method,
          url: endpoint,
          data,
          params,
          headers,
          timeout: timeout || this.config.timeout
        };

        const response: AxiosResponse = await this.axiosInstance.request(config);
        
        return this.transformResponse<T>(response);
      } catch (error: any) {
        const isLastAttempt = attempt === this.retryConfig.attempts;
        const shouldRetry = this.shouldRetry(error, attempt);

        if (isLastAttempt || !shouldRetry) {
          return this.handleError(error);
        }

        // Wait before retry with exponential backoff
        const delay = this.retryConfig.delay * Math.pow(this.retryConfig.backoff, attempt - 1);
        await this.sleep(delay);
        
        logger.warn('Retrying API request', {
          attempt,
          endpoint,
          delay
        });
      }
    }

    // This should never be reached, but TypeScript requires it
    return {
      success: false,
      error: 'Maximum retry attempts exceeded'
    };
  }
  /**
   * Transform axios response to standardized API response
   */
  private transformResponse<T>(response: AxiosResponse): ApiResponse<T> {
    const { data, status } = response;
    
    // Handle 9PSB Wallet API response format
    if (data.status && data.responseCode && data.message) {
      return {
        success: data.status === 'PENDING' || data.status === 'SUCCESS' || data.responseCode === '00',
        data: data.data || data,
        message: data.message,
        error: data.status === 'FAILED' ? data.message : data.error,
        code: data.responseCode,
        reference: data.data?.reference || data.reference
      };
    }

    // Handle VAS API response format
    if (data.status === 'success' || data.status === 'failed') {
      return {
        success: data.status === 'success',
        data: data.data,
        message: data.message,
        error: data.status === 'failed' ? data.message : undefined,
        reference: data.data?.reference || data.data?.transactionReference
      };
    }

    // Handle different response formats from various providers
    if (data.success !== undefined) {
      return {
        success: data.success,
        data: data.data,
        message: data.message,
        error: data.error,
        code: data.code,
        reference: data.reference
      };
    }

    // Flutterwave format
    if (data.status === 'success') {
      return {
        success: true,
        data: data.data,
        message: data.message,
        reference: data.data?.reference || data.data?.tx_ref
      };
    }

    // Paystack format
    if (data.status === true) {
      return {
        success: true,
        data: data.data,
        message: data.message,
        reference: data.data?.reference
      };
    }

    // Default successful response
    if (status >= 200 && status < 300) {
      return {
        success: true,
        data: data,
        message: 'Request successful'
      };
    }

    return {
      success: false,
      error: 'Unknown response format',
      data: data
    };
  }

  /**
   * Handle API errors
   */
  private handleError(error: any): ApiResponse {
    if (error.response) {
      const { status, data } = error.response;
      
      return {
        success: false,
        error: data?.message || data?.error || 'API request failed',
        code: data?.code || status.toString(),
        data: data
      };
    }

    if (error.request) {
      return {
        success: false,
        error: 'Network error - no response received',
        code: 'NETWORK_ERROR'
      };
    }

    return {
      success: false,
      error: error.message || 'Unknown error occurred',
      code: 'UNKNOWN_ERROR'
    };
  }

  /**
   * Determine if request should be retried
   */
  private shouldRetry(error: any, attempt: number): boolean {
    // Don't retry if we've exceeded max attempts
    if (attempt >= this.retryConfig.attempts) {
      return false;
    }

    // Don't retry client errors (4xx)
    if (error.response && error.response.status >= 400 && error.response.status < 500) {
      return false;
    }

    // Retry on network errors, timeouts, and server errors (5xx)
    return (
      !error.response || // Network error
      error.code === 'ECONNABORTED' || // Timeout
      error.response.status >= 500 // Server error
    );
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update API configuration
   */
  updateConfig(newConfig: Partial<ApiConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update axios instance defaults
    this.axiosInstance.defaults.baseURL = this.config.baseUrl;
    this.axiosInstance.defaults.timeout = this.config.timeout || 30000;
    this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${this.config.apiKey}`;
    
    if (this.config.headers) {
      Object.assign(this.axiosInstance.defaults.headers.common, this.config.headers);
    }
  }

  /**
   * Update retry configuration
   */
  updateRetryConfig(newRetryConfig: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...newRetryConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): ApiConfig {
    return { ...this.config };
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<ApiResponse> {
    try {
      const response = await this.makeRequest({
        endpoint: '/health',
        method: 'GET'
      });
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: 'Health check failed',
        code: 'HEALTH_CHECK_FAILED'
      };
    }
  }
}
