import { UserRepository } from '../repositories/user.repository';
import { v4 as uuidv4 } from 'uuid';

export class UserService {
  private userRepository = new UserRepository();

  async getProfile(userId: string) {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }
    
    // Remove sensitive data
    const { password, ...profile } = user;
    return profile;
  }

  async updateProfile(userId: string, updateData: any) {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }
    
    const updatedUser = await this.userRepository.update(userId, updateData);
    const { password, ...profile } = updatedUser;
    return profile;
  }

  async submitKYC(userId: string, kycData: any) {
    // TODO: Process KYC documents, integrate with verification service
    const referenceId = uuidv4();
    
    return {
      verificationStatus: 'PENDING',
      referenceId,
      message: 'KYC documents submitted successfully. Verification may take 1-3 business days.'
    };
  }
}
