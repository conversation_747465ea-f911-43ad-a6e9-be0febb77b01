import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { TransactionService } from '../../../services/transaction.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const queryParams = event.queryStringParameters || {};
    const transactionService = new TransactionService();
    const transactions = await transactionService.getTransactions(user.userId, queryParams);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: transactions })
    };
  } catch (err: any) {
    logger.error('Get transactions error', { error: err.message });
    return errorResponse(401, 'UNAUTHORIZED', err.message);
  }
};
