import mongoose from 'mongoose';

export class DatabaseConfig {
  private static instance: DatabaseConfig;
  private isConnected = false;

  private constructor() {}

  public static getInstance(): DatabaseConfig {
    if (!DatabaseConfig.instance) {
      DatabaseConfig.instance = new DatabaseConfig();
    }
    return DatabaseConfig.instance;
  }
  public async connect(): Promise<void> {
    if (this.isConnected) {
      console.log('Already connected to MongoDB');
      return;
    }

    try {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/zapwallet';
        await mongoose.connect(mongoUri, {
        bufferCommands: true, // Enable buffering for better compatibility
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 10000,
        socketTimeoutMS: 45000,
      });

      this.isConnected = true;
      console.log('Connected to MongoDB successfully');
    } catch (error) {
      console.error('MongoDB connection error:', error);
      throw new Error('Connection failed');
    }
  }
  public async disconnect(): Promise<void> {
    if (!this.isConnected) {
      console.log('Not connected to MongoDB');
      return;
    }

    try {
      await mongoose.disconnect();
      this.isConnected = false;
      console.log('Disconnected from MongoDB');
    } catch (error) {
      console.error('MongoDB disconnection error:', error);
      throw error;
    }
  }

  public getConnection() {
    return mongoose.connection;
  }
}

// Helper function to ensure database connection
export const connectToDatabase = async (): Promise<void> => {
  const db = DatabaseConfig.getInstance();
  await db.connect();
};
