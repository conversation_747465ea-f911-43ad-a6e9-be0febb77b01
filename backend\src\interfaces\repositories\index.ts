/**
 * User repository related interfaces
 */
export interface UserQueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  role?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Transaction repository related interfaces
 */
export interface TransactionQueryOptions {
  page?: number;
  limit?: number;
  userId?: string;
  status?: string;
  type?: string;
  category?: string;
  startDate?: Date;
  endDate?: Date;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
