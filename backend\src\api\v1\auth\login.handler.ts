import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { validateLogin } from '../../../common/validation/auth.validation';
import { AuthService } from '../../../services/auth.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { DatabaseConfig } from '../../../config/database';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Ensure database connection
    await DatabaseConfig.getInstance().connect();
    
    const body = JSON.parse(event.body || '{}');
    const { error } = validateLogin(body);
    if (error) {
      logger.warn('Validation failed', { details: error.details });
      return errorResponse(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const authService = new AuthService();
    const result = await authService.login(body);
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: result })
    };
  } catch (err: any) {
    logger.error('Login error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during login');
  }
};
