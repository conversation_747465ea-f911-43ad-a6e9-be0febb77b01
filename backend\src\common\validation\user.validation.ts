import Joi from 'joi';

export const updateProfileSchema = Joi.object({
  firstName: Joi.string().min(2).max(50),
  lastName: Joi.string().min(2).max(50),
  phone: Joi.string().pattern(/^\+234[0-9]{10}$/),
  dateOfBirth: Joi.string().isoDate()
});

export const kycSchema = Joi.object({
  documentType: Joi.string().valid('NIN', 'PASSPORT', 'DRIVERS_LICENSE').required(),
  documentNumber: Joi.string().required(),
  documentImage: Joi.string().required(), // Base64 encoded image
  selfieImage: Joi.string().required(), // Base64 encoded image
  address: Joi.object({
    street: Joi.string().required(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    country: Joi.string().default('Nigeria')
  }).required()
});

export function validateUpdateProfile(data: any) {
  return updateProfileSchema.validate(data, { abortEarly: true });
}

export function validateKYC(data: any) {
  return kycSchema.validate(data, { abortEarly: true });
}
