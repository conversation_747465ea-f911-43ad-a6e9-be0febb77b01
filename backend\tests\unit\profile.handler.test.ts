import { main } from '../../src/api/v1/users/profile.handler';
import { UserService } from '../../src/services/user.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';

jest.mock('../../src/services/user.service');
jest.mock('../../src/common/middlewares/auth.middleware');

describe('Profile Handler', () => {
  let mockUserService: jest.Mocked<UserService>;
  let mockAuthMiddleware: jest.Mocked<AuthMiddleware>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUserService = {
      getProfile: jest.fn(),
    } as any;
    (UserService as jest.MockedClass<typeof UserService>).mockImplementation(() => mockUserService);

    mockAuthMiddleware = {
      authenticate: jest.fn(),
    } as any;
    (AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>).mockImplementation(() => mockAuthMiddleware);
  });

  describe('successful profile retrieval', () => {
    it('should return user profile for authenticated user', async () => {
      const event = {
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };
      const mockProfile = {
        id: 'user123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+2348123456789',
        dateOfBirth: '1990-01-01',
        kycVerified: true,        role: 'USER' as const,
        status: 'ACTIVE' as const,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockUserService.getProfile.mockResolvedValue(mockProfile);

      const result = await main(event);

      expect(mockAuthMiddleware.authenticate).toHaveBeenCalledWith(event);      expect(mockUserService.getProfile).toHaveBeenCalledWith('user123');
      expect(result.statusCode).toBe(200);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(true);
      expect(response.data.id).toBe(mockProfile.id);
      expect(response.data.firstName).toBe(mockProfile.firstName);
      expect(response.data.email).toBe(mockProfile.email);
    });

    it('should handle profile with minimal data', async () => {
      const event = {
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user456', email: '<EMAIL>' };      const mockProfile = {
        id: 'user456',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+2348987654321',
        dateOfBirth: '1995-05-15',
        kycVerified: false,
        role: 'USER' as const,
        status: 'PENDING_VERIFICATION' as const,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockUserService.getProfile.mockResolvedValue(mockProfile);      const result = await main(event);

      expect(result.statusCode).toBe(200);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(true);
      expect(response.data.id).toBe(mockProfile.id);
      expect(response.data.firstName).toBe(mockProfile.firstName);
      expect(response.data.email).toBe(mockProfile.email);
    });
  });

  describe('authentication failures', () => {
    it('should return 401 when authentication fails', async () => {
      const event = {
        headers: {}
      } as any;

      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });      const result = await main(event);

      expect(result.statusCode).toBe(401);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('UNAUTHORIZED');
      expect(response.error.message).toBe('Missing or invalid authorization header');
    });

    it('should return 401 for invalid token', async () => {
      const event = {
        headers: { Authorization: 'Bearer invalidtoken' }
      } as any;

      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Invalid or expired token');
      });      const result = await main(event);

      expect(result.statusCode).toBe(401);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('UNAUTHORIZED');
      expect(response.error.message).toBe('Invalid or expired token');
    });

    it('should return 401 for expired token', async () => {
      const event = {
        headers: { Authorization: 'Bearer expiredtoken' }
      } as any;

      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Token expired');
      });

      const result = await main(event);      expect(result.statusCode).toBe(401);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('UNAUTHORIZED');
      expect(response.error.message).toBe('Token expired');
    });
  });

  describe('service errors', () => {
    it('should handle user not found error', async () => {
      const event = {
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'nonexistent', email: '<EMAIL>' };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockUserService.getProfile.mockRejectedValue(new Error('User not found'));

      const result = await main(event);      expect(result.statusCode).toBe(401);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('UNAUTHORIZED');
      expect(response.error.message).toBe('User not found');
    });

    it('should handle database connection errors', async () => {
      const event = {
        headers: { Authorization: 'Bearer validtoken' }
      } as any;

      const mockUser = { userId: 'user123', email: '<EMAIL>' };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockUserService.getProfile.mockRejectedValue(new Error('Database connection failed'));

      const result = await main(event);      expect(result.statusCode).toBe(401);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('UNAUTHORIZED');
      expect(response.error.message).toBe('Database connection failed');
    });
  });

  describe('edge cases', () => {
    it('should handle missing Authorization header', async () => {
      const event = {
        headers: {}
      } as any;

      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Missing authorization header');
      });

      const result = await main(event);      expect(result.statusCode).toBe(401);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('UNAUTHORIZED');
      expect(response.error.message).toBe('Missing authorization header');
    });

    it('should handle malformed Authorization header', async () => {
      const event = {
        headers: { Authorization: 'InvalidFormat' }
      } as any;

      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Malformed authorization header');
      });

      const result = await main(event);      expect(result.statusCode).toBe(401);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(false);
      expect(response.error.code).toBe('UNAUTHORIZED');
      expect(response.error.message).toBe('Malformed authorization header');
    });

    it('should handle different user roles', async () => {
      const event = {
        headers: { Authorization: 'Bearer admintoken' }
      } as any;

      const mockUser = { userId: 'admin123', email: '<EMAIL>' };
      const mockProfile = {
        id: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '+2348111111111',
        dateOfBirth: '1985-01-01',
        kycVerified: true,        role: 'ADMIN' as const,
        status: 'ACTIVE' as const,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockUserService.getProfile.mockResolvedValue(mockProfile);

      const result = await main(event);      expect(result.statusCode).toBe(200);
      const response = JSON.parse(result.body);
      expect(response.success).toBe(true);
      expect(response.data.id).toBe(mockProfile.id);
      expect(response.data.firstName).toBe(mockProfile.firstName);
      expect(response.data.email).toBe(mockProfile.email);
    });
  });
});
