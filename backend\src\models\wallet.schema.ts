import mongoose, { Schema, Document } from 'mongoose';
import { IWallet } from '../interfaces/models';

export { IWallet };

const WalletSchema: Schema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true, unique: true },
  accountNumber: { type: String, required: true, unique: true },
  customerID: { type: String },
  accountName: { type: String },
  available: { type: Number, default: 0, min: 0 },
  pending: { type: Number, default: 0, min: 0 },
  currency: { type: String, default: 'NGN' },
  status: { 
    type: String, 
    enum: ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'CLOSED'], 
    default: 'ACTIVE' 
  },
  limits: {
    daily: { type: Number, default: 100000 },
    monthly: { type: Number, default: 500000 },
    single: { type: Number, default: 50000 },
    dailyTransfer: { type: Number, default: 1000000 }, // 1M NGN
    dailyTransferUsed: { type: Number, default: 0 },
    singleTransferMax: { type: Number, default: 500000 } // 500K NGN
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// No additional indexes needed since userId is already unique

export const WalletModel = mongoose.model<IWallet>('Wallet', WalletSchema);
