import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { main as fundHandler } from '../../src/api/v1/wallets/fund.handler';
import { WalletService } from '../../src/services/wallet.service';

// Mock WalletService
jest.mock('../../src/services/wallet.service');

// Mock DatabaseConfig
jest.mock('../../src/config/database', () => ({
  DatabaseConfig: {
    getInstance: jest.fn().mockReturnValue({
      connect: jest.fn().mockResolvedValue(undefined)
    })
  }
}));

// Mock AuthMiddleware
jest.mock('../../src/common/middlewares/auth.middleware', () => ({
  AuthMiddleware: jest.fn().mockImplementation(() => ({
    authenticate: jest.fn().mockImplementation((event) => {
      const authHeader = event.headers?.['Authorization'] || event.headers?.['authorization'];
      if (!authHeader) {
        throw new Error('Missing or invalid authorization header');
      }
      return {
        userId: 'user123',
        email: '<EMAIL>'
      };
    })
  }))
}));

describe('Fund Handler', () => {
  let mockWalletService: jest.Mocked<WalletService>;
  let mockEvent: Partial<APIGatewayProxyEvent>;
  beforeEach(() => {
    mockWalletService = {
      fundWallet: jest.fn(),
      withdrawFromWallet: jest.fn(),
      getBalance: jest.fn(),
      createDefaultWallet: jest.fn()
    } as any;
    (WalletService as jest.MockedClass<typeof WalletService>).mockImplementation(() => mockWalletService);

    mockEvent = {
      headers: {
        'Authorization': 'Bearer valid-token'
      },
      body: JSON.stringify({
        amount: 1000,
        paymentMethod: 'bank_transfer'
      }),
      httpMethod: 'POST',
      path: '/wallets/fund',
      isBase64Encoded: false,
      multiValueHeaders: {},
      multiValueQueryStringParameters: null,
      pathParameters: null,
      queryStringParameters: null,
      requestContext: {} as any,
      resource: '',
      stageVariables: null
    };

    jest.clearAllMocks();
  });

  describe('successful funding', () => {
    it('should initiate wallet funding successfully', async () => {      const mockFundingResult = {
        transactionId: 'txn_123',
        status: 'PENDING',
        amount: 1000,
        paymentMethod: 'bank_transfer',
        message: 'Funding initiated successfully. Please complete payment.'
      };

      mockWalletService.fundWallet.mockResolvedValue(mockFundingResult);

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data).toBeDefined();      expect(body.data.transactionId).toBe('txn_123');
      expect(body.data.message).toBeDefined();
      expect(mockWalletService.fundWallet).toHaveBeenCalledWith('user123', { amount: 1000, paymentMethod: 'bank_transfer' });
    });

    it('should handle different payment methods', async () => {
      mockEvent.body = JSON.stringify({
        amount: 5000,
        paymentMethod: 'card'
      });      const mockFundingResult = {
        transactionId: 'txn_456',
        status: 'PENDING',
        amount: 5000,
        paymentMethod: 'card',
        message: 'Funding initiated successfully. Please complete payment.'
      };

      mockWalletService.fundWallet.mockResolvedValue(mockFundingResult);

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      expect(mockWalletService.fundWallet).toHaveBeenCalledWith('user123', { amount: 5000, paymentMethod: 'card' });
    });
  });

  describe('validation errors', () => {
    it('should return 400 for missing amount', async () => {
      mockEvent.body = JSON.stringify({
        paymentMethod: 'bank_transfer'
      });

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid amount', async () => {
      mockEvent.body = JSON.stringify({
        amount: -100,
        paymentMethod: 'bank_transfer'
      });

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });

    it('should return 400 for missing payment method', async () => {
      mockEvent.body = JSON.stringify({
        amount: 1000
      });

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });

    it('should return 400 for invalid request body', async () => {
      mockEvent.body = 'invalid json';

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('authentication errors', () => {
    it('should return 401 for missing authorization', async () => {
      delete mockEvent.headers!['Authorization'];

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('service errors', () => {
    it('should handle wallet service errors', async () => {
      mockWalletService.fundWallet.mockRejectedValue(new Error('Insufficient balance'));

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
    });

    it('should handle database connection errors', async () => {
      mockWalletService.fundWallet.mockRejectedValue(new Error('Database connection failed'));

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('edge cases', () => {
    it('should handle empty request body', async () => {
      mockEvent.body = '';

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });

    it('should handle null request body', async () => {
      mockEvent.body = null;

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });

    it('should handle large amounts', async () => {
      mockEvent.body = JSON.stringify({
        amount: 1000000,
        paymentMethod: 'bank_transfer'
      });      const mockFundingResult = {
        transactionId: 'txn_large',
        status: 'PENDING',
        amount: 1000000,
        paymentMethod: 'bank_transfer',
        message: 'Funding initiated successfully. Please complete payment.'
      };

      mockWalletService.fundWallet.mockResolvedValue(mockFundingResult);

      const result = await fundHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      expect(mockWalletService.fundWallet).toHaveBeenCalledWith('user123', { amount: 1000000, paymentMethod: 'bank_transfer' });
    });
  });
});
