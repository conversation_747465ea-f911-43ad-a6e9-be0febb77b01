import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { validateRegister } from '../../../common/validation/auth.validation';
import { AuthService } from '../../../services/auth.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { DatabaseConfig } from '../../../config/database';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Ensure database connection
    await DatabaseConfig.getInstance().connect();
    
    // Parse and validate request body
    let body;
    try {
      body = JSON.parse(event.body || '{}');
    } catch (parseError) {
      logger.warn('Invalid JSON in request body');
      return errorResponse(400, 'VALIDATION_ERROR', 'Invalid JSON in request body');
    }
    
    const { error } = validateRegister(body);
    if (error) {
      logger.warn('Validation failed', { details: error.details });
      return errorResponse(400, 'VALIDATION_ERROR', error.details[0].message);
    }    const authService = new AuthService();
    const result = await authService.register(body);
    
    return {
      statusCode: 201,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'OPTIONS,POST,GET,PUT,DELETE'
      },
      body: JSON.stringify({ success: true, data: result })
    };
  } catch (err: any) {
    logger.error('Registration error', { error: err.message });
    
    // Handle specific error cases
    if (err.message.includes('already exists')) {
      return errorResponse(409, 'USER_EXISTS', err.message);
    }
    
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during registration');
  }
};
