import { APIGatewayProxyEvent } from 'aws-lambda';
import { AuthMiddleware, AuthenticatedEvent } from '../../src/common/middlewares/auth.middleware';
import { JwtService } from '../../src/common/security/jwt.service';

// Mock the JwtService
jest.mock('../../src/common/security/jwt.service');

describe('AuthMiddleware', () => {
  let authMiddleware: AuthMiddleware;
  let mockJwtService: jest.Mocked<JwtService>;

  beforeEach(() => {
    jest.clearAllMocks();
    authMiddleware = new AuthMiddleware();
    mockJwtService = {
      verifyAccessToken: jest.fn(),
      verifyRefreshToken: jest.fn(),
      generateTokenPair: jest.fn(),
      refreshAccessToken: jest.fn()
    } as any;
    (authMiddleware as any).jwtService = mockJwtService;
  });

  const createMockEvent = (headers: any = {}): APIGatewayProxyEvent => ({
    body: null,
    headers,
    multiValueHeaders: {},
    httpMethod: 'GET',
    isBase64Encoded: false,
    path: '/api/v1/test',
    pathParameters: null,
    queryStringParameters: null,
    multiValueQueryStringParameters: null,
    stageVariables: null,
    resource: '/api/v1/test',
    requestContext: {
      requestId: 'test-request-id',
      stage: 'test',
      resourceId: 'test-resource',
      httpMethod: 'GET',
      resourcePath: '/api/v1/test',
      path: '/api/v1/test',
      accountId: 'test-account',
      apiId: 'test-api',
      protocol: 'HTTP/1.1',
      requestTime: '01/Jan/2025:00:00:00 +0000',
      requestTimeEpoch: **********,
      identity: {
        accessKey: null,
        accountId: null,
        apiKey: null,
        apiKeyId: null,
        caller: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '127.0.0.1',
        user: null,
        userAgent: 'test-user-agent',
        userArn: null,
        clientCert: null
      },
      authorizer: null
    }
  });

  describe('authenticate', () => {
    it('should authenticate user with valid token', () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };
      
      mockJwtService.verifyAccessToken.mockReturnValue(mockUser);
      
      const event = createMockEvent({
        'Authorization': 'Bearer valid-token-123'
      });

      const result = authMiddleware.authenticate(event);

      expect(result).toEqual(mockUser);
      expect(mockJwtService.verifyAccessToken).toHaveBeenCalledWith('valid-token-123');
    });

    it('should authenticate user with lowercase authorization header', () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };
      
      mockJwtService.verifyAccessToken.mockReturnValue(mockUser);
      
      const event = createMockEvent({
        'authorization': 'Bearer valid-token-123'
      });

      const result = authMiddleware.authenticate(event);

      expect(result).toEqual(mockUser);
      expect(mockJwtService.verifyAccessToken).toHaveBeenCalledWith('valid-token-123');
    });

    it('should throw error when authorization header is missing', () => {
      const event = createMockEvent({});

      expect(() => authMiddleware.authenticate(event))
        .toThrow('Missing or invalid authorization header');
      
      expect(mockJwtService.verifyAccessToken).not.toHaveBeenCalled();
    });

    it('should throw error when authorization header is empty', () => {
      const event = createMockEvent({
        'Authorization': ''
      });

      expect(() => authMiddleware.authenticate(event))
        .toThrow('Missing or invalid authorization header');
      
      expect(mockJwtService.verifyAccessToken).not.toHaveBeenCalled();
    });

    it('should throw error when authorization header does not start with Bearer', () => {
      const event = createMockEvent({
        'Authorization': 'Basic token-123'
      });

      expect(() => authMiddleware.authenticate(event))
        .toThrow('Missing or invalid authorization header');
      
      expect(mockJwtService.verifyAccessToken).not.toHaveBeenCalled();
    });

    it('should throw error when token is invalid', () => {
      mockJwtService.verifyAccessToken.mockImplementation(() => {
        throw new Error('Token invalid');
      });
      
      const event = createMockEvent({
        'Authorization': 'Bearer invalid-token'
      });

      expect(() => authMiddleware.authenticate(event))
        .toThrow('Invalid or expired token');
      
      expect(mockJwtService.verifyAccessToken).toHaveBeenCalledWith('invalid-token');
    });

    it('should throw error when token is expired', () => {
      mockJwtService.verifyAccessToken.mockImplementation(() => {
        throw new Error('Token expired');
      });
      
      const event = createMockEvent({
        'Authorization': 'Bearer expired-token'
      });

      expect(() => authMiddleware.authenticate(event))
        .toThrow('Invalid or expired token');
      
      expect(mockJwtService.verifyAccessToken).toHaveBeenCalledWith('expired-token');
    });

    it('should handle malformed token', () => {
      mockJwtService.verifyAccessToken.mockImplementation(() => {
        throw new Error('Malformed token');
      });
      
      const event = createMockEvent({
        'Authorization': 'Bearer malformed.token'
      });

      expect(() => authMiddleware.authenticate(event))
        .toThrow('Invalid or expired token');
    });
  });

  describe('getUser static method', () => {
    it('should return user from authenticated event', () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };
      
      const authenticatedEvent: AuthenticatedEvent = {
        ...createMockEvent({}),
        user: mockUser
      };

      const result = AuthMiddleware.getUser(authenticatedEvent);

      expect(result).toEqual(mockUser);
    });

    it('should throw error when user is not in event', () => {
      const authenticatedEvent: AuthenticatedEvent = {
        ...createMockEvent({})
      };

      expect(() => AuthMiddleware.getUser(authenticatedEvent))
        .toThrow('User not authenticated');
    });

    it('should throw error when user is undefined', () => {
      const authenticatedEvent: AuthenticatedEvent = {
        ...createMockEvent({}),
        user: undefined
      };

      expect(() => AuthMiddleware.getUser(authenticatedEvent))
        .toThrow('User not authenticated');
    });
  });

  describe('edge cases', () => {
    it('should handle token extraction correctly with Bearer prefix', () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };
      
      mockJwtService.verifyAccessToken.mockReturnValue(mockUser);
      
      const event = createMockEvent({
        'Authorization': 'Bearer very-long-token-that-should-be-extracted-correctly'
      });

      authMiddleware.authenticate(event);

      expect(mockJwtService.verifyAccessToken).toHaveBeenCalledWith('very-long-token-that-should-be-extracted-correctly');
    });

    it('should handle case sensitivity in Authorization header', () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };
      
      mockJwtService.verifyAccessToken.mockReturnValue(mockUser);
      
      const event = createMockEvent({
        'AUTHORIZATION': 'Bearer token-123'
      });

      // This should fail because the middleware only checks for exact case
      expect(() => authMiddleware.authenticate(event))
        .toThrow('Missing or invalid authorization header');
    });
  });
});
