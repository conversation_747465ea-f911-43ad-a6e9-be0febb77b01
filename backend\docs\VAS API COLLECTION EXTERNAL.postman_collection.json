{"info": {"_postman_id": "9d622e5e-229a-4591-bb00-e264f5bad9e4", "name": "VAS API COLLECTION EXTERNAL", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24808094"}, "item": [{"name": "AUTHENTICATION", "item": [{"name": "AUTHENTICATE", "event": [{"listen": "test", "script": {"exec": ["var response = JSON.parse(responseBody);\r", "if (response.status === 'success') {\r", "    pm.collectionVariables.set(\"token\", response.data.accessToken);\r", "}"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"{{api<PERSON><PERSON>}}\",\r\n    \"password\": \"{{secretKey}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_auth}}/authenticate", "host": ["{{base_url_auth}}"], "path": ["authenticate"]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"PK_TEST_2Aufw49wci\",\r\n    \"password\": \"EdQDUBMrf4pekY4dXkq2Ki8WSHJ9BM0z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_auth}}/authenticate", "host": ["{{base_url_auth}}"], "path": ["authenticate"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "<PERSON><PERSON>, 28 Mar 2023 13:37:01 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Authentication Successful\",\n    \"data\": {\n        \"accessToken\": \"eyJ0eXAiOiJKV1QiLCJrZXlJZCI6InZhc19qd3QiLCJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************************************************************************************************************************************************************************.h9kb-ii-RZ-p9TjCbfhna8hAMbAfFTufCOhsu9Peh6MrZ28a2UuTr3vuHCDNYu0FPxqPlhLP0kFOo-1rCYB4eQ\",\n        \"expiresIn\": 7200000\n    }\n}"}, {"name": "INVALID CREDENTIAL", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"PK_TEST_2Aufw49wi\",\r\n    \"password\": \"EdQDUBMrf4pekY4dXkq2Ki8WSHJ9BM0z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_auth}}/authenticate", "host": ["{{base_url_auth}}"], "path": ["authenticate"]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "<PERSON><PERSON>, 28 Mar 2023 13:38:18 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"401\",\n    \"message\": \"Bad credentials\"\n}"}]}]}, {"name": "TOP-UP", "item": [{"name": "GET NETWORK", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/topup/network?phone=08135686327", "host": ["{{base_url_vas}}"], "path": ["topup", "network"], "query": [{"key": "phone", "value": "08135686327"}]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/topup/network?phone=08136863210", "host": ["{{base_url_vas}}"], "path": ["topup", "network"], "query": [{"key": "phone", "value": "08136863210"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "<PERSON><PERSON>, 28 Mar 2023 13:40:28 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Phone network retrieved\",\n    \"data\": {\n        \"network\": \"MTN\"\n    }\n}"}, {"name": "FAILED", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/topup/network?phone=0813686321", "host": ["{{base_url_vas}}"], "path": ["topup", "network"], "query": [{"key": "phone", "value": "0813686321"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "<PERSON><PERSON>, 28 Mar 2023 13:41:15 GMT"}, {"key": "Connection", "value": "close"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"500\",\n    \"message\": \"Error getting phone network\"\n}"}, {"name": "BAD REQUEST, INVALID PHONE NUMBER", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas1}}/topup/network?phone=0813686321", "host": ["{{base_url_vas1}}"], "path": ["topup", "network"], "query": [{"key": "phone", "value": "0813686321"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "<PERSON><PERSON>, 11 Apr 2023 11:54:19 GMT"}, {"key": "Connection", "value": "close"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"400\",\n    \"message\": \"Phone number is not a valid NGN number\"\n}"}]}, {"name": "GET DATA PLANS", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/topup/dataPlans?phone=***********", "host": ["{{base_url_vas}}"], "path": ["topup", "dataPlans"], "query": [{"key": "phone", "value": "***********"}]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/topup/dataPlans?phone=08136863210", "host": ["{{base_url_vas}}"], "path": ["topup", "dataPlans"], "query": [{"key": "phone", "value": "08136863210"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "<PERSON><PERSON>, 28 Mar 2023 13:42:08 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Data plans retrieved\",\n    \"data\": [\n        {\n            \"productId\": \"MTN-75MB-1\",\n            \"dataBundle\": \"75MB\",\n            \"amount\": \"100\",\n            \"validity\": \"1day\"\n        },\n        {\n            \"productId\": \"MTN-200MB-2\",\n            \"dataBundle\": \"200MB\",\n            \"amount\": \"200\",\n            \"validity\": \"2days\"\n        },\n        {\n            \"productId\": \"MTN-XT300-37\",\n            \"dataBundle\": \"XT300\",\n            \"amount\": \"300\",\n            \"validity\": \"XT300Wkly\"\n        },\n        {\n            \"productId\": \"MTN-XD300-45\",\n            \"dataBundle\": \"XD300\",\n            \"amount\": \"300\",\n            \"validity\": \"XD300Wkly\"\n        },\n        {\n            \"productId\": \"MTN-350MB-19\",\n            \"dataBundle\": \"350MB\",\n            \"amount\": \"300\",\n            \"validity\": \"7days\"\n        },\n        {\n            \"productId\": \"MTN-2.5GB-27\",\n            \"dataBundle\": \"2.5GB\",\n            \"amount\": \"500\",\n            \"validity\": \"2days\"\n        },\n        {\n            \"productId\": \"MTN-750MB-28\",\n            \"dataBundle\": \"750MB\",\n            \"amount\": \"500\",\n            \"validity\": \"14days\"\n        },\n        {\n            \"productId\": \"MTN-XT500-38\",\n            \"dataBundle\": \"XT500\",\n            \"amount\": \"500\",\n            \"validity\": \"XT500Wkly\"\n        },\n        {\n            \"productId\": \"MTN-750MB-26\",\n            \"dataBundle\": \"750MB\",\n            \"amount\": \"500\",\n            \"validity\": \"7days\"\n        },\n        {\n            \"productId\": \"MTN-2GB-55\",\n            \"dataBundle\": \"2GB\",\n            \"amount\": \"500\",\n            \"validity\": \"2Days\"\n        },\n        {\n            \"productId\": \"MTN-XD500-46\",\n            \"dataBundle\": \"XD500\",\n            \"amount\": \"500\",\n            \"validity\": \"XD500Wkly\"\n        },\n        {\n            \"productId\": \"MTN-2GB-58\",\n            \"dataBundle\": \"2GB\",\n            \"amount\": \"500\",\n            \"validity\": \"2Days\"\n        },\n        {\n            \"productId\": \"MTN-XD1000-47\",\n            \"dataBundle\": \"XD1000\",\n            \"amount\": \"1000\",\n            \"validity\": \"XD1000Mthly\"\n        },\n        {\n            \"productId\": \"MTN-1.5GB-4\",\n            \"dataBundle\": \"1.5GB\",\n            \"amount\": \"1000\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-XT1000-39\",\n            \"dataBundle\": \"XT1000\",\n            \"amount\": \"1000\",\n            \"validity\": \"XT1000Mthly\"\n        },\n        {\n            \"productId\": \"MTN-2GB-20\",\n            \"dataBundle\": \"2GB\",\n            \"amount\": \"1200\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-6GB-29\",\n            \"dataBundle\": \"6GB\",\n            \"amount\": \"1500\",\n            \"validity\": \"7days\"\n        },\n        {\n            \"productId\": \"MTN-3GB-5\",\n            \"dataBundle\": \"3GB\",\n            \"amount\": \"1500\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-XT2000-40\",\n            \"dataBundle\": \"XT2000\",\n            \"amount\": \"2000\",\n            \"validity\": \"XT2000Mthly\"\n        },\n        {\n            \"productId\": \"MTN-4.5GB-6\",\n            \"dataBundle\": \"4.5GB\",\n            \"amount\": \"2000\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-XD2000-48\",\n            \"dataBundle\": \"XD2000\",\n            \"amount\": \"2000\",\n            \"validity\": \"XD2000Mthly\"\n        },\n        {\n            \"productId\": \"MTN-6GB-21\",\n            \"dataBundle\": \"6GB\",\n            \"amount\": \"2500\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-10GB-22\",\n            \"dataBundle\": \"10GB\",\n            \"amount\": \"3000\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-XD5000-49\",\n            \"dataBundle\": \"XD5000\",\n            \"amount\": \"5000\",\n            \"validity\": \"XD5000Mthly\"\n        },\n        {\n            \"productId\": \"MTN-XT5000-41\",\n            \"dataBundle\": \"XT5000\",\n            \"amount\": \"5000\",\n            \"validity\": \"XT5000Mthly\"\n        },\n        {\n            \"productId\": \"MTN-20GB-7\",\n            \"dataBundle\": \"20GB\",\n            \"amount\": \"5000\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-25GB-8\",\n            \"dataBundle\": \"25GB\",\n            \"amount\": \"5000\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-30GB-53\",\n            \"dataBundle\": \"30GB\",\n            \"amount\": \"8000\",\n            \"validity\": \"60days\"\n        },\n        {\n            \"productId\": \"MTN-XD10000-50\",\n            \"dataBundle\": \"XD10000\",\n            \"amount\": \"10000\",\n            \"validity\": \"XD10000Mthl\"\n        },\n        {\n            \"productId\": \"MTN-40GB-9\",\n            \"dataBundle\": \"40GB\",\n            \"amount\": \"10000\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-XT10000-42\",\n            \"dataBundle\": \"XT10000\",\n            \"amount\": \"10000\",\n            \"validity\": \"XT10000Mthl\"\n        },\n        {\n            \"productId\": \"MTN-35GB-23\",\n            \"dataBundle\": \"35GB\",\n            \"amount\": \"13500\",\n            \"validity\": \"SME\"\n        },\n        {\n            \"productId\": \"MTN-XT15000-43\",\n            \"dataBundle\": \"XT15000\",\n            \"amount\": \"15000\",\n            \"validity\": \"XT15000Mthl\"\n        },\n        {\n            \"productId\": \"MTN-XD15000-51\",\n            \"dataBundle\": \"XD15000\",\n            \"amount\": \"15000\",\n            \"validity\": \"XD15000Mthl\"\n        },\n        {\n            \"productId\": \"MTN-75GB-13\",\n            \"dataBundle\": \"75GB\",\n            \"amount\": \"15000\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-XT20000-44\",\n            \"dataBundle\": \"XT20000\",\n            \"amount\": \"20000\",\n            \"validity\": \"XT20000Mthl\"\n        },\n        {\n            \"productId\": \"MTN-75GB-14\",\n            \"dataBundle\": \"75GB\",\n            \"amount\": \"20000\",\n            \"validity\": \"60days\"\n        },\n        {\n            \"productId\": \"MTN-110GB-30\",\n            \"dataBundle\": \"110GB\",\n            \"amount\": \"20000\",\n            \"validity\": \"30days\"\n        },\n        {\n            \"productId\": \"MTN-XD20000-52\",\n            \"dataBundle\": \"XD20000\",\n            \"amount\": \"20000\",\n            \"validity\": \"XD20000Mthl\"\n        },\n        {\n            \"productId\": \"MTN-120GB-15\",\n            \"dataBundle\": \"120GB\",\n            \"amount\": \"30000\",\n            \"validity\": \"60days\"\n        },\n        {\n            \"productId\": \"MTN-90GB-24\",\n            \"dataBundle\": \"90GB\",\n            \"amount\": \"40000\",\n            \"validity\": \"SME\"\n        },\n        {\n            \"productId\": \"MTN-150GB-3\",\n            \"dataBundle\": \"150GB\",\n            \"amount\": \"50000\",\n            \"validity\": \"SME_90days\"\n        },\n        {\n            \"productId\": \"MTN-150GB-16\",\n            \"dataBundle\": \"150GB\",\n            \"amount\": \"50000\",\n            \"validity\": \"90days\"\n        },\n        {\n            \"productId\": \"MTN-250GB-17\",\n            \"dataBundle\": \"250GB\",\n            \"amount\": \"75000\",\n            \"validity\": \"90days\"\n        },\n        {\n            \"productId\": \"MTN-1TB-33\",\n            \"dataBundle\": \"1TB\",\n            \"amount\": \"100000\",\n            \"validity\": \"365days\"\n        },\n        {\n            \"productId\": \"MTN-325MB-56\",\n            \"dataBundle\": \"325GB\",\n            \"amount\": \"100000\",\n            \"validity\": \"180Days\"\n        },\n        {\n            \"productId\": \"MTN-400GB-34\",\n            \"dataBundle\": \"400GB\",\n            \"amount\": \"120000\",\n            \"validity\": \"365days\"\n        },\n        {\n            \"productId\": \"MTN-1000GB-35\",\n            \"dataBundle\": \"1000GB\",\n            \"amount\": \"300000\",\n            \"validity\": \"365days\"\n        },\n        {\n            \"productId\": \"MTN-2000GB-36\",\n            \"dataBundle\": \"2000GB\",\n            \"amount\": \"450000\",\n            \"validity\": \"365days\"\n        },\n        {\n            \"productId\": \"MTN-1500GB-57\",\n            \"dataBundle\": \"1500GB\",\n            \"amount\": \"450000\",\n            \"validity\": \"365Days\"\n        }\n    ]\n}"}, {"name": "FAILED", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/topup/dataPlans?phone=0813686321", "host": ["{{base_url_vas}}"], "path": ["topup", "dataPlans"], "query": [{"key": "phone", "value": "0813686321"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "<PERSON><PERSON>, 28 Mar 2023 13:42:27 GMT"}, {"key": "Connection", "value": "close"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"500\",\n    \"message\": \"Error getting data plans\"\n}"}, {"name": "BAD REQUEST - INVALID PHONE NUMBER", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas1}}/topup/dataPlans?phone=0813686321", "host": ["{{base_url_vas1}}"], "path": ["topup", "dataPlans"], "query": [{"key": "phone", "value": "0813686321"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "<PERSON><PERSON>, 11 Apr 2023 11:55:45 GMT"}, {"key": "Connection", "value": "close"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"400\",\n    \"message\": \"Phone number is not a valid NGN number\"\n}"}]}, {"name": "AIRTIME-TOPUP", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"phoneNumber\": \"**********\",\r\n    \"network\": \"MTN\",\r\n    \"amount\": \"100\",\r\n    \"debitAccount\": \"**********\",\r\n    \"transactionReference\": \"20240205124100000000\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/topup/airtime", "host": ["{{base_url_vas}}"], "path": ["topup", "airtime"]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"phoneNumber\": \"***********\",\r\n    \"network\": \"MTN\",\r\n    \"amount\": \"100\",\r\n    \"debitAccount\": \"********\",\r\n    \"transactionReference\": \"VAS29PSBA000000024\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/topup/airtime", "host": ["{{base_url_vas}}"], "path": ["topup", "airtime"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 03 Apr 2023 09:07:58 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Airtime topup successful\",\n    \"data\": {\n        \"recipient\": \"***********\",\n        \"amount\": \"100\",\n        \"network\": \"MTN\",\n        \"transactionReference\": \"2304031007421680414\"\n    }\n}"}]}, {"name": "DATA-TOPUP", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"phoneNumber\": \"***********\",\r\n    \"amount\": \"200\",\r\n    \"debitAccount\": \"********\",\r\n    \"network\": \"MTN\",\r\n    \"productId\": \"MTN-200MB-2\",\r\n    \"transactionReference\": \"VAS29PSBD000000014\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/topup/data", "host": ["{{base_url_vas}}"], "path": ["topup", "data"]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"phoneNumber\": \"***********\",\r\n    \"amount\": \"200\",\r\n    \"debitAccount\": \"********\",\r\n    \"network\": \"MTN\",\r\n    \"productId\": \"MTN-200MB-2\",\r\n    \"transactionReference\": \"VAS29PSBD000000012\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/topup/data", "host": ["{{base_url_vas}}"], "path": ["topup", "data"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 03 Apr 2023 09:29:35 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Data topup Successful\",\n    \"data\": {\n        \"recipient\": \"***********\",\n        \"network\": \"MTN\",\n        \"amount\": \"200\",\n        \"dataPlan\": \"MTN-200MB-2\",\n        \"transactionReference\": \"2304031029193386661\"\n    }\n}"}]}, {"name": "GET TOPUP STATUS", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/topup/status?transReference=FATOP20240120000000", "host": ["{{base_url_vas}}"], "path": ["topup", "status"], "query": [{"key": "transReference", "value": "FATOP20240120000000"}]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/topup/status?transReference=VAS29PSBA00000024", "host": ["{{base_url_vas}}"], "path": ["topup", "status"], "query": [{"key": "transReference", "value": "VAS29PSBA00000024"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 05 Apr 2023 08:15:47 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"data\": {\n        \"transactionStatus\": \"success\",\n        \"description\": \"Topup transaction successful\"\n    }\n}"}, {"name": "INVALID TRANSACTION REFERENCE", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/topup/status?transReference=VAS29PSBA00000054", "host": ["{{base_url_vas}}"], "path": ["topup", "status"], "query": [{"key": "transReference", "value": "VAS29PSBA00000054"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 08:28:21 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"404\",\n    \"message\": \"Invalid transaction reference\"\n}"}]}]}, {"name": "BILLSPAYMENT", "item": [{"name": "GET CATEGORIES", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/billspayment/categories", "host": ["{{base_url_vas}}"], "path": ["billspayment", "categories"]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/billspayment/categories", "host": ["{{base_url_vas}}"], "path": ["billspayment", "categories"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "<PERSON><PERSON>, 28 Mar 2023 13:50:47 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Categories retrieved\",\n    \"data\": [\n        {\n            \"id\": \"1\",\n            \"name\": \"ELECTRICITY\"\n        },\n        {\n            \"id\": \"2\",\n            \"name\": \"BETTING/LOTTERY\"\n        },\n        {\n            \"id\": \"4\",\n            \"name\": \"CABLE TV\"\n        },\n        {\n            \"id\": \"7\",\n            \"name\": \"INTERNET SERVICES\"\n        },\n        {\n            \"id\": \"8\",\n            \"name\": \"WILLS & PROTECTION\"\n        }\n    ]\n}"}]}, {"name": "GET CATEGORY BILLERS", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/billspayment/billers/1", "host": ["{{base_url_vas}}"], "path": ["billspayment", "billers", "1"]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/billspayment/billers/1", "host": ["{{base_url_vas}}"], "path": ["billspayment", "billers", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 03 Apr 2023 11:46:31 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Billers Retrieved\",\n    \"data\": [\n        {\n            \"id\": \"CW-01\",\n            \"name\": \"IKEDC\"\n        },\n        {\n            \"id\": \"CW-02\",\n            \"name\": \"EKEDC\"\n        },\n        {\n            \"id\": \"CW-03\",\n            \"name\": \"KEDC\"\n        },\n        {\n            \"id\": \"CW-04\",\n            \"name\": \"AEDC\"\n        },\n        {\n            \"id\": \"CW-05\",\n            \"name\": \"EKEDC\"\n        },\n        {\n            \"id\": \"CW-06\",\n            \"name\": \"ENEDC\"\n        },\n        {\n            \"id\": \"CW-07\",\n            \"name\": \"IBEDC\"\n        },\n        {\n            \"id\": \"CW-08\",\n            \"name\": \"JED<PERSON>\"\n        },\n        {\n            \"id\": \"CW-09\",\n            \"name\": \"KAEDC\"\n        },\n        {\n            \"id\": \"CW-10\",\n            \"name\": \"PHEDC\"\n        },\n        {\n            \"id\": \"BP-ABUJA\",\n            \"name\": \"AEDC\"\n        },\n        {\n            \"id\": \"BP-EKO\",\n            \"name\": \"EKEDC\"\n        },\n        {\n            \"id\": \"BP-IKEJA\",\n            \"name\": \"IKEDC\"\n        },\n        {\n            \"id\": \"BP-IBADAN\",\n            \"name\": \"IEDC\"\n        },\n        {\n            \"id\": \"BP-ENUGU\",\n            \"name\": \"EEDC\"\n        },\n        {\n            \"id\": \"BP-PH\",\n            \"name\": \"PHED\"\n        },\n        {\n            \"id\": \"BP-JOS\",\n            \"name\": \"JEDC\"\n        },\n        {\n            \"id\": \"BP-KADUNA\",\n            \"name\": \"KEDC\"\n        },\n        {\n            \"id\": \"BP-KANO\",\n            \"name\": \"KEDCO\"\n        }\n    ]\n}"}, {"name": "INVALID CATEGORY ID", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/billspayment/billers/5", "host": ["{{base_url_vas}}"], "path": ["billspayment", "billers", "5"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 08:43:43 GMT"}, {"key": "Connection", "value": "close"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"400\",\n    \"message\": \"Category not available\"\n}"}]}, {"name": "GET BILLER INPUT FIELDS", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/fields/BP-IKEJA", "host": ["{{base_url_vas}}"], "path": ["billspayment", "fields", "BP-IKEJA"]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/fields/BP-EKO", "host": ["{{base_url_vas}}"], "path": ["billspayment", "fields", "BP-EKO"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 03 Apr 2023 11:22:35 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Biller Input fields retrieved\",\n    \"data\": [\n        {\n            \"fieldName\": \"customerId\",\n            \"fieldDescription\": \"Enter Meter number\",\n            \"validation\": \"Y\"\n        },\n        {\n            \"fieldName\": \"amount\",\n            \"fieldDescription\": \"Enter Amount to Pay\",\n            \"validation\": \"Y\"\n        },\n        {\n            \"fieldName\": \"itemId\",\n            \"fieldDescription\": \"Select vend type Option\",\n            \"validation\": \"Y\",\n            \"isSelectData\": \"Y\",\n            \"items\": [\n                {\n                    \"itemId\": \"VT01\",\n                    \"itemName\": \"PREPAID\"\n                },\n                {\n                    \"itemId\": \"VT02\",\n                    \"itemName\": \"POSTPAID\"\n                }\n            ]\n        }\n    ]\n}"}, {"name": "INVALID BILLER ID", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/fields/BP-EKE", "host": ["{{base_url_vas}}"], "path": ["billspayment", "fields", "BP-EKE"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 08:51:55 GMT"}, {"key": "Connection", "value": "close"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"400\",\n    \"message\": \"Biller fields not available\"\n}"}]}, {"name": "VALIDATE BILLER INPUT", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"34382\",\r\n    \"amount\": \"1000\",\r\n    \"billerId\": \"CW-B02T\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/validate", "host": ["{{base_url_vas}}"], "path": ["billspayment", "validate"]}}, "response": [{"name": "SUCCESS- ELECTRICITY VALIDATION", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"***********\",\r\n    \"billerId\": \"CW-01\",\r\n    \"itemId\": \"E01E\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/validate", "host": ["{{base_url_vas}}"], "path": ["billspayment", "validate"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 03 Apr 2023 11:25:21 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Validation successful\",\n    \"data\": {\n        \"customerName\": \"Funmilade Abiodun\",\n        \"otherField\": \"90 Mogbadunade Street 91398 ChinyereVille\"\n    }\n}"}, {"name": "FAILED", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"***********\",\r\n    \"billerId\": \"CW-11\",\r\n    \"itemId\": \"E10E\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/validate", "host": ["{{base_url_vas}}"], "path": ["billspayment", "validate"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 10:16:52 GMT"}, {"key": "Connection", "value": "close"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"500\",\n    \"message\": \"Error validating biller inputs\"\n}"}, {"name": "SUCCESS - BET9JA VALIDATION", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"34382\",\r\n    \"billerId\": \"CW-B01T\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/validate", "host": ["{{base_url_vas}}"], "path": ["billspayment", "validate"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 11:11:11 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Validation successful\",\n    \"data\": {\n        \"customerName\": \"Test Account 2/\",\n        \"otherField\": \"Bet9ja\"\n    }\n}"}, {"name": "SUCCESS - AWOOF VALIDATION", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"***********\",\r\n    \"billerId\": \"AWF-001\",\r\n    \"itemId\": \"1086\",\r\n    \"firstname\": \"<PERSON><PERSON>ez<PERSON>\",\r\n    \"lastname\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/validate", "host": ["{{base_url_vas}}"], "path": ["billspayment", "validate"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 11:12:48 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Validation successful\",\n    \"data\": {\n        \"customerName\": \"Chibueze Nwajiobi\",\n        \"amount\": \"1000\",\n        \"otherField\": \"\"\n    }\n}"}, {"name": "FAILED - <PERSON><PERSON><PERSON><PERSON><PERSON>D FIELD MISSING", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"***********\",\r\n    \"itemId\": \"E10E\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/validate", "host": ["{{base_url_vas}}"], "path": ["billspayment", "validate"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 11:13:46 GMT"}, {"key": "Connection", "value": "close"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"400\",\n    \"message\": \"billerId is required\"\n}"}]}, {"name": "INITIATE BILLS PAYMENT TRANSACTION", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"34382\",\r\n    \"billerId\": \"BET9JA\",\r\n    \"customerPhone\": \"***********\",\r\n    \"customerName\": \"SDASXL SSLSSSS/dijsssp84\",\r\n    \"otherField\": \"98acbe87c3be9a4083c05cd87db4c238\",\r\n    \"debitAccount\": \"**********\",\r\n    \"amount\": \"500\",\r\n    \"transactionReference\": \"VAS202407310000000001\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/pay", "host": ["{{base_url_vas}}"], "path": ["billspayment", "pay"]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"***********\",\r\n    \"billerId\": \"CW-01\",\r\n    \"itemId\": \"E01E\",\r\n    \"customerPhone\": \"***********\",\r\n    \"customerName\": \"<PERSON>\",\r\n    \"otherField\": \"2, Johnson street\",\r\n    \"debitAccount\": \"********\",\r\n    \"amount\": \"1000\",\r\n    \"transactionReference\": \"VAS29PSBE000000051\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/pay", "host": ["{{base_url_vas}}"], "path": ["billspayment", "pay"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 03 Apr 2023 11:25:35 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Payment Successful\",\n    \"data\": {\n        \"otherField\": \"Units Purchased: 3.93\",\n        \"isToken\": true,\n        \"token\": \"26095759226633748739\"\n    }\n}"}, {"name": "BAD REQUEST - MISSING REQUIRED FIELD", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"***********\",\r\n    \"itemId\": \"E01E\",\r\n    \"customerPhone\": \"**********\",\r\n    \"customerName\": \"<PERSON>\",\r\n    \"otherField\": \"2, Johnson street\",\r\n    \"debitAccount\": \"********\",\r\n    \"amount\": \"1000\",\r\n    \"transactionReference\": \"VAS29PSBE000000041\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/pay", "host": ["{{base_url_vas}}"], "path": ["billspayment", "pay"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 10:23:09 GMT"}, {"key": "Connection", "value": "close"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"400\",\n    \"message\": \"billerId is required\"\n}"}, {"name": "FAILED - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TRANSACTION REFERENCE", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"customerId\": \"**********\",\r\n    \"billerId\": \"CW-01\",\r\n    \"itemId\": \"E01E\",\r\n    \"customerPhone\": \"***********\",\r\n    \"customerName\": \"<PERSON>\",\r\n    \"otherField\": \"2, Johnson street\",\r\n    \"debitAccount\": \"********\",\r\n    \"amount\": \"1000\",\r\n    \"transactionReference\": \"VAS29PSBE000000021\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_vas}}/billspayment/pay", "host": ["{{base_url_vas}}"], "path": ["billspayment", "pay"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 10:24:24 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"300\",\n    \"message\": \"Duplicate Slip/Instrument No. 'VT1_VAS29PSBE000000021' detected.\"\n}"}]}, {"name": "GET BILLSPAYMENT STATUS", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/billspayment/status?transReference=VASTEST202401200000", "host": ["{{base_url_vas}}"], "path": ["billspayment", "status"], "query": [{"key": "transReference", "value": "VASTEST202401200000"}]}}, "response": [{"name": "SUCCESS", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/billspayment/status?transReference=VAS29PSBE000000022", "host": ["{{base_url_vas}}"], "path": ["billspayment", "status"], "query": [{"key": "transReference", "value": "VAS29PSBE000000022"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 11:07:24 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"responseCode\": \"200\",\n    \"message\": \"Transaction status retrieved\",\n    \"data\": {\n        \"transactionStatus\": \"success\",\n        \"description\": \"successful\\nToken:\\n23418570928238292127\\nUnits Purchased: 7.8\"\n    }\n}"}, {"name": "FAILED - <PERSON><PERSON><PERSON><PERSON> TRANSACTION REFERENCE", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url_vas}}/billspayment/status?transReference=VAS29PSBE000000023", "host": ["{{base_url_vas}}"], "path": ["billspayment", "status"], "query": [{"key": "transReference", "value": "VAS29PSBE000000023"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 12 Apr 2023 11:08:10 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"failed\",\n    \"responseCode\": \"404\",\n    \"message\": \"Invalid transaction reference\"\n}"}]}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url_auth", "value": "http://**************:9090/identity/api/v1", "type": "string"}, {"key": "base_url_vas", "value": "http://**************:9090/vas/api/v1", "type": "string"}, {"key": "secret<PERSON>ey", "value": "", "type": "string"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "", "type": "string"}, {"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJrZXlJZCI6InZhc19qd3QiLCJhbGciOiJIUzUxMiJ9.eyJhdXRob3JpdGllcyI6WyJCSUxMU19QQVlNRU5UIiwiVE9QX1VQIl0sInN1YiI6IkNVUlJFWCIsImlzcyI6Ijlwc2IuY29tLm5nIiwiaWF0IjoxNzAxMDI3ODk2LCJqdGkiOiJkMGFlMjZjOS1iOWRkLTQ1ZDgtYjQyYi03ZTQxMTE4MDQ1ZTUiLCJleHAiOjE3MDEwMzUwOTZ9.AJo95WeI65_HsnPWV-fdGZPZlYBMV1I0hbhMeMDR1kOLH4lrqj0Jy6wrp-uOYLcLMXtoRV8yFbVQcQ8Ek1e_VA", "type": "string"}]}