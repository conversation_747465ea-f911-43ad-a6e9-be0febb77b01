import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { BillService } from '../../../services/bill.service';
import { validateBillValidation } from '../../../common/validation/bill.validation';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const body = JSON.parse(event.body || '{}');
    const { error } = validateBillValidation(body);
    if (error) {
      logger.warn('Validation failed', { details: error.details });
      return errorResponse(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    
    const billService = new BillService();
    const validationResult = await billService.validateCustomer(body);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: validationResult })
    };
  } catch (err: any) {
    logger.error('Bill validation error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred during bill validation');
  }
};
