import { APIGatewayProxyEvent } from 'aws-lambda';
import { main } from '../../src/api/v1/admin/stats.handler';
import { AdminService, DashboardStats } from '../../src/services/admin.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';
import { DatabaseConfig } from '../../src/config/database';
import { TokenPayload } from '../../src/common/security/jwt.service';

// Mock dependencies
jest.mock('../../src/services/admin.service');
jest.mock('../../src/common/middlewares/auth.middleware');
jest.mock('../../src/config/database');

const mockAdminService = AdminService as jest.MockedClass<typeof AdminService>;
const mockAuthMiddleware = AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>;

describe('Admin Stats Handler', () => {
  let mockEvent: APIGatewayProxyEvent;
  let mockAdminServiceInstance: jest.Mocked<AdminService>;
  let mockAuthMiddlewareInstance: jest.Mocked<AuthMiddleware>;

  beforeEach(() => {
    jest.clearAllMocks();

    mockEvent = {
      headers: {
        Authorization: 'Bearer valid-token'
      },
      body: null,
      pathParameters: null,
      queryStringParameters: null,
      httpMethod: 'GET',
      path: '/admin/stats',
      resource: '/admin/stats',
      requestContext: {} as any,
      isBase64Encoded: false,
      multiValueHeaders: {},
      multiValueQueryStringParameters: null,
      stageVariables: null
    };

    // Mock AdminService instance
    mockAdminServiceInstance = {
      getDashboardStats: jest.fn(),
      getUsers: jest.fn(),
      getTransactions: jest.fn(),
      suspendUser: jest.fn(),
      activateUser: jest.fn(),
      approveKYC: jest.fn(),
      rejectKYC: jest.fn(),
      getUserDetails: jest.fn(),
    } as unknown as jest.Mocked<AdminService>;

    mockAdminService.mockImplementation(() => mockAdminServiceInstance);

    // Mock AuthMiddleware instance
    mockAuthMiddlewareInstance = {
      authenticate: jest.fn(),
    } as unknown as jest.Mocked<AuthMiddleware>;

    mockAuthMiddleware.mockImplementation(() => mockAuthMiddlewareInstance);

    // Mock DatabaseConfig
    const mockDbInstance = {
      connect: jest.fn(),
      disconnect: jest.fn(),
      getConnection: jest.fn(),
      isConnected: true,
    };

    (DatabaseConfig.getInstance as jest.Mock) = jest.fn().mockReturnValue(mockDbInstance);
  });
  describe('successful stats retrieval', () => {
    it('should return admin dashboard stats successfully', async () => {
      const mockUser: TokenPayload = { 
        userId: 'admin1', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      };
      const mockStats: DashboardStats = {
        totalUsers: 100,
        totalTransactions: 500,
        totalVolume: 1000000,
        activeUsers: 75,
        averageTransactionValue: 2000,
        revenueThisMonth: 50000,
        growthRate: 15.5,
        topTransactionCategories: [
          { category: 'Bills', count: 200, volume: 400000 },
          { category: 'Transfer', count: 300, volume: 600000 }
        ],
        userRegistrationTrend: [
          { date: '2024-01-01', count: 10 },
          { date: '2024-01-02', count: 15 }
        ]
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockAdminServiceInstance.getDashboardStats.mockResolvedValue(mockStats);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockStats
      });
      expect(mockAuthMiddlewareInstance.authenticate).toHaveBeenCalledWith(mockEvent);
      expect(mockAdminServiceInstance.getDashboardStats).toHaveBeenCalled();
    });

    it('should handle empty stats', async () => {
      const mockUser: TokenPayload = { 
        userId: 'admin1', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      };
      const mockStats: DashboardStats = {
        totalUsers: 0,
        totalTransactions: 0,
        totalVolume: 0,
        activeUsers: 0,
        averageTransactionValue: 0,
        revenueThisMonth: 0,
        growthRate: 0,
        topTransactionCategories: [],
        userRegistrationTrend: []
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockAdminServiceInstance.getDashboardStats.mockResolvedValue(mockStats);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockStats
      });
    });
  });

  describe('authentication failures', () => {
    it('should return 500 when authentication fails', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const result = await main(mockEvent);      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Missing or invalid authorization header');
    });

    it('should return 500 for invalid token', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Invalid or expired token');
      });

      const result = await main(mockEvent);      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Invalid or expired token');
    });
  });
  describe('service errors', () => {
    it('should handle admin service errors', async () => {
      const mockUser: TokenPayload = { 
        userId: 'admin1', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockAdminServiceInstance.getDashboardStats.mockRejectedValue(new Error('Database connection failed'));      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Database connection failed');
    });

    it('should handle database connection errors', async () => {
      const mockConnect = jest.fn().mockRejectedValue(new Error('Database connection failed'));
      (DatabaseConfig.getInstance as jest.Mock) = jest.fn().mockReturnValue({
        connect: mockConnect,
        disconnect: jest.fn(),
        getConnection: jest.fn(),
      });      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Database connection failed');
    });
  });

  describe('edge cases', () => {
    it('should handle missing Authorization header', async () => {
      mockEvent.headers = {};

      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing authorization header');
      });      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Missing authorization header');
    });

    it('should handle malformed Authorization header', async () => {
      mockEvent.headers = { Authorization: 'Invalid header' };

      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Malformed authorization header');
      });      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(body.error.message).toBe('Malformed authorization header');
    });
  });
});
