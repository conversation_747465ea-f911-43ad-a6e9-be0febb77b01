import { UserRepository } from '../repositories/user.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { TransactionService } from './transaction.service';
import { WalletService } from './wallet.service';
import { DashboardStats } from '../interfaces/services';

export class AdminService {
  private userRepository = new UserRepository();
  private transactionRepository = new TransactionRepository();
  private transactionService = new TransactionService();
  private walletService = new WalletService();

  async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Get current month start date
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

      // Fetch data in parallel
      const [
        userStats,
        transactionStats,
        lastMonthStats,
        topCategories,
        registrationTrend
      ] = await Promise.all([
        this.userRepository.getStats(),
        this.transactionRepository.getStats(startOfMonth),
        this.transactionRepository.getStats(startOfLastMonth, endOfLastMonth),
        this.transactionRepository.getTopCategories(4),
        this.userRepository.getUserRegistrationTrend(6)
      ]);

      // Calculate growth rate
      const currentRevenue = transactionStats.totalFees;
      const lastMonthRevenue = lastMonthStats.totalFees;
      const growthRate = lastMonthRevenue > 0 
        ? ((currentRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
        : 0;

      return {
        totalUsers: userStats.totalUsers,
        activeUsers: userStats.activeUsers,
        totalTransactions: transactionStats.totalTransactions,
        totalVolume: transactionStats.totalVolume,
        averageTransactionValue: transactionStats.averageTransactionValue || 0,
        revenueThisMonth: currentRevenue,
        growthRate: Math.round(growthRate * 100) / 100,
        topTransactionCategories: topCategories,
        userRegistrationTrend: registrationTrend
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Return default stats in case of error
      return {
        totalUsers: 0,
        activeUsers: 0,
        totalTransactions: 0,
        totalVolume: 0,
        averageTransactionValue: 0,
        revenueThisMonth: 0,
        growthRate: 0,
        topTransactionCategories: [],
        userRegistrationTrend: []
      };
    }
  }

  async getUsers(queryParams: any) {
    const {
      page = 1,
      limit = 50,
      search,
      status,
      role
    } = queryParams;

    try {
      const result = await this.userRepository.findMany({
        page: parseInt(page),
        limit: parseInt(limit),
        search,
        status,
        role
      });

      const stats = await this.userRepository.getStats();

      return {
        users: result.users,
        pagination: {
          total: result.total,
          pages: result.pages,
          currentPage: result.page,
          limit: parseInt(limit)
        },
        summary: {
          totalUsers: stats.totalUsers,
          verifiedUsers: stats.verifiedUsers,
          activeUsers: stats.activeUsers
        }
      };
    } catch (error) {
      console.error('Error fetching users:', error);
      throw new Error('Failed to fetch users');
    }
  }

  async getTransactions(queryParams: any) {
    const {
      page = 1,
      limit = 50,
      status,
      type,
      startDate,
      endDate,
      userId
    } = queryParams;

    try {
      const options: any = {
        page: parseInt(page),
        limit: parseInt(limit)
      };

      if (status) options.status = status;
      if (type) options.type = type;
      if (userId) options.userId = userId;
      if (startDate) options.startDate = new Date(startDate);
      if (endDate) options.endDate = new Date(endDate);

      const result = await this.transactionRepository.findMany(options);
      const stats = await this.transactionRepository.getStats(
        options.startDate,
        options.endDate
      );

      return {
        transactions: result.transactions,
        pagination: {
          total: result.total,
          pages: result.pages,
          currentPage: result.page,
          limit: parseInt(limit)
        },
        summary: {
          totalTransactions: stats.totalTransactions,
          totalVolume: stats.totalVolume,
          totalFees: stats.totalFees,
          completedTransactions: stats.completedTransactions
        }
      };
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw new Error('Failed to fetch transactions');
    }
  }

  // User management methods
  async suspendUser(userId: string, reason: string) {
    try {
      await this.userRepository.update(userId, { 
        status: 'SUSPENDED'
      });
      
      // TODO: Log admin action for audit purposes
      return { success: true, message: 'User suspended successfully' };
    } catch (error) {
      console.error('Error suspending user:', error);
      throw new Error('Failed to suspend user');
    }
  }

  async activateUser(userId: string) {
    try {
      await this.userRepository.update(userId, { 
        status: 'ACTIVE'
      });
      
      // TODO: Log admin action for audit purposes
      return { success: true, message: 'User activated successfully' };
    } catch (error) {
      console.error('Error activating user:', error);
      throw new Error('Failed to activate user');
    }
  }

  async approveKYC(userId: string) {
    try {
      await this.userRepository.update(userId, { 
        kycVerified: true,
        status: 'ACTIVE'
      });
      
      // TODO: Send notification to user
      // TODO: Log admin action for audit purposes
      return { success: true, message: 'KYC approved successfully' };
    } catch (error) {
      console.error('Error approving KYC:', error);
      throw new Error('Failed to approve KYC');
    }
  }

  async rejectKYC(userId: string, reason: string) {
    try {
      await this.userRepository.update(userId, { 
        kycVerified: false,
        status: 'PENDING_VERIFICATION'
      });
      
      // TODO: Send notification to user with rejection reason
      // TODO: Log admin action for audit purposes
      return { success: true, message: 'KYC rejected successfully' };
    } catch (error) {
      console.error('Error rejecting KYC:', error);
      throw new Error('Failed to reject KYC');
    }
  }

  async getUserDetails(userId: string) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get user's transaction summary
      const transactionStats = await this.transactionRepository.getStats();
      const userTransactions = await this.transactionRepository.findByUserId(userId, {
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });

      return {
        user,
        transactionSummary: {
          totalTransactions: userTransactions.total,
          recentTransactions: userTransactions.transactions
        }
      };
    } catch (error) {
      console.error('Error fetching user details:', error);
      throw new Error('Failed to fetch user details');
    }
  }
}
