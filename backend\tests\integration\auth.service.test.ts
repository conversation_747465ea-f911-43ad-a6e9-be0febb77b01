import { AuthService } from '../../src/services/auth.service';
import { UserRepository } from '../../src/repositories/user.repository';
import { WalletRepository } from '../../src/repositories/wallet.repository';
import * as bcrypt from 'bcryptjs';

describe('AuthService Integration Tests', () => {
  let authService: AuthService;
  let userRepository: UserRepository;
  let walletRepository: WalletRepository;

  beforeEach(() => {
    authService = new AuthService();
    userRepository = new UserRepository();
    walletRepository = new WalletRepository();
  });

  describe('register', () => {
    it('should register a new user and create wallet', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'password123',
        dateOfBirth: '1990-01-01'
      };

      const result = await authService.register(userData);

      expect(result).toBeDefined();
      expect(result.userId).toBeDefined();
      expect(result.verificationStatus).toBe('PENDING');
      expect(result.user.email).toBe(userData.email);
      expect(result.user.firstName).toBe(userData.firstName);
      expect(result.user.lastName).toBe(userData.lastName);

      // Verify user was created in database
      const createdUser = await userRepository.findByEmail(userData.email);
      expect(createdUser).toBeDefined();
      expect(createdUser?.id).toEqual(result.userId);

      // Verify wallet was created
      const wallet = await walletRepository.findByUserId(result.userId);
      expect(wallet).toBeDefined();
      expect(wallet?.userId).toEqual(result.userId);
    });

    it('should hash the password', async () => {
      const userData = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+1234567891',
        password: 'password123',
        dateOfBirth: '1990-01-01'
      };      const result = await authService.register(userData);
      const createdUser = await userRepository.findByEmailWithPassword(userData.email);

      expect(createdUser?.password).not.toBe(userData.password);
      expect(createdUser?.password.length).toBeGreaterThan(20); // bcrypt hash length
    });

    it('should throw error for duplicate email', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'password123',
        dateOfBirth: '1990-01-01'
      };

      await authService.register(userData);

      const duplicateData = {
        ...userData,
        phone: '+1234567891'
      };

      await expect(authService.register(duplicateData)).rejects.toThrow('User with this email already exists');
    });

    it('should throw error for duplicate phone', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'password123',
        dateOfBirth: '1990-01-01'
      };

      await authService.register(userData);

      const duplicateData = {
        ...userData,
        email: '<EMAIL>'
      };

      await expect(authService.register(duplicateData)).rejects.toThrow('User with this phone number already exists');
    });
  });

  describe('login', () => {
    beforeEach(async () => {
      // Create a test user
      const userData = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'password123',
        dateOfBirth: '1990-01-01'
      };
      await authService.register(userData);
    });

    it('should login with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const result = await authService.login(loginData);

      expect(result).toBeDefined();
      expect(result.accessToken).toBeDefined();
      expect(result.refreshToken).toBeDefined();
      expect(result.user).toBeDefined();
      expect(result.user.email).toBe(loginData.email);
      expect(typeof result.accessToken).toBe('string');
      expect(typeof result.refreshToken).toBe('string');
    });

    it('should throw error for invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      await expect(authService.login(loginData)).rejects.toThrow('Invalid credentials');
    });

    it('should throw error for invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      await expect(authService.login(loginData)).rejects.toThrow('Invalid credentials');
    });

    it('should update last login time', async () => {
      const user = await userRepository.findByEmail('<EMAIL>');
      const initialLastLogin = user?.lastLoginAt;

      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      await authService.login(loginData);

      const updatedUser = await userRepository.findByEmail('<EMAIL>');
      expect(updatedUser?.lastLoginAt).toBeDefined();
      
      if (initialLastLogin) {
        expect(updatedUser?.lastLoginAt!.getTime()).toBeGreaterThan(initialLastLogin.getTime());
      }
    });
  });

  describe('password security', () => {
    it('should properly hash and verify passwords', async () => {
      const password = 'testPassword123!';
      const hashedPassword = await bcrypt.hash(password, 10);

      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(20);

      const isValid = await bcrypt.compare(password, hashedPassword);
      expect(isValid).toBe(true);

      const isInvalid = await bcrypt.compare('wrongPassword', hashedPassword);
      expect(isInvalid).toBe(false);
    });
  });
});
