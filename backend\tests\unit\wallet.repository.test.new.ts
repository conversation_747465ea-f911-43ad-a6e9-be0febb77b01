import { Types } from 'mongoose';
import { WalletRepository } from '../../src/repositories/wallet.repository';
import { Wallet } from '../../src/interfaces/models';

// Helper function to generate test wallet data
const createTestWalletData = (userId: string) => ({
  userId,
  accountNumber: `ZW${Date.now()}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
  status: 'ACTIVE' as const
});

describe('WalletRepository', () => {
  let walletRepository: WalletRepository;

  beforeEach(() => {
    walletRepository = new WalletRepository();
  });

  describe('create', () => {
    it('should create a new wallet successfully', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      const wallet = await walletRepository.create(createWalletDto);

      expect(wallet).toBeDefined();
      expect(wallet.userId.toString()).toBe(userId);
      expect(wallet.currency).toBe('NGN');
      expect(wallet.available).toBe(0);
      expect(wallet.pending).toBe(0);
      expect(wallet.limits).toBeDefined();
      expect(wallet.limits.dailyTransfer).toBe(1000000);
      expect(wallet.createdAt).toBeDefined();
      expect(wallet.updatedAt).toBeDefined();
    });

    it('should create wallet with default values', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      const wallet = await walletRepository.create(createWalletDto);

      expect(wallet.available).toBe(0);
      expect(wallet.pending).toBe(0);
      expect(wallet.currency).toBe('NGN');
    });
  });

  describe('findByUserId', () => {
    it('should find wallet by user id', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      await walletRepository.create(createWalletDto);
      const wallet = await walletRepository.findByUserId(userId);

      expect(wallet).toBeDefined();
      expect(wallet?.userId.toString()).toBe(userId);
      expect(wallet?.available).toBe(0);
    });

    it('should return null for non-existent user id', async () => {
      const nonExistentUserId = new Types.ObjectId().toString();
      const wallet = await walletRepository.findByUserId(nonExistentUserId);

      expect(wallet).toBeNull();
    });
  });

  describe('findById', () => {
    it('should find wallet by id', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      const createdWallet = await walletRepository.create(createWalletDto);
      const wallet = await walletRepository.findById(createdWallet.id);

      expect(wallet).toBeDefined();
      expect(wallet?.id).toStrictEqual(createdWallet.id);
      expect(wallet?.available).toBe(0);
    });

    it('should return null for non-existent wallet id', async () => {
      const nonExistentWalletId = new Types.ObjectId().toString();
      const wallet = await walletRepository.findById(nonExistentWalletId);

      expect(wallet).toBeNull();
    });
  });

  describe('updateBalance', () => {
    it('should update available balance', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      await walletRepository.create(createWalletDto);
      const updatedWallet = await walletRepository.updateBalance(userId, 1500, false);

      expect(updatedWallet).toBeDefined();
      expect(updatedWallet.available).toBe(1500);
      expect(updatedWallet.pending).toBe(0);
    });

    it('should update pending balance', async () => {
      const userId = new Types.ObjectId().toString();
      const createWalletDto = createTestWalletData(userId);

      await walletRepository.create(createWalletDto);
      const updatedWallet = await walletRepository.updateBalance(userId, 500, true);

      expect(updatedWallet).toBeDefined();
      expect(updatedWallet.available).toBe(0);
      expect(updatedWallet.pending).toBe(500);
    });
  });

  describe('transferFunds', () => {
    it('should transfer funds between wallets', async () => {
      const userId1 = new Types.ObjectId().toString();
      const userId2 = new Types.ObjectId().toString();

      await walletRepository.create(createTestWalletData(userId1));
      await walletRepository.create(createTestWalletData(userId2));

      // Add some funds to first wallet
      await walletRepository.updateBalance(userId1, 1000, false);

      const { fromWallet, toWallet } = await walletRepository.transferFunds(userId1, userId2, 200);

      expect(fromWallet.available).toBe(800);
      expect(toWallet.available).toBe(200);
    });
  });

  describe('delete', () => {
    it('should delete wallet by user id', async () => {
      const userId = new Types.ObjectId().toString();
      await walletRepository.create(createTestWalletData(userId));

      const deleted = await walletRepository.delete(userId);

      expect(deleted).toBe(true);
      const wallet = await walletRepository.findByUserId(userId);
      expect(wallet).toBeNull();
    });

    it('should return false for non-existent user id', async () => {
      const nonExistentUserId = new Types.ObjectId().toString();
      const deleted = await walletRepository.delete(nonExistentUserId);

      expect(deleted).toBe(false);
    });
  });

  describe('getTotalBalance', () => {
    it('should calculate total balance for all wallets', async () => {
      const userId = new Types.ObjectId().toString();
      await walletRepository.create(createTestWalletData(userId));

      const totalBalance = await walletRepository.getTotalBalance();

      expect(totalBalance).toBeDefined();
      expect(typeof totalBalance.totalAvailable).toBe('number');
      expect(typeof totalBalance.totalPending).toBe('number');
      expect(typeof totalBalance.totalWallets).toBe('number');
    });
  });

  describe('movePendingToAvailable', () => {
    it('should move pending balance to available', async () => {
      const userId = new Types.ObjectId().toString();
      await walletRepository.create(createTestWalletData(userId));
      
      // Add pending balance
      await walletRepository.updateBalance(userId, 500, true);
      
      const wallet = await walletRepository.movePendingToAvailable(userId, 300);
      
      expect(wallet.available).toBe(300);
      expect(wallet.pending).toBe(200);
    });
  });

  describe('updateDailyTransferUsed', () => {
    it('should update daily transfer used amount', async () => {
      const userId = new Types.ObjectId().toString();
      await walletRepository.create(createTestWalletData(userId));
      
      const wallet = await walletRepository.updateDailyTransferUsed(userId, 10000);
      
      expect(wallet.limits.dailyTransferUsed).toBe(10000);
    });
  });

  describe('resetDailyLimits', () => {
    it('should reset daily limits for all wallets', async () => {
      const userId1 = new Types.ObjectId().toString();
      const userId2 = new Types.ObjectId().toString();

      await walletRepository.create(createTestWalletData(userId1));
      await walletRepository.create(createTestWalletData(userId2));

      // Set some daily transfer used amounts
      await walletRepository.updateDailyTransferUsed(userId1, 5000);
      await walletRepository.updateDailyTransferUsed(userId2, 3000);

      const modifiedCount = await walletRepository.resetDailyLimits();

      expect(modifiedCount).toBeGreaterThanOrEqual(2);
    });
  });

  describe('updateLimits', () => {
    it('should update wallet limits', async () => {
      const userId = new Types.ObjectId().toString();
      await walletRepository.create(createTestWalletData(userId));
      
      const newLimits = {
        dailyTransfer: 200000,
        singleTransferMax: 100000
      };
      
      const wallet = await walletRepository.updateLimits(userId, newLimits);
      
      expect(wallet.limits.dailyTransfer).toBe(200000);
      expect(wallet.limits.singleTransferMax).toBe(100000);
    });
  });

  describe('getTopWalletsByBalance', () => {
    it('should get top wallets by balance', async () => {
      const userId1 = new Types.ObjectId().toString();
      const userId2 = new Types.ObjectId().toString();

      await walletRepository.create(createTestWalletData(userId1));
      await walletRepository.create(createTestWalletData(userId2));

      // Add different amounts to wallets
      await walletRepository.updateBalance(userId1, 1500, false);
      await walletRepository.updateBalance(userId2, 800, false);

      const topWallets = await walletRepository.getTopWalletsByBalance(5);

      expect(Array.isArray(topWallets)).toBe(true);
      expect(topWallets.length).toBeGreaterThanOrEqual(2);
      // Should be sorted by total balance descending
      if (topWallets.length >= 2) {
        expect(topWallets[0].total).toBeGreaterThanOrEqual(topWallets[1].total);
      }
    });
  });
});
