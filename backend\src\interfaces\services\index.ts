/**
 * Notification related interfaces
 */
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'TRANSACTION' | 'SECURITY' | 'PROMOTION' | 'SYSTEM';
  read: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  data?: any; // Additional metadata
  createdAt: string;
  readAt?: string;
}

/**
 * Transfer related interfaces
 */
export interface Transfer {
  id: string;
  senderId: string;
  recipientType: 'ZAPWALLET' | 'BANK';
  recipientId: string;
  amount: number;
  fee: number;
  description: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  reference: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Bill payment related interfaces
 */
export interface BillCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
}

export interface BillProvider {
  id: string;
  categoryId: string;
  name: string;
  code: string;
  fee: number;
  validationFields: string[];
}

export interface BillPayment {
  id: string;
  userId: string;
  providerId: string;
  customerNumber: string;
  amount: number;
  fee: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  reference: string;
  createdAt: string;
}

/**
 * Admin dashboard related interfaces
 */
export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalTransactions: number;
  totalVolume: number;
  averageTransactionValue: number;
  revenueThisMonth: number;
  growthRate: number;
  topTransactionCategories: Array<{
    category: string;
    count: number;
    volume: number;
  }>;
  userRegistrationTrend: Array<{
    date: string;
    count: number;
  }>;
}
