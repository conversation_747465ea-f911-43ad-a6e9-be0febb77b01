import { errorResponse } from '../../src/common/errors/errorResponse';

describe('errorResponse', () => {
  describe('basic error response', () => {
    it('should create error response with all parameters', () => {
      const response = errorResponse(400, 'VALIDATION_ERROR', 'Invalid input data');

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('Invalid input data');
      expect(body.error.timestamp).toBeDefined();
      expect(body.error.requestId).toBe('');
    });

    it('should create error response with details', () => {
      const response = errorResponse(500, 'INTERNAL_ERROR', 'Server error', 'Database connection failed');

      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('Server error');
      expect(body.error.details).toBe('Database connection failed');
      expect(body.error.timestamp).toBeDefined();
      expect(body.error.requestId).toBe('');
    });

    it('should handle different status codes', () => {
      const response401 = errorResponse(401, 'UNAUTHORIZED', 'Access denied');
      const response404 = errorResponse(404, 'NOT_FOUND', 'Resource not found');
      const response500 = errorResponse(500, 'SERVER_ERROR', 'Internal server error');

      expect(response401.statusCode).toBe(401);
      expect(response404.statusCode).toBe(404);
      expect(response500.statusCode).toBe(500);
    });    it('should include CORS headers', () => {
      const response = errorResponse(400, 'BAD_REQUEST', 'Bad request');

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error.code).toBe('BAD_REQUEST');
      expect(body.error.message).toBe('Bad request');
    });

    it('should create valid JSON body', () => {
      const response = errorResponse(422, 'VALIDATION_FAILED', 'Validation failed');

      expect(() => JSON.parse(response.body)).not.toThrow();
      
      const body = JSON.parse(response.body);
      expect(body).toHaveProperty('success', false);
      expect(body).toHaveProperty('error');
      expect(body.error).toHaveProperty('code', 'VALIDATION_FAILED');
      expect(body.error).toHaveProperty('message', 'Validation failed');
    });

    it('should handle empty message', () => {
      const response = errorResponse(400, 'EMPTY_MESSAGE', '');

      const body = JSON.parse(response.body);
      expect(body.error.message).toBe('');
    });

    it('should handle special characters in message', () => {
      const specialMessage = 'Error with "quotes" and \\backslashes\\ and émojis 🚀';
      const response = errorResponse(400, 'SPECIAL_CHARS', specialMessage);

      const body = JSON.parse(response.body);
      expect(body.error.message).toBe(specialMessage);
    });
  });
});
