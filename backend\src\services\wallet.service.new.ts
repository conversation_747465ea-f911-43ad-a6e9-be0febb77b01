import { v4 as uuidv4 } from 'uuid';
import { Wallet } from '../interfaces/models';
import { WalletApiService, WalletOpenRequest, WalletTransferRequest } from './wallet-api.service';
import { WalletRepository } from '../repositories/wallet.repository';
import { ApiConfig } from '../interfaces/common';
import { logger } from '../common/logging/logger';

export class WalletService {
  // In-memory store for demonstration. Replace with PostgreSQL logic.
  private static wallets: Wallet[] = [];
  private walletRepository = new WalletRepository();
  private walletApiService: WalletApiService;

  constructor() {
    // Initialize wallet API service with configuration
    const walletConfig: ApiConfig = {
      baseUrl: process.env.WALLET_API_BASE_URL || 'http://102.216.128.75:9090/waas',
      apiKey: process.env.WALLET_API_KEY || '',
      secretKey: process.env.WALLET_SECRET_KEY || '',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    this.walletApiService = new WalletApiService(walletConfig);
  }

  async getBalance(userId: string) {
    try {
      // Get wallet from database
      const wallet = await this.walletRepository.findByUserId(userId);
      
      if (!wallet) {
        throw new Error('Wallet not found for user');
      }

      // Get real-time balance from wallet API
      const balanceResponse = await this.walletApiService.walletEnquiry({
        accountNo: wallet.accountNumber
      });

      if (balanceResponse.success && balanceResponse.data) {
        return {
          available: balanceResponse.data.availableBalance || wallet.available,
          pending: wallet.pending,
          currency: wallet.currency,
          limits: wallet.limits,
          accountNumber: wallet.accountNumber
        };
      }

      // Fallback to database values
      return {
        available: wallet.available,
        pending: wallet.pending,
        currency: wallet.currency,
        limits: wallet.limits,
        accountNumber: wallet.accountNumber
      };
    } catch (error: any) {
      logger.error('Get balance failed', { userId, error: error.message });
      throw new Error('Failed to get wallet balance');
    }
  }

  async createWallet(userId: string, walletData: WalletOpenRequest) {
    try {
      // Authenticate with wallet service first
      await this.authenticateWalletService();

      // Create wallet via API
      const walletResponse = await this.walletApiService.openWallet(walletData);

      if (!walletResponse.success) {
        throw new Error(walletResponse.error || 'Wallet creation failed');
      }

      // Save wallet details to database
      const wallet = await this.walletRepository.create({
        userId,
        accountNumber: walletResponse.data?.data.accountNumber,
        customerID: walletResponse.data?.data.customerID,
        available: parseFloat(walletResponse.data?.data.availableBalance || '0'),
        pending: 0,
        currency: 'NGN',
        status: 'ACTIVE',
        limits: {
          daily: 100000,
          monthly: 500000,
          single: 50000
        }
      });

      return {
        walletId: wallet.id,
        accountNumber: wallet.accountNumber,
        customerID: wallet.customerID,
        status: walletResponse.data?.status,
        message: walletResponse.data?.message
      };
    } catch (error: any) {
      logger.error('Wallet creation failed', { userId, error: error.message });
      throw new Error('Failed to create wallet');
    }
  }

  async fundWallet(userId: string, fundingData: any) {
    try {
      const wallet = await this.walletRepository.findByUserId(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      await this.authenticateWalletService();

      const transactionId = `FUND_${Date.now()}_${uuidv4().substring(0, 8)}`;

      // Credit wallet via API
      const creditData: WalletTransferRequest = {
        accountNo: wallet.accountNumber,
        narration: `Wallet funding - ${fundingData.paymentMethod}`,
        totalAmount: fundingData.amount,
        transactionId,
        merchant: {
          isFee: false
        }
      };

      const creditResponse = await this.walletApiService.creditWallet(creditData);

      if (!creditResponse.success) {
        throw new Error(creditResponse.error || 'Wallet funding failed');
      }

      return {
        transactionId,
        status: 'SUCCESS',
        amount: fundingData.amount,
        paymentMethod: fundingData.paymentMethod,
        message: 'Wallet funded successfully',
        reference: creditResponse.reference
      };
    } catch (error: any) {
      logger.error('Wallet funding failed', { userId, error: error.message });
      throw new Error('Failed to fund wallet');
    }
  }

  async withdrawFromWallet(userId: string, withdrawalData: any) {
    try {
      const wallet = await this.walletRepository.findByUserId(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      await this.authenticateWalletService();

      const transactionId = `WITHDRAW_${Date.now()}_${uuidv4().substring(0, 8)}`;

      // First verify the target bank account
      const enquiryResponse = await this.walletApiService.otherBankEnquiry(
        withdrawalData.bankCode,
        withdrawalData.accountNumber
      );

      if (!enquiryResponse.success) {
        throw new Error('Invalid bank account details');
      }

      // Process withdrawal via other bank transfer
      const transferData = {
        customer: {
          account: {
            bank: withdrawalData.bankCode,
            name: withdrawalData.accountName,
            number: withdrawalData.accountNumber,
            senderaccountnumber: wallet.accountNumber,
            sendername: wallet.accountName || 'Wallet User'
          }
        },
        narration: withdrawalData.narration || 'Wallet withdrawal',
        order: {
          amount: withdrawalData.amount.toString(),
          country: 'NG',
          currency: 'NGN',
          description: 'Wallet withdrawal'
        },
        transaction: {
          reference: transactionId
        }
      };

      const transferResponse = await this.walletApiService.transferToOtherBank(transferData);

      if (!transferResponse.success) {
        throw new Error(transferResponse.error || 'Withdrawal failed');
      }

      return {
        transactionId,
        status: 'PENDING',
        amount: withdrawalData.amount,
        bankCode: withdrawalData.bankCode,
        accountNumber: withdrawalData.accountNumber,
        message: 'Withdrawal initiated successfully. Processing may take 1-3 business days.',
        reference: transferResponse.reference
      };
    } catch (error: any) {
      logger.error('Wallet withdrawal failed', { userId, error: error.message });
      throw new Error('Failed to process withdrawal');
    }
  }

  async transferBetweenWallets(fromUserId: string, toUserId: string, transferData: any) {
    try {
      const fromWallet = await this.walletRepository.findByUserId(fromUserId);
      const toWallet = await this.walletRepository.findByUserId(toUserId);

      if (!fromWallet || !toWallet) {
        throw new Error('One or both wallets not found');
      }

      await this.authenticateWalletService();

      const transactionId = `TRANSFER_${Date.now()}_${uuidv4().substring(0, 8)}`;

      // Debit sender's wallet
      const debitData: WalletTransferRequest = {
        accountNo: fromWallet.accountNumber,
        narration: `Transfer to ${toWallet.accountNumber}`,
        totalAmount: transferData.amount,
        transactionId: `${transactionId}_DEBIT`,
        merchant: {
          isFee: false
        }
      };

      const debitResponse = await this.walletApiService.debitWallet(debitData);

      if (!debitResponse.success) {
        throw new Error(debitResponse.error || 'Debit failed');
      }

      // Credit receiver's wallet
      const creditData: WalletTransferRequest = {
        accountNo: toWallet.accountNumber,
        narration: `Transfer from ${fromWallet.accountNumber}`,
        totalAmount: transferData.amount,
        transactionId: `${transactionId}_CREDIT`,
        merchant: {
          isFee: false
        }
      };

      const creditResponse = await this.walletApiService.creditWallet(creditData);

      if (!creditResponse.success) {
        // TODO: Implement reversal logic for the debit
        throw new Error(creditResponse.error || 'Credit failed');
      }

      return {
        transactionId,
        status: 'SUCCESS',
        amount: transferData.amount,
        fromAccount: fromWallet.accountNumber,
        toAccount: toWallet.accountNumber,
        message: 'Transfer completed successfully'
      };
    } catch (error: any) {
      logger.error('Wallet transfer failed', { fromUserId, toUserId, error: error.message });
      throw new Error('Failed to transfer between wallets');
    }
  }

  async getTransactionHistory(userId: string, limit = 50, offset = 0) {
    try {
      const wallet = await this.walletRepository.findByUserId(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      await this.authenticateWalletService();

      const historyResponse = await this.walletApiService.getWalletTransactions(
        wallet.accountNumber,
        limit,
        Math.floor(offset / limit) + 1
      );

      if (!historyResponse.success) {
        throw new Error(historyResponse.error || 'Failed to get transaction history');
      }

      return {
        transactions: historyResponse.data || [],
        total: historyResponse.data?.length || 0,
        limit,
        offset
      };
    } catch (error: any) {
      logger.error('Get transaction history failed', { userId, error: error.message });
      throw new Error('Failed to get transaction history');
    }
  }

  async getWalletStatus(userId: string) {
    try {
      const wallet = await this.walletRepository.findByUserId(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      await this.authenticateWalletService();

      const statusResponse = await this.walletApiService.walletStatus(wallet.accountNumber);

      if (!statusResponse.success) {
        throw new Error(statusResponse.error || 'Failed to get wallet status');
      }

      return {
        accountNumber: wallet.accountNumber,
        status: statusResponse.data?.status || wallet.status,
        ...statusResponse.data
      };
    } catch (error: any) {
      logger.error('Get wallet status failed', { userId, error: error.message });
      throw new Error('Failed to get wallet status');
    }
  }

  private async authenticateWalletService() {
    try {
      const authResponse = await this.walletApiService.authenticate({
        username: process.env.WALLET_USERNAME || '',
        password: process.env.WALLET_PASSWORD || '',
        clientId: process.env.WALLET_CLIENT_ID || '',
        clientSecret: process.env.WALLET_CLIENT_SECRET || ''
      });

      if (!authResponse.success) {
        throw new Error('Wallet service authentication failed');
      }
    } catch (error: any) {
      logger.error('Wallet service authentication failed', { error: error.message });
      throw new Error('Failed to authenticate with wallet service');
    }
  }

  private createDefaultWallet(userId: string): Wallet {
    return {
      id: uuidv4(),
      userId,
      accountNumber: '',
      available: 0,
      pending: 0,
      currency: 'NGN',
      status: 'ACTIVE',
      limits: {
        daily: 100000,
        monthly: 500000,
        single: 50000
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
}
