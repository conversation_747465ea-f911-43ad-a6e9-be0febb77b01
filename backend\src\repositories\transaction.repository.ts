import { TransactionModel, ITransaction } from '../models/transaction.schema';
import { DatabaseConfig } from '../config/database';
import { Transaction, TransactionQueryOptions } from '../interfaces';

export class TransactionRepository {
  constructor() {
    // Only ensure database connection if not in test mode
    if (process.env.NODE_ENV !== 'test') {
      DatabaseConfig.getInstance().connect();
    }
  }

  async create(transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<Transaction> {
    const newTransaction = new TransactionModel(transaction);
    const savedTransaction = await newTransaction.save();
    return savedTransaction.toJSON() as Transaction;
  }

  async findById(id: string): Promise<Transaction | null> {
    const transaction = await TransactionModel.findById(id);
    return transaction ? (transaction.toJSON() as Transaction) : null;
  }

  async findByReference(reference: string): Promise<Transaction | null> {
    const transaction = await TransactionModel.findOne({ reference });
    return transaction ? (transaction.toJSON() as Transaction) : null;
  }

  async findByUserId(userId: string, options: Omit<TransactionQueryOptions, 'userId'> = {}): Promise<{
    transactions: Transaction[];
    total: number;
    page: number;
    pages: number;
  }> {
    return this.findMany({ ...options, userId });
  }

  async findMany(options: TransactionQueryOptions = {}): Promise<{
    transactions: Transaction[];
    total: number;
    page: number;
    pages: number;
  }> {
    const {
      page = 1,
      limit = 50,
      userId,
      status,
      type,
      category,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options;

    const query: any = {};

    // Add filters
    if (userId) query.userId = userId;
    if (status) query.status = status;
    if (type) query.type = type;
    if (category) query.category = category;

    // Add date range filter
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = startDate;
      if (endDate) query.createdAt.$lte = endDate;
    }

    const skip = (page - 1) * limit;
    const sortObj: any = {};
    sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;    const [transactions, total] = await Promise.all([
      process.env.NODE_ENV === 'test' 
        ? TransactionModel.find(query)
            .sort(sortObj)
            .skip(skip)
            .limit(limit)
            .lean()
        : TransactionModel.find(query)
            .populate('userId', 'firstName lastName email')
            .sort(sortObj)
            .skip(skip)
            .limit(limit)
            .lean(),
      TransactionModel.countDocuments(query)
    ]);

    return {
      transactions: transactions.map(transaction => ({
        ...transaction,
        id: transaction._id.toString(),
        _id: undefined
      })) as Transaction[],
      total,
      page,
      pages: Math.ceil(total / limit)
    };
  }

  async update(id: string, updateData: Partial<Transaction>): Promise<Transaction> {
    const transaction = await TransactionModel.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!transaction) {
      throw new Error('Transaction not found');
    }

    return transaction.toJSON() as Transaction;
  }
  async getStats(startDate?: Date, endDate?: Date): Promise<{
    totalTransactions: number;
    totalVolume: number;
    totalFees: number;
    completedTransactions: number;
    pendingTransactions: number;
    failedTransactions: number;
    averageTransactionValue: number;
  }> {
    const query: any = {};
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = startDate;
      if (endDate) query.createdAt.$lte = endDate;
    }

    const pipeline: any[] = [
      { $match: query },
      {
        $group: {
          _id: null,
          totalTransactions: { $sum: 1 },
          totalVolume: { $sum: '$amount' },
          totalFees: { $sum: '$fee' },
          completedTransactions: {
            $sum: { $cond: [{ $eq: ['$status', 'COMPLETED'] }, 1, 0] }
          },
          pendingTransactions: {
            $sum: { $cond: [{ $eq: ['$status', 'PENDING'] }, 1, 0] }
          },
          failedTransactions: {
            $sum: { $cond: [{ $eq: ['$status', 'FAILED'] }, 1, 0] }
          },
          averageTransactionValue: { $avg: '$amount' }
        }
      }
    ];

    const result = await TransactionModel.aggregate(pipeline);
    
    if (result.length === 0) {
      return {
        totalTransactions: 0,
        totalVolume: 0,
        totalFees: 0,
        completedTransactions: 0,
        pendingTransactions: 0,
        failedTransactions: 0,
        averageTransactionValue: 0
      };
    }

    return result[0];
  }
  async getTopCategories(limit: number = 10, startDate?: Date, endDate?: Date): Promise<Array<{
    category: string;
    count: number;
    volume: number;
  }>> {
    const query: any = { status: 'COMPLETED' };
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = startDate;
      if (endDate) query.createdAt.$lte = endDate;
    }

    const pipeline: any[] = [
      { $match: query },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          volume: { $sum: '$amount' }
        }
      },
      { $sort: { volume: -1 } },
      { $limit: limit },
      {
        $project: {
          category: '$_id',
          count: 1,
          volume: 1,
          _id: 0
        }
      }
    ];

    return await TransactionModel.aggregate(pipeline);
  }
  async getVolumeByDate(days: number = 30): Promise<Array<{
    date: string;
    volume: number;
    count: number;
  }>> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const pipeline: any[] = [
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'COMPLETED'
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          volume: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } },
      {
        $project: {
          date: '$_id',
          volume: 1,
          count: 1,
          _id: 0
        }
      }
    ];

    return await TransactionModel.aggregate(pipeline);
  }

  async delete(id: string): Promise<boolean> {
    const result = await TransactionModel.findByIdAndDelete(id);
    return !!result;
  }
}
