import { APIGatewayProxyEvent } from 'aws-lambda';
import { main as balanceHandler } from '../../src/api/v1/wallets/balance.handler';
import { WalletService } from '../../src/services/wallet.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';

// Mock dependencies
jest.mock('../../src/services/wallet.service');
jest.mock('../../src/common/middlewares/auth.middleware');

describe('Balance Handler', () => {
  let mockWalletService: jest.Mocked<WalletService>;
  let mockAuthMiddleware: jest.Mocked<AuthMiddleware>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockWalletService = {
      getBalance: jest.fn(),
      fundWallet: jest.fn(),
      withdrawFromWallet: jest.fn(),
      createDefaultWallet: jest.fn()
    } as any;
    
    mockAuthMiddleware = {
      authenticate: jest.fn()
    } as any;
    
    (WalletService as jest.MockedClass<typeof WalletService>).mockImplementation(() => mockWalletService);
    (AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>).mockImplementation(() => mockAuthMiddleware);
  });

  const createMockEvent = (headers: any = {}): Partial<APIGatewayProxyEvent> => ({
    body: null,
    headers,
    multiValueHeaders: {},
    httpMethod: 'GET',
    isBase64Encoded: false,
    path: '/api/v1/wallets/balance',
    pathParameters: null,
    queryStringParameters: null,
    multiValueQueryStringParameters: null,
    stageVariables: null,
    requestContext: {
      requestId: 'test-request-id',
      stage: 'test',
      resourceId: 'test-resource',
      httpMethod: 'GET',
      resourcePath: '/api/v1/wallets/balance',
      path: '/api/v1/wallets/balance',
      accountId: 'test-account',
      apiId: 'test-api',
      protocol: 'HTTP/1.1',
      requestTime: '01/Jan/2025:00:00:00 +0000',
      requestTimeEpoch: **********,
      identity: {
        accessKey: null,
        accountId: null,
        apiKey: null,
        apiKeyId: null,
        caller: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '127.0.0.1',
        user: null,
        userAgent: 'test-user-agent',
        userArn: null,
        clientCert: null
      },
      authorizer: null
    }
  });

  describe('successful balance retrieval', () => {
    it('should return wallet balance for authenticated user', async () => {
      const mockUser = {
        userId: 'user123',
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };

      const mockBalance = {
        availableBalance: 5000,
        pendingBalance: 1000,
        totalBalance: 6000,
        currency: 'NGN'
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockWalletService.getBalance.mockResolvedValue(mockBalance);

      const mockEvent = createMockEvent({
        'Authorization': 'Bearer valid-token-123'
      });

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data).toEqual(mockBalance);
      expect(mockAuthMiddleware.authenticate).toHaveBeenCalledWith(mockEvent);
      expect(mockWalletService.getBalance).toHaveBeenCalledWith('user123');
    });

    it('should handle zero balance', async () => {
      const mockUser = {
        userId: 'user123',
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };

      const mockBalance = {
        availableBalance: 0,
        pendingBalance: 0,
        totalBalance: 0,
        currency: 'NGN'
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockWalletService.getBalance.mockResolvedValue(mockBalance);

      const mockEvent = createMockEvent({
        'Authorization': 'Bearer valid-token-123'
      });

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data.totalBalance).toBe(0);
    });
  });

  describe('authentication failures', () => {
    it('should return 401 when authentication fails', async () => {
      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const mockEvent = createMockEvent({});

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('UNAUTHORIZED');
      expect(body.error.message).toBe('Missing or invalid authorization header');
      expect(mockWalletService.getBalance).not.toHaveBeenCalled();
    });

    it('should return 401 for invalid token', async () => {
      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Invalid or expired token');
      });

      const mockEvent = createMockEvent({
        'Authorization': 'Bearer invalid-token'
      });

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.message).toBe('Invalid or expired token');
    });

    it('should return 401 for expired token', async () => {
      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Token expired');
      });

      const mockEvent = createMockEvent({
        'Authorization': 'Bearer expired-token'
      });

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('wallet service errors', () => {
    it('should handle wallet service errors', async () => {
      const mockUser = {
        userId: 'user123',
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockWalletService.getBalance.mockRejectedValue(new Error('Database connection failed'));

      const mockEvent = createMockEvent({
        'Authorization': 'Bearer valid-token-123'
      });

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.message).toBe('Database connection failed');
    });

    it('should handle user not found error', async () => {
      const mockUser = {
        userId: 'nonexistent-user',
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockWalletService.getBalance.mockRejectedValue(new Error('User not found'));

      const mockEvent = createMockEvent({
        'Authorization': 'Bearer valid-token-123'
      });

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.message).toBe('User not found');
    });
  });

  describe('edge cases', () => {
    it('should handle missing Authorization header', async () => {
      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const mockEvent = createMockEvent({});

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(401);
    });

    it('should handle malformed Authorization header', async () => {
      mockAuthMiddleware.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const mockEvent = createMockEvent({
        'Authorization': 'NotBearer token'
      });

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(401);
    });

    it('should handle large balance values', async () => {
      const mockUser = {
        userId: 'user123',
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600000
      };

      const mockBalance = {
        availableBalance: 999999999999,
        pendingBalance: 0,
        totalBalance: 999999999999,
        currency: 'NGN'
      };

      mockAuthMiddleware.authenticate.mockReturnValue(mockUser);
      mockWalletService.getBalance.mockResolvedValue(mockBalance);

      const mockEvent = createMockEvent({
        'Authorization': 'Bearer valid-token-123'
      });

      const result = await balanceHandler(mockEvent as APIGatewayProxyEvent);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.data.availableBalance).toBe(999999999999);
    });
  });
});
