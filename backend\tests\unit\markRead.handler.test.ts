import { APIGatewayProxyEvent } from 'aws-lambda';
import { main } from '../../src/api/v1/notifications/markRead.handler';
import { NotificationService } from '../../src/services/notification.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';

// Mock dependencies
jest.mock('../../src/services/notification.service');
jest.mock('../../src/common/middlewares/auth.middleware');

describe('Mark Read Notification Handler', () => {
  let mockEvent: APIGatewayProxyEvent;
  let mockNotificationServiceInstance: any;
  let mockAuthMiddlewareInstance: any;

  beforeEach(() => {
    mockEvent = {
      pathParameters: { id: 'notification123' },
      headers: { Authorization: 'Bearer valid-token' }
    } as any;

    mockNotificationServiceInstance = {
      markAsRead: jest.fn()
    };

    mockAuthMiddlewareInstance = {
      authenticate: jest.fn()
    };

    (NotificationService as jest.MockedClass<typeof NotificationService>).mockImplementation(() => mockNotificationServiceInstance);
    (AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>).mockImplementation(() => mockAuthMiddlewareInstance);

    jest.clearAllMocks();
  });

  describe('successful mark read', () => {
    it('should mark notification as read successfully', async () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.markAsRead.mockResolvedValue(undefined);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        message: 'Notification marked as read'
      });
      expect(mockNotificationServiceInstance.markAsRead).toHaveBeenCalledWith('user123', 'notification123');
    });
  });

  describe('validation errors', () => {
    it('should return 400 for missing notification ID', async () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockEvent.pathParameters = null;
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('MISSING_PARAMETER');
      expect(body.error.message).toBe('Notification ID is required');
    });

    it('should return 400 for empty notification ID', async () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockEvent.pathParameters = { id: '' };
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('MISSING_PARAMETER');
      expect(body.error.message).toBe('Notification ID is required');
    });
  });

  describe('authentication failures', () => {
    it('should return 500 when authentication fails', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred while marking notification as read');
    });
  });

  describe('service errors', () => {
    it('should handle notification service errors', async () => {
      const mockUser = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockNotificationServiceInstance.markAsRead.mockRejectedValue(new Error('Notification not found'));

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred while marking notification as read');
    });
  });
});
