import { UserRepository } from '../../src/repositories/user.repository';
import { UserModel } from '../../src/models/user.schema';
import mongoose from 'mongoose';

describe('UserRepository', () => {
  let userRepository: UserRepository;

  beforeEach(() => {
    userRepository = new UserRepository();
  });

  describe('create', () => {
    it('should create a new user successfully', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01'
      };

      const createdUser = await userRepository.create(userData);

      expect(createdUser).toBeDefined();
      expect(createdUser.email).toBe(userData.email);
      expect(createdUser.firstName).toBe(userData.firstName);
      expect(createdUser.lastName).toBe(userData.lastName);
      expect(createdUser.phone).toBe(userData.phone);
      expect(createdUser.id).toBeDefined();
    });    it('should throw error when creating user with duplicate email', async () => {
      // Ensure indexes are created first
      await UserModel.syncIndexes();
      
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01'
      };

      await userRepository.create(userData);

      const duplicateUserData = {
        ...userData,
        phone: '+1234567891'
      };

      await expect(userRepository.create(duplicateUserData)).rejects.toThrow();
    });
  });

  describe('findById', () => {
    it('should find user by id', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01'
      };

      const createdUser = await userRepository.create(userData);
      const foundUser = await userRepository.findById(createdUser.id);      expect(foundUser).toBeDefined();
      expect(foundUser?.id).toEqual(createdUser.id);
      expect(foundUser?.email).toBe(userData.email);
    });

    it('should return null for non-existent user id', async () => {
      const nonExistentId = new mongoose.Types.ObjectId().toString();
      const foundUser = await userRepository.findById(nonExistentId);

      expect(foundUser).toBeNull();
    });
  });

  describe('findByEmail', () => {
    it('should find user by email', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01'
      };

      const createdUser = await userRepository.create(userData);
      const foundUser = await userRepository.findByEmail(userData.email);      expect(foundUser).toBeDefined();
      expect(foundUser?.email).toBe(userData.email);
      expect(foundUser?.id).toEqual(createdUser.id);
    });

    it('should return null for non-existent email', async () => {
      const foundUser = await userRepository.findByEmail('<EMAIL>');
      expect(foundUser).toBeNull();
    });
  });

  describe('getStats', () => {
    it('should return user statistics', async () => {
      const userData = {
        firstName: 'User1',
        lastName: 'Test',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'hashedpassword',
        dateOfBirth: '1990-01-01'
      };

      await userRepository.create(userData);

      const stats = await userRepository.getStats();

      expect(stats).toBeDefined();      expect(stats.totalUsers).toBeGreaterThanOrEqual(1);
      expect(stats.activeUsers).toBeGreaterThanOrEqual(0);
      expect(stats.verifiedUsers).toBeGreaterThanOrEqual(0);
    });
  });
});
