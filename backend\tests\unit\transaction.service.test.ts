import { TransactionService, Transaction } from '../../src/services/transaction.service';

describe('TransactionService', () => {
  let transactionService: TransactionService;

  beforeEach(() => {
    transactionService = new TransactionService();
    // Clear the static transactions array
    (TransactionService as any).transactions = [];
  });

  describe('createTransaction', () => {
    it('should create a new transaction successfully', async () => {
      const transactionData = {
        userId: 'user123',
        type: 'CREDIT' as const,
        amount: 10000,
        fee: 0,
        description: 'Wallet funding',
        status: 'COMPLETED' as const,
        category: 'FUNDING',
        reference: 'REF123'
      };

      const transaction = await transactionService.createTransaction(transactionData);

      expect(transaction.id).toBeDefined();
      expect(transaction.userId).toBe('user123');
      expect(transaction.type).toBe('CREDIT');
      expect(transaction.amount).toBe(10000);
      expect(transaction.fee).toBe(0);
      expect(transaction.description).toBe('Wallet funding');
      expect(transaction.status).toBe('COMPLETED');
      expect(transaction.category).toBe('FUNDING');
      expect(transaction.reference).toBe('REF123');
      expect(transaction.createdAt).toBeDefined();
      expect(transaction.updatedAt).toBeDefined();
    });

    it('should create debit transaction', async () => {
      const transactionData = {
        userId: 'user123',
        type: 'DEBIT' as const,
        amount: 5000,
        fee: 50,
        description: 'Transfer to friend',
        status: 'PENDING' as const,
        category: 'TRANSFER',
        reference: 'REF456'
      };

      const transaction = await transactionService.createTransaction(transactionData);

      expect(transaction.type).toBe('DEBIT');
      expect(transaction.amount).toBe(5000);
      expect(transaction.fee).toBe(50);
      expect(transaction.status).toBe('PENDING');
    });

    it('should generate unique IDs for different transactions', async () => {
      const transactionData1 = {
        userId: 'user123',
        type: 'CREDIT' as const,
        amount: 10000,
        fee: 0,
        description: 'Transaction 1',
        status: 'COMPLETED' as const,
        category: 'FUNDING',
        reference: 'REF1'
      };

      const transactionData2 = {
        userId: 'user456',
        type: 'DEBIT' as const,
        amount: 5000,
        fee: 25,
        description: 'Transaction 2',
        status: 'PENDING' as const,
        category: 'TRANSFER',
        reference: 'REF2'
      };

      const transaction1 = await transactionService.createTransaction(transactionData1);
      const transaction2 = await transactionService.createTransaction(transactionData2);

      expect(transaction1.id).not.toBe(transaction2.id);
      expect(transaction1.reference).not.toBe(transaction2.reference);
    });
  });
  describe('getTransactions', () => {
    beforeEach(async () => {
      // Create sample transactions with small delays to ensure different timestamps
      await transactionService.createTransaction({
        userId: 'user123',
        type: 'CREDIT',
        amount: 10000,
        fee: 0,
        description: 'Wallet funding',
        status: 'COMPLETED',
        category: 'FUNDING',
        reference: 'REF1'
      });

      // Add a small delay to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 2));

      await transactionService.createTransaction({
        userId: 'user123',
        type: 'DEBIT',
        amount: 5000,
        fee: 50,
        description: 'Transfer',
        status: 'COMPLETED',
        category: 'TRANSFER',
        reference: 'REF2'
      });

      await transactionService.createTransaction({
        userId: 'user456',
        type: 'CREDIT',
        amount: 15000,
        fee: 0,
        description: 'Salary',
        status: 'COMPLETED',
        category: 'FUNDING',
        reference: 'REF3'
      });
    });

    it('should return user transactions with pagination', async () => {
      const result = await transactionService.getTransactions('user123', {
        page: 1,
        limit: 10
      });

      expect(result.transactions).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
      expect(result.pagination.pages).toBe(1);
      expect(result.pagination.currentPage).toBe(1);
      expect(result.pagination.limit).toBe(10);
      
      // Check if transactions belong to correct user
      result.transactions.forEach(transaction => {
        expect(transaction.userId).toBe('user123');
      });
    });

    it('should filter transactions by type', async () => {
      const result = await transactionService.getTransactions('user123', {
        type: 'CREDIT'
      });

      expect(result.transactions).toHaveLength(1);
      expect(result.transactions[0].type).toBe('CREDIT');
    });

    it('should filter transactions by status', async () => {
      // Add a pending transaction
      await transactionService.createTransaction({
        userId: 'user123',
        type: 'DEBIT',
        amount: 2000,
        fee: 20,
        description: 'Pending transfer',
        status: 'PENDING',
        category: 'TRANSFER',
        reference: 'REF4'
      });

      const result = await transactionService.getTransactions('user123', {
        status: 'PENDING'
      });

      expect(result.transactions).toHaveLength(1);
      expect(result.transactions[0].status).toBe('PENDING');
    });

    it('should filter transactions by category', async () => {
      const result = await transactionService.getTransactions('user123', {
        category: 'FUNDING'
      });

      expect(result.transactions).toHaveLength(1);
      expect(result.transactions[0].category).toBe('FUNDING');
    });

    it('should handle empty results', async () => {
      const result = await transactionService.getTransactions('nonexistent', {});

      expect(result.transactions).toHaveLength(0);
      expect(result.pagination.total).toBe(0);
    });    it('should return transactions in consistent order', async () => {
      const result = await transactionService.getTransactions('user123', {});

      expect(result.transactions).toHaveLength(2);
      
      // Verify that all returned transactions belong to the user
      result.transactions.forEach(transaction => {
        expect(transaction.userId).toBe('user123');
      });
      
      // Check that service returns transactions in a consistent order
      const secondResult = await transactionService.getTransactions('user123', {});
      expect(secondResult.transactions).toHaveLength(2);
      expect(secondResult.transactions[0].id).toBe(result.transactions[0].id);
      expect(secondResult.transactions[1].id).toBe(result.transactions[1].id);
      
      // Verify both results have the same order
      for (let i = 0; i < result.transactions.length; i++) {
        expect(result.transactions[i].id).toBe(secondResult.transactions[i].id);
      }
    });
  });
  describe('getTransactionById', () => {
    let sampleTransactionId: string;

    beforeEach(async () => {
      const transaction = await transactionService.createTransaction({
        userId: 'user123',
        type: 'CREDIT',
        amount: 10000,
        fee: 0,
        description: 'Test transaction',
        status: 'COMPLETED',
        category: 'FUNDING',
        reference: 'REF123'
      });
      sampleTransactionId = transaction.id;
    });

    it('should return transaction by ID', async () => {
      const transaction = await transactionService.getTransactionById('user123', sampleTransactionId);

      expect(transaction).toBeDefined();
      expect(transaction.id).toBe(sampleTransactionId);
      expect(transaction.description).toBe('Test transaction');
    });

    it('should throw error for non-existent transaction', async () => {
      await expect(
        transactionService.getTransactionById('user123', 'nonexistent')
      ).rejects.toThrow('Transaction not found');
    });

    it('should throw error when accessing other user transaction', async () => {
      await expect(
        transactionService.getTransactionById('otheruser', sampleTransactionId)
      ).rejects.toThrow('Transaction not found');
    });
  });
  describe('getTransactionStats', () => {
    beforeEach(async () => {
      // Create transactions for statistics
      await transactionService.createTransaction({
        userId: 'user123',
        type: 'CREDIT',
        amount: 10000,
        fee: 0,
        description: 'Funding 1',
        status: 'COMPLETED',
        category: 'FUNDING',
        reference: 'REF1'
      });

      await transactionService.createTransaction({
        userId: 'user123',
        type: 'DEBIT',
        amount: 5000,
        fee: 50,
        description: 'Transfer 1',
        status: 'COMPLETED',
        category: 'TRANSFER',
        reference: 'REF2'
      });

      await transactionService.createTransaction({
        userId: 'user123',
        type: 'DEBIT',
        amount: 2000,
        fee: 20,
        description: 'Bill payment',
        status: 'COMPLETED',
        category: 'BILLS',
        reference: 'REF3'
      });

      await transactionService.createTransaction({
        userId: 'user123',
        type: 'DEBIT',
        amount: 1000,
        fee: 10,
        description: 'Failed transfer',
        status: 'FAILED',
        category: 'TRANSFER',
        reference: 'REF4'
      });
    });

    it('should return transaction statistics for user', async () => {
      const stats = await transactionService.getTransactionStats('user123');

      expect(stats.totalTransactions).toBe(4);
      expect(stats.totalCredit).toBe(10000);
      expect(stats.totalDebit).toBe(7000); // 5000 + 2000 (only completed)
      expect(stats.netBalance).toBe(3000); // 10000 - 7000
      expect(stats.monthlyStats).toBeDefined();
      expect(Array.isArray(stats.monthlyStats)).toBe(true);
    });

    it('should return zero stats for user with no transactions', async () => {
      const stats = await transactionService.getTransactionStats('newuser');

      expect(stats.totalTransactions).toBe(0);
      expect(stats.totalCredit).toBe(0);
      expect(stats.totalDebit).toBe(0);
      expect(stats.netBalance).toBe(0);
      expect(stats.monthlyStats).toBeDefined();
    });

    it('should only count completed transactions in totals', async () => {
      // Add more pending transactions
      await transactionService.createTransaction({
        userId: 'user123',
        type: 'CREDIT',
        amount: 5000,
        fee: 0,
        description: 'Pending credit',
        status: 'PENDING',
        category: 'FUNDING',
        reference: 'PENDING1'
      });

      const stats = await transactionService.getTransactionStats('user123');

      // Should still be same totals since pending transactions are not counted
      expect(stats.totalCredit).toBe(10000);
      expect(stats.totalDebit).toBe(7000);
    });
  });

  describe('edge cases', () => {
    it('should handle large amounts', async () => {
      const transactionData = {
        userId: 'user123',
        type: 'CREDIT' as const,
        amount: 10000000, // 10 million
        fee: 100000, // 100k fee
        description: 'Large transaction',
        status: 'COMPLETED' as const,
        category: 'FUNDING',
        reference: 'LARGE_REF'
      };

      const transaction = await transactionService.createTransaction(transactionData);

      expect(transaction.amount).toBe(10000000);
      expect(transaction.fee).toBe(100000);
    });

    it('should handle zero amounts', async () => {
      const transactionData = {
        userId: 'user123',
        type: 'CREDIT' as const,
        amount: 0,
        fee: 0,
        description: 'Zero amount transaction',
        status: 'COMPLETED' as const,
        category: 'ADJUSTMENT',
        reference: 'ZERO_REF'
      };

      const transaction = await transactionService.createTransaction(transactionData);

      expect(transaction.amount).toBe(0);
      expect(transaction.fee).toBe(0);
    });

    it('should handle pagination with large datasets', async () => {
      // Create many transactions
      for (let i = 0; i < 25; i++) {
        await transactionService.createTransaction({
          userId: 'user123',
          type: i % 2 === 0 ? 'CREDIT' : 'DEBIT',
          amount: 1000 + i,
          fee: i,
          description: `Transaction ${i}`,
          status: 'COMPLETED',
          category: 'TEST',
          reference: `REF${i}`
        });
      }

      const result = await transactionService.getTransactions('user123', {
        page: 2,
        limit: 10
      });

      expect(result.transactions).toHaveLength(10);
      expect(result.pagination.total).toBe(25);
      expect(result.pagination.pages).toBe(3);
      expect(result.pagination.currentPage).toBe(2);
    });
  });
});
