import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { BillService } from '../../../services/bill.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const categoryId = event.pathParameters?.categoryId;
    if (!categoryId) {
      return errorResponse(400, 'MISSING_PARAMETER', 'Category ID is required');
    }
    
    const billService = new BillService();
    const providers = await billService.getBillProviders(categoryId);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, data: providers })
    };
  } catch (err: any) {
    logger.error('Get bill providers error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred while fetching bill providers');
  }
};
