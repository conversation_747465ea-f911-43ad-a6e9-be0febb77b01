import { APIGatewayProxyResult } from 'aws-lambda';

export function errorResponse(statusCode: number, code: string, message: string, details?: string): APIGatewayProxyResult {
  return {
    statusCode,
    body: JSON.stringify({
      success: false,
      error: {
        code,
        message,
        details,
        timestamp: new Date().toISOString(),
        requestId: '' // To be filled from context if available
      }
    })
  };
}
