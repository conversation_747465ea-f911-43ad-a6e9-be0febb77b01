{"info": {"_postman_id": "28f3836a-bcb6-4d00-a4ee-9e7287b8cd81", "name": "WALLET AS A SERVICE", "description": "This document exposes APIs for 9PSB Wallet As A Service.\n\nContact Support:\n Name: 9PSB\n Email: <EMAIL>", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "24808094"}, "item": [{"name": "WALLET", "item": [{"name": "authenticate", "event": [{"listen": "test", "script": {"exec": ["var response = JSON.parse(pm.response.text())\r", "if (response.message === 'successful') {\r", "    pm.collectionVariables.set('bearerToken', response.accessToken);\r", "}"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\",\n    \"clientId\": \"{{clientId}}\",\n    \"clientSecret\": \"{{clientSecret}}\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/authenticate"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"<string>\",\n  \"password\": \"<string>\",\n  \"clientId\": \"<string>\",\n  \"clientSecret\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/authenticate"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"message\": \"<string>\",\n  \"accessToken\": \"<string>\",\n  \"expiresIn\": \"<string>\",\n  \"refreshToken\": \"<string>\",\n  \"refreshExpiresIn\": \"<string>\",\n  \"jwt\": \"<string>\",\n  \"tokenType\": \"<string>\"\n}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"<string>\",\n  \"password\": \"<string>\",\n  \"clientId\": \"<string>\",\n  \"clientSecret\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/authenticate"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"<string>\",\n  \"password\": \"<string>\",\n  \"clientId\": \"<string>\",\n  \"clientSecret\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/authenticate"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"<string>\",\n  \"password\": \"<string>\",\n  \"clientId\": \"<string>\",\n  \"clientSecret\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/authenticate"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"<string>\",\n  \"password\": \"<string>\",\n  \"clientId\": \"<string>\",\n  \"clientSecret\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/authenticate"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "open Wallet", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"bvn\": \"***********\",\n    \"dateOfBirth\": \"29/04/1995\",\n    \"gender\": 1,\n    \"lastName\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"otherNames\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"phoneNo\": \"09034478562\",\n    \"transactionTrackingRef\": \"DGN2024112212010000\",\n    \"placeOfBirth\": \"Lagos\",\n    \"address\": \"7, <PERSON>kat<PERSON> Elegushi\",\n    \"nationalIdentityNo\": \"***********\",\n    \"ninUserId\": \"abcdf-1235\",\n    \"nextOfKinPhoneNo\": \"***********\",\n    \"nextOfKinName\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_wallet"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"***********\",\n  \"dateOfBirth\": \"85/44/0536\",\n  \"gender\": \"<integer>\",\n  \"lastName\": \"<string>\",\n  \"otherNames\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"transactionTrackingRef\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"address\": \"<string>\",\n  \"nationalIdentityNo\": \"<string>\",\n  \"nextOfKinPhoneNo\": \"<string>\",\n  \"nextOfKinName\": \"<string>\",\n  \"email\": \"\\\"\\\\q\\\\m\\\\j\\\\\\u0001\\\\n\\b\\\\w\\u0007\\\\jN\\\"@n93aqy.cygw.5.r2-duqop5hx.l41z0tvu\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_wallet"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"responseCode\": \"<string>\",\n    \"orderRef\": \"<string>\",\n    \"fullName\": \"<string>\",\n    \"creationMessage\": \"<string>\",\n    \"accountNumber\": \"<string>\",\n    \"ledgerBalance\": \"<string>\",\n    \"availableBalance\": \"<string>\",\n    \"customerID\": \"<string>\",\n    \"mfbcode\": \"<string>\",\n    \"financialDate\": \"<string>\",\n    \"withdrawableAmount\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"Ut1\": \"<string>\",\n    \"occaecat_a47\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"***********\",\n  \"dateOfBirth\": \"85/44/0536\",\n  \"gender\": \"<integer>\",\n  \"lastName\": \"<string>\",\n  \"otherNames\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"transactionTrackingRef\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"address\": \"<string>\",\n  \"nationalIdentityNo\": \"<string>\",\n  \"nextOfKinPhoneNo\": \"<string>\",\n  \"nextOfKinName\": \"<string>\",\n  \"email\": \"\\\"\\\\q\\\\m\\\\j\\\\\\u0001\\\\n\\b\\\\w\\u0007\\\\jN\\\"@n93aqy.cygw.5.r2-duqop5hx.l41z0tvu\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_wallet"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"***********\",\n  \"dateOfBirth\": \"85/44/0536\",\n  \"gender\": \"<integer>\",\n  \"lastName\": \"<string>\",\n  \"otherNames\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"transactionTrackingRef\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"address\": \"<string>\",\n  \"nationalIdentityNo\": \"<string>\",\n  \"nextOfKinPhoneNo\": \"<string>\",\n  \"nextOfKinName\": \"<string>\",\n  \"email\": \"\\\"\\\\q\\\\m\\\\j\\\\\\u0001\\\\n\\b\\\\w\\u0007\\\\jN\\\"@n93aqy.cygw.5.r2-duqop5hx.l41z0tvu\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_wallet"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"***********\",\n  \"dateOfBirth\": \"85/44/0536\",\n  \"gender\": \"<integer>\",\n  \"lastName\": \"<string>\",\n  \"otherNames\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"transactionTrackingRef\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"address\": \"<string>\",\n  \"nationalIdentityNo\": \"<string>\",\n  \"nextOfKinPhoneNo\": \"<string>\",\n  \"nextOfKinName\": \"<string>\",\n  \"email\": \"\\\"\\\\q\\\\m\\\\j\\\\\\u0001\\\\n\\b\\\\w\\u0007\\\\jN\\\"@n93aqy.cygw.5.r2-duqop5hx.l41z0tvu\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_wallet"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"***********\",\n  \"dateOfBirth\": \"85/44/0536\",\n  \"gender\": \"<integer>\",\n  \"lastName\": \"<string>\",\n  \"otherNames\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"transactionTrackingRef\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"address\": \"<string>\",\n  \"nationalIdentityNo\": \"<string>\",\n  \"nextOfKinPhoneNo\": \"<string>\",\n  \"nextOfKinName\": \"<string>\",\n  \"email\": \"\\\"\\\\q\\\\m\\\\j\\\\\\u0001\\\\n\\b\\\\w\\u0007\\\\jN\\\"@n93aqy.cygw.5.r2-duqop5hx.l41z0tvu\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_wallet"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "SUCCESS", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"***********\",\n  \"dateOfBirth\": \"29/04/1995\",\n  \"gender\": 0,\n  \"lastName\": \"<PERSON>\",\n  \"otherNames\": \"Deen\",\n  \"phoneNo\": \"***********\",\n  \"transactionTrackingRef\": \"******************\",\n  \"accountName\": \"<PERSON>\",\n  \"placeOfBirth\": \"Lagos\",\n  \"address\": \"7, Ikate Elegushi\",\n  \"nationalIdentityNo\": \"***********\",\n  \"nextOfKinPhoneNo\": \"***********\",\n  \"nextOfKinName\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_wallet"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Set-<PERSON><PERSON>", "value": "JSESSIONID=B08D9995EE5086A49042A1F05E0AAE48; Path=/waas; HttpOnly"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Sun, 25 Aug 2024 11:04:53 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"message\": \"Account Opening successful\",\n    \"data\": {\n        \"orderRef\": \"**********\",\n        \"customerID\": \"003289\",\n        \"fullName\": \"9PSBQA1/<PERSON>\",\n        \"accountNumber\": \"**********\"\n    }\n}"}]}, {"name": "debit Wallet", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"accountNo\": \"**********\",\n    \"narration\": \"TEST DEBIT\",\n    \"totalAmount\": 100.00,\n    \"transactionId\": \"******************\",\n    \"merchant\": {\n        \"isFee\": false,\n        \"merchantFeeAccount\": \"\",\n        \"merchantFeeAmount\": \"\"\n    }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/debit/transfer"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/debit/transfer"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/debit/transfer"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/debit/transfer"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/debit/transfer"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/debit/transfer"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "credit Wallet", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"TEST CREDIT\",\n  \"totalAmount\": 5000.00,\n  \"transactionId\": \"B2024072312540001\",\n  \"merchant\": {\n    \"isFee\": false,\n    \"merchantFeeAccount\": \"\",\n    \"merchantFeeAmount\": \"\"\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/credit/transfer"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/credit/transfer"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/credit/transfer"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/credit/transfer"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/credit/transfer"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\",\n  \"narration\": \"<string>\",\n  \"totalAmount\": \"<double>\",\n  \"transactionId\": \"<string>\",\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"**********\",\n    \"merchantFeeAmount\": \"***********.02\"\n  },\n  \"transactionType\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/credit/transfer"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "other Banks Enquiry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer\": {\n        \"account\": {\n            \"bank\": \"120001\",\n            \"number\": \"**********\"\n        }\n    }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/other_banks_enquiry"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"43203\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\"\n    }\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/other_banks_enquiry"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"43203\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\"\n    }\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/other_banks_enquiry"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"43203\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\"\n    }\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/other_banks_enquiry"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"43203\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\"\n    }\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/other_banks_enquiry"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"43203\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\"\n    }\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/other_banks_enquiry"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "other Bank Transfer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer\": {\n        \"account\": {\n            \"bank\": \"120001\",\n            \"name\": \"<PERSON>\",\n            \"number\": \"**********\",\n            \"senderaccountnumber\": \"**********\",\n            \"sendername\": \"<PERSON>\"\n        }\n    },\n    \"narration\": \"Test Transfer\",\n    \"order\": {\n        \"amount\": \"1000\",\n        \"country\": \"NGA\",\n        \"currency\": \"NGN\",\n        \"description\": \"TEST TRANSFER\"\n    },\n    \"transaction\": {\n        \"reference\": \"202412170543900000\"\n    },\n    \"merchant\": {\n        \"isFee\": true,\n        \"merchantFeeAccount\": \"**********\",\n        \"merchantFeeAmount\": \"16.13\"\n    }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_other_banks"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"8878\",\n      \"name\": \"<string>\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\",\n      \"sendername\": \"<string>\"\n    }\n  },\n  \"narration\": \"<string>\",\n  \"order\": {\n    \"amount\": \"280278\",\n    \"country\": \"<string>\",\n    \"currency\": \"<string>\",\n    \"description\": \"<string>\"\n  },\n  \"transaction\": {\n    \"reference\": \"<string>\",\n    \"sessionId\": \"<string>\"\n  },\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"\",\n    \"merchantFeeAmount\": \"*********\"\n  },\n  \"code\": \"<string>\",\n  \"message\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_other_banks"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"8878\",\n      \"name\": \"<string>\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\",\n      \"sendername\": \"<string>\"\n    }\n  },\n  \"narration\": \"<string>\",\n  \"order\": {\n    \"amount\": \"280278\",\n    \"country\": \"<string>\",\n    \"currency\": \"<string>\",\n    \"description\": \"<string>\"\n  },\n  \"transaction\": {\n    \"reference\": \"<string>\",\n    \"sessionId\": \"<string>\"\n  },\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"\",\n    \"merchantFeeAmount\": \"*********\"\n  },\n  \"code\": \"<string>\",\n  \"message\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_other_banks"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"8878\",\n      \"name\": \"<string>\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\",\n      \"sendername\": \"<string>\"\n    }\n  },\n  \"narration\": \"<string>\",\n  \"order\": {\n    \"amount\": \"280278\",\n    \"country\": \"<string>\",\n    \"currency\": \"<string>\",\n    \"description\": \"<string>\"\n  },\n  \"transaction\": {\n    \"reference\": \"<string>\",\n    \"sessionId\": \"<string>\"\n  },\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"\",\n    \"merchantFeeAmount\": \"*********\"\n  },\n  \"code\": \"<string>\",\n  \"message\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_other_banks"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"8878\",\n      \"name\": \"<string>\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\",\n      \"sendername\": \"<string>\"\n    }\n  },\n  \"narration\": \"<string>\",\n  \"order\": {\n    \"amount\": \"280278\",\n    \"country\": \"<string>\",\n    \"currency\": \"<string>\",\n    \"description\": \"<string>\"\n  },\n  \"transaction\": {\n    \"reference\": \"<string>\",\n    \"sessionId\": \"<string>\"\n  },\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"\",\n    \"merchantFeeAmount\": \"*********\"\n  },\n  \"code\": \"<string>\",\n  \"message\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_other_banks"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": {\n    \"account\": {\n      \"bank\": \"8878\",\n      \"name\": \"<string>\",\n      \"number\": \"**********\",\n      \"senderaccountnumber\": \"**********\",\n      \"sendername\": \"<string>\"\n    }\n  },\n  \"narration\": \"<string>\",\n  \"order\": {\n    \"amount\": \"280278\",\n    \"country\": \"<string>\",\n    \"currency\": \"<string>\",\n    \"description\": \"<string>\"\n  },\n  \"transaction\": {\n    \"reference\": \"<string>\",\n    \"sessionId\": \"<string>\"\n  },\n  \"merchant\": {\n    \"isFee\": \"<boolean>\",\n    \"merchantFeeAccount\": \"\",\n    \"merchantFeeAmount\": \"*********\"\n  },\n  \"code\": \"<string>\",\n  \"message\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_other_banks"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "wallet Requery", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"*****************\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_requery"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"<string>\",\n  \"amount\": \"<double>\",\n  \"transactionType\": \"<string>\",\n  \"transactionDate\": \"<string>\",\n  \"accountNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_requery"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"<string>\",\n  \"amount\": \"<double>\",\n  \"transactionType\": \"<string>\",\n  \"transactionDate\": \"<string>\",\n  \"accountNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_requery"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"<string>\",\n  \"amount\": \"<double>\",\n  \"transactionType\": \"<string>\",\n  \"transactionDate\": \"<string>\",\n  \"accountNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_requery"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"<string>\",\n  \"amount\": \"<double>\",\n  \"transactionType\": \"<string>\",\n  \"transactionDate\": \"<string>\",\n  \"accountNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_requery"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"<string>\",\n  \"amount\": \"<double>\",\n  \"transactionType\": \"<string>\",\n  \"transactionDate\": \"<string>\",\n  \"accountNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_requery"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "SUCCESS- CREDIT/DEBIT", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"******************\",\n  \"amount\": 1000,\n  \"transactionType\": \"CREDIT_WALLET\",\n  \"transactionDate\": \"\",\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_requery"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Sun, 25 Aug 2024 11:50:09 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"message\": \"Approved by Financial Institution\",\n    \"data\": {\n        \"isSuccessful\": true,\n        \"responseMessage\": \"Approved by Financial Institution\",\n        \"responseCode\": \"00\",\n        \"reference\": \"******************\",\n        \"status\": \"SUCCESS\"\n    }\n}"}, {"name": "SUCCESS - TRANSACTION STATUS 1", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"*****************\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_requery"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Sun, 25 Aug 2024 11:55:39 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"message\": \"Approved by Financial Institution\",\n    \"data\": {\n        \"isSuccessful\": true,\n        \"responseMessage\": \"Approved by Financial Institution\",\n        \"responseCode\": \"00\",\n        \"reference\": \"*****************\",\n        \"status\": \"SUCCESS\"\n    }\n}"}]}, {"name": "wallet Enquiry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_enquiry"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_enquiry"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"SUCCESS\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"name\": \"<string>\",\n    \"number\": \"<string>\",\n    \"responseCode\": \"<string>\",\n    \"requestStatus\": \"<boolean>\",\n    \"email\": \"<string>\",\n    \"status\": \"<string>\",\n    \"phoneNo\": \"<string>\",\n    \"lastName\": \"<string>\",\n    \"tier\": \"<string>\",\n    \"productCode\": \"<string>\",\n    \"isSuccessful\": \"<boolean>\",\n    \"firstName\": \"<string>\",\n    \"maximumBalance\": \"<double>\",\n    \"responseMessage\": \"<string>\",\n    \"phoneNuber\": \"<string>\",\n    \"ledgerBalance\": \"<double>\",\n    \"nuban\": \"<string>\",\n    \"bvn\": \"<string>\",\n    \"responseDescription\": \"<string>\",\n    \"availableBalance\": \"<double>\",\n    \"maximumDeposit\": \"<string>\",\n    \"freezeStatus\": \"<string>\",\n    \"lienStatus\": \"<string>\",\n    \"pndstatus\": \"<string>\",\n    \"responseStatus\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"mollit8\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_enquiry"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_enquiry"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_enquiry"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_enquiry"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "wallet Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_status"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_status"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"SUCCESS\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"walletStatus\": \"<string>\",\n    \"responseCode\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"labore49\": \"<string>\",\n    \"qui_e\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_status"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_status"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_status"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNo\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_status"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "get Wallet", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"bvn\": \"***********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_wallet"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"82345548178\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_wallet"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"82345548178\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_wallet"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"82345548178\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_wallet"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"82345548178\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_wallet"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"bvn\": \"82345548178\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_wallet"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "wallet Transactions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"accountNumber\": \"**********\",\n    \"fromDate\": \"2024-11-19\",\n    \"toDate\": \"2024-11-21\",\n    \"numberOfItems\": \"100\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_transactions"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"fromDate\": \"1903-12-01\",\n  \"toDate\": \"2003-91-99\",\n  \"numberOfItems\": \"998\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_transactions"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"fromDate\": \"1903-12-01\",\n  \"toDate\": \"2003-91-99\",\n  \"numberOfItems\": \"998\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_transactions"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"fromDate\": \"1903-12-01\",\n  \"toDate\": \"2003-91-99\",\n  \"numberOfItems\": \"998\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_transactions"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"fromDate\": \"1903-12-01\",\n  \"toDate\": \"2003-91-99\",\n  \"numberOfItems\": \"998\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_transactions"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"fromDate\": \"1903-12-01\",\n  \"toDate\": \"2003-91-99\",\n  \"numberOfItems\": \"998\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_transactions"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "wallet Upgrade File Upload", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{<PERSON>T<PERSON>}}"}}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "accountName", "value": "DGNRAVE/Doe John", "description": "(Required) ", "type": "text"}, {"key": "accountNumber", "value": "**********", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "***********", "description": "(Required) ", "type": "text"}, {"key": "city", "value": "<PERSON><PERSON><PERSON>", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<EMAIL>", "description": "(Required) ", "type": "text"}, {"key": "houseNumber", "value": "7", "description": "(Required) ", "type": "text"}, {"key": "idIssueDate", "value": "2021-01-21", "description": "(Required) ", "type": "text"}, {"key": "idNumber", "value": "***********", "description": "(Required) ", "type": "text"}, {"key": "idType", "value": "1", "description": "(Required) ", "type": "text"}, {"key": "localGovernment", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "(Required) ", "type": "text"}, {"key": "pep", "value": "NO", "description": "(Required) ", "type": "text"}, {"key": "phoneNumber", "value": "***********", "description": "(Required) ", "type": "text"}, {"key": "state", "value": "Lagos", "description": "(Required) ", "type": "text"}, {"key": "streetName", "value": "<PERSON><PERSON><PERSON><PERSON>", "description": "(Required) ", "type": "text"}, {"key": "tier", "value": "2", "description": "(Required) ", "type": "text"}, {"key": "idExpiryDate", "value": "", "type": "text", "disabled": true}, {"key": "nearestLandmark", "value": "Elegushi Palace", "type": "text"}, {"key": "placeOfBirth", "value": "Apapa", "type": "text"}, {"key": "utilityBill", "type": "file", "src": "/C:/Users/<USER>/OneDrive - 9 PSB/Pictures/Screenshots/Screenshot 2023-05-11 132442.png"}, {"key": "proofOfAddressVerification", "type": "file", "src": []}, {"key": "userPhoto", "type": "file", "src": "/C:/Users/<USER>/OneDrive - 9 PSB/Pictures/Screenshots/Screenshot 2023-05-12 095741.png"}, {"key": "customerSignature", "type": "file", "src": "/C:/Users/<USER>/OneDrive - 9 PSB/Pictures/Screenshots/Screenshot 2023-05-11 132746.png"}, {"key": "idCardFront", "type": "file", "src": "/C:/Users/<USER>/OneDrive - 9 PSB/Pictures/Screenshots/Screenshot 2023-05-12 104640.png"}, {"key": "idCardBack", "type": "file", "src": []}, {"key": "nin", "value": "***********", "type": "text"}]}, "url": "{{baseUrl}}/wallet_upgrade_file_upload"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "city", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "houseNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idIssueDate", "value": "7614-11-97", "description": "(Required) ", "type": "text"}, {"key": "idNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idType", "value": "4", "description": "(Required) ", "type": "text"}, {"key": "localGovernment", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "pep", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "state", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "streetName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "tier", "value": "1", "description": "(Required) ", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "idExpiryDate", "value": "8738-01-99", "type": "text"}, {"key": "approvalStatus", "value": "<string>", "type": "text"}, {"key": "nearestLandmark", "value": "<string>", "type": "text"}, {"key": "placeOfBirth", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "userPhoto", "value": "<string>", "type": "text"}, {"key": "customerSignature", "value": "<string>", "type": "text"}, {"key": "idCardFront", "value": "<string>", "type": "text"}, {"key": "idCardBack", "value": "<string>", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/wallet_upgrade_file_upload"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "city", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "houseNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idIssueDate", "value": "7614-11-97", "description": "(Required) ", "type": "text"}, {"key": "idNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idType", "value": "4", "description": "(Required) ", "type": "text"}, {"key": "localGovernment", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "pep", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "state", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "streetName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "tier", "value": "1", "description": "(Required) ", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "idExpiryDate", "value": "8738-01-99", "type": "text"}, {"key": "approvalStatus", "value": "<string>", "type": "text"}, {"key": "nearestLandmark", "value": "<string>", "type": "text"}, {"key": "placeOfBirth", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "userPhoto", "value": "<string>", "type": "text"}, {"key": "customerSignature", "value": "<string>", "type": "text"}, {"key": "idCardFront", "value": "<string>", "type": "text"}, {"key": "idCardBack", "value": "<string>", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/wallet_upgrade_file_upload"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "city", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "houseNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idIssueDate", "value": "7614-11-97", "description": "(Required) ", "type": "text"}, {"key": "idNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idType", "value": "4", "description": "(Required) ", "type": "text"}, {"key": "localGovernment", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "pep", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "state", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "streetName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "tier", "value": "1", "description": "(Required) ", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "idExpiryDate", "value": "8738-01-99", "type": "text"}, {"key": "approvalStatus", "value": "<string>", "type": "text"}, {"key": "nearestLandmark", "value": "<string>", "type": "text"}, {"key": "placeOfBirth", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "userPhoto", "value": "<string>", "type": "text"}, {"key": "customerSignature", "value": "<string>", "type": "text"}, {"key": "idCardFront", "value": "<string>", "type": "text"}, {"key": "idCardBack", "value": "<string>", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/wallet_upgrade_file_upload"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "city", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "houseNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idIssueDate", "value": "7614-11-97", "description": "(Required) ", "type": "text"}, {"key": "idNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idType", "value": "4", "description": "(Required) ", "type": "text"}, {"key": "localGovernment", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "pep", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "state", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "streetName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "tier", "value": "1", "description": "(Required) ", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "idExpiryDate", "value": "8738-01-99", "type": "text"}, {"key": "approvalStatus", "value": "<string>", "type": "text"}, {"key": "nearestLandmark", "value": "<string>", "type": "text"}, {"key": "placeOfBirth", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "userPhoto", "value": "<string>", "type": "text"}, {"key": "customerSignature", "value": "<string>", "type": "text"}, {"key": "idCardFront", "value": "<string>", "type": "text"}, {"key": "idCardBack", "value": "<string>", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/wallet_upgrade_file_upload"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "city", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "houseNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idIssueDate", "value": "7614-11-97", "description": "(Required) ", "type": "text"}, {"key": "idNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "idType", "value": "4", "description": "(Required) ", "type": "text"}, {"key": "localGovernment", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "pep", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "state", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "streetName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "tier", "value": "1", "description": "(Required) ", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "idExpiryDate", "value": "8738-01-99", "type": "text"}, {"key": "approvalStatus", "value": "<string>", "type": "text"}, {"key": "nearestLandmark", "value": "<string>", "type": "text"}, {"key": "placeOfBirth", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "userPhoto", "value": "<string>", "type": "text"}, {"key": "customerSignature", "value": "<string>", "type": "text"}, {"key": "idCardFront", "value": "<string>", "type": "text"}, {"key": "idCardBack", "value": "<string>", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/wallet_upgrade_file_upload"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "SUCCESS", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "accountName", "value": "9PSBQA1/<PERSON>", "description": "(Required) ", "type": "text"}, {"key": "accountNumber", "value": "**********", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "***********", "description": "(Required) ", "type": "text"}, {"key": "city", "value": "<PERSON><PERSON><PERSON>", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<EMAIL>", "description": "(Required) ", "type": "text"}, {"key": "houseNumber", "value": "7", "description": "(Required) ", "type": "text"}, {"key": "idIssueDate", "value": "2021-01-21", "description": "(Required) ", "type": "text"}, {"key": "idNumber", "value": "***********", "description": "(Required) ", "type": "text"}, {"key": "idType", "value": "1", "description": "(Required) ", "type": "text"}, {"key": "localGovernment", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "(Required) ", "type": "text"}, {"key": "pep", "value": "NO", "description": "(Required) ", "type": "text"}, {"key": "phoneNumber", "value": "***********", "description": "(Required) ", "type": "text"}, {"key": "state", "value": "Lagos", "description": "(Required) ", "type": "text"}, {"key": "streetName", "value": "<PERSON><PERSON><PERSON><PERSON>", "description": "(Required) ", "type": "text"}, {"key": "tier", "value": "2", "description": "(Required) ", "type": "text"}, {"key": "idExpiryDate", "value": "", "type": "text", "disabled": true}, {"key": "nearestLandmark", "value": "Elegushi Palace", "type": "text"}, {"key": "placeOfBirth", "value": "Apapa", "type": "text"}, {"key": "utilityBill", "type": "file", "src": "/C:/Users/<USER>/OneDrive - 9 PSB/Pictures/Screenshots/Screenshot 2023-05-11 132442.png"}, {"key": "proofOfAddressVerification", "type": "file", "src": []}, {"key": "userPhoto", "type": "file", "src": "/C:/Users/<USER>/OneDrive - 9 PSB/Pictures/Screenshots/Screenshot 2023-05-12 095741.png"}, {"key": "customerSignature", "type": "file", "src": "/C:/Users/<USER>/OneDrive - 9 PSB/Pictures/Screenshots/Screenshot 2023-05-11 132746.png"}, {"key": "idCardFront", "type": "file", "src": "/C:/Users/<USER>/OneDrive - 9 PSB/Pictures/Screenshots/Screenshot 2023-05-12 104640.png"}, {"key": "idCardBack", "type": "file", "src": []}]}, "url": "{{baseUrl}}/api/v1/wallet_upgrade_file_upload"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Set-<PERSON><PERSON>", "value": "JSESSIONID=2B5183B784F1DAB875A5E5303DF55B5D; Path=/waas; HttpOnly"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Sun, 25 Aug 2024 11:41:46 GMT"}, {"key": "Keep-Alive", "value": "timeout=20"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"message\": \"Wallet Upgrade Request Successful\",\n    \"data\": {\n        \"message\": \"Request submitted successfully\",\n        \"status\": \"Successful\"\n    }\n}"}]}, {"name": "wallet Upgrade", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"nin\": \"\",\n  \"accountName\": \"<string>\",\n  \"channelType\": \"<string>\",\n  \"phoneNumber\": \"<string>\",\n  \"tier\": \"<string>\",\n  \"email\": \"<string>\",\n  \"userPhoto\": \"<string>\",\n  \"idType\": \"<string>\",\n  \"idNumber\": \"<string>\",\n  \"idIssueDate\": \"<dateTime>\",\n  \"idExpiryDate\": \"<dateTime>\",\n  \"idCardFront\": \"<string>\",\n  \"idCardBack\": \"<string>\",\n  \"houseNumber\": \"<string>\",\n  \"streetName\": \"<string>\",\n  \"state\": \"<string>\",\n  \"city\": \"<string>\",\n  \"localGovernment\": \"<string>\",\n  \"approvalStatus\": \"<string>\",\n  \"pep\": \"<string>\",\n  \"customerSignature\": \"<string>\",\n  \"utilityBill\": \"<string>\",\n  \"nearestLandmark\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"proofOfAddressVerification\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_upgrade"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"channelType\": \"<string>\",\n  \"phoneNumber\": \"<string>\",\n  \"tier\": \"<string>\",\n  \"email\": \"<string>\",\n  \"userPhoto\": \"<string>\",\n  \"idType\": \"<string>\",\n  \"idNumber\": \"<string>\",\n  \"idIssueDate\": \"<dateTime>\",\n  \"idExpiryDate\": \"<dateTime>\",\n  \"idCardFront\": \"<string>\",\n  \"idCardBack\": \"<string>\",\n  \"houseNumber\": \"<string>\",\n  \"streetName\": \"<string>\",\n  \"state\": \"<string>\",\n  \"city\": \"<string>\",\n  \"localGovernment\": \"<string>\",\n  \"approvalStatus\": \"<string>\",\n  \"pep\": \"<string>\",\n  \"customerSignature\": \"<string>\",\n  \"utilityBill\": \"<string>\",\n  \"nearestLandmark\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"proofOfAddressVerification\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_upgrade"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"channelType\": \"<string>\",\n  \"phoneNumber\": \"<string>\",\n  \"tier\": \"<string>\",\n  \"email\": \"<string>\",\n  \"userPhoto\": \"<string>\",\n  \"idType\": \"<string>\",\n  \"idNumber\": \"<string>\",\n  \"idIssueDate\": \"<dateTime>\",\n  \"idExpiryDate\": \"<dateTime>\",\n  \"idCardFront\": \"<string>\",\n  \"idCardBack\": \"<string>\",\n  \"houseNumber\": \"<string>\",\n  \"streetName\": \"<string>\",\n  \"state\": \"<string>\",\n  \"city\": \"<string>\",\n  \"localGovernment\": \"<string>\",\n  \"approvalStatus\": \"<string>\",\n  \"pep\": \"<string>\",\n  \"customerSignature\": \"<string>\",\n  \"utilityBill\": \"<string>\",\n  \"nearestLandmark\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"proofOfAddressVerification\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_upgrade"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"channelType\": \"<string>\",\n  \"phoneNumber\": \"<string>\",\n  \"tier\": \"<string>\",\n  \"email\": \"<string>\",\n  \"userPhoto\": \"<string>\",\n  \"idType\": \"<string>\",\n  \"idNumber\": \"<string>\",\n  \"idIssueDate\": \"<dateTime>\",\n  \"idExpiryDate\": \"<dateTime>\",\n  \"idCardFront\": \"<string>\",\n  \"idCardBack\": \"<string>\",\n  \"houseNumber\": \"<string>\",\n  \"streetName\": \"<string>\",\n  \"state\": \"<string>\",\n  \"city\": \"<string>\",\n  \"localGovernment\": \"<string>\",\n  \"approvalStatus\": \"<string>\",\n  \"pep\": \"<string>\",\n  \"customerSignature\": \"<string>\",\n  \"utilityBill\": \"<string>\",\n  \"nearestLandmark\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"proofOfAddressVerification\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_upgrade"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"channelType\": \"<string>\",\n  \"phoneNumber\": \"<string>\",\n  \"tier\": \"<string>\",\n  \"email\": \"<string>\",\n  \"userPhoto\": \"<string>\",\n  \"idType\": \"<string>\",\n  \"idNumber\": \"<string>\",\n  \"idIssueDate\": \"<dateTime>\",\n  \"idExpiryDate\": \"<dateTime>\",\n  \"idCardFront\": \"<string>\",\n  \"idCardBack\": \"<string>\",\n  \"houseNumber\": \"<string>\",\n  \"streetName\": \"<string>\",\n  \"state\": \"<string>\",\n  \"city\": \"<string>\",\n  \"localGovernment\": \"<string>\",\n  \"approvalStatus\": \"<string>\",\n  \"pep\": \"<string>\",\n  \"customerSignature\": \"<string>\",\n  \"utilityBill\": \"<string>\",\n  \"nearestLandmark\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"proofOfAddressVerification\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_upgrade"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"accountName\": \"<string>\",\n  \"channelType\": \"<string>\",\n  \"phoneNumber\": \"<string>\",\n  \"tier\": \"<string>\",\n  \"email\": \"<string>\",\n  \"userPhoto\": \"<string>\",\n  \"idType\": \"<string>\",\n  \"idNumber\": \"<string>\",\n  \"idIssueDate\": \"<dateTime>\",\n  \"idExpiryDate\": \"<dateTime>\",\n  \"idCardFront\": \"<string>\",\n  \"idCardBack\": \"<string>\",\n  \"houseNumber\": \"<string>\",\n  \"streetName\": \"<string>\",\n  \"state\": \"<string>\",\n  \"city\": \"<string>\",\n  \"localGovernment\": \"<string>\",\n  \"approvalStatus\": \"<string>\",\n  \"pep\": \"<string>\",\n  \"customerSignature\": \"<string>\",\n  \"utilityBill\": \"<string>\",\n  \"nearestLandmark\": \"<string>\",\n  \"placeOfBirth\": \"<string>\",\n  \"proofOfAddressVerification\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/wallet_upgrade"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "wallet Upgrade Tier2To3Multipart", "request": {"auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ0ek1WWmtzOG1zcG01TmNHazNFdW1BVjZWYTFRQTVpTlYwcHVfU3hZQldBIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PBbOtmNbOPOQI-D9G2Cyl1ROwzttoMxo2ErOyGpRBEgyLcXZYj3gDDryO2Dk5Xp_HR_XLeZF2yZrD8NGxi6ZA1yWmt1ru9KJpqqslWb06t8T3XIv1pvkWDZihnfXYc9lDl3xac3I-8Iro6PzdHnFV5N_Jo1j414oW4ZG0pRBC5CgJ-r99U-hGuBwRwWEvUI0ARYgwNhZuybkv246PRwvgNnu_ihr7syJbKaPDkz2FHar1cCNwJoK0bBTeX4XhpLHF2rcnsRmvTySqe1S-_91qaCFPsXnASAbqS322MtmeVNaT6klxy3RXOvGW9Tzt_hQ9oicwJo3VgBHdGDjBn1UJA"}}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "proofOfAddressVerification", "description": "(Required) ", "type": "file", "src": []}, {"key": "nin", "value": "<String>", "description": "(Required)", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-multipart"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-multipart"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-multipart"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-multipart"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-multipart"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "accountNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "bvn", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-multipart"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "wallet Upgrade Tier3Base64", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"nin\": \"<string>\",\n  \"proofOfAddressVerification\": \"{{base64ImageString}}\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-base64"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"proofOfAddressVerification\": \"xggTrSgoBYrupgecYK+WBb8IDEqLEM3hRqg=\",\n  \"channel\": \"<string>\",\n  \"accountName\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-base64"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"proofOfAddressVerification\": \"xggTrSgoBYrupgecYK+WBb8IDEqLEM3hRqg=\",\n  \"channel\": \"<string>\",\n  \"accountName\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-base64"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"proofOfAddressVerification\": \"xggTrSgoBYrupgecYK+WBb8IDEqLEM3hRqg=\",\n  \"channel\": \"<string>\",\n  \"accountName\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-base64"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"proofOfAddressVerification\": \"xggTrSgoBYrupgecYK+WBb8IDEqLEM3hRqg=\",\n  \"channel\": \"<string>\",\n  \"accountName\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-base64"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"bvn\": \"<string>\",\n  \"proofOfAddressVerification\": \"xggTrSgoBYrupgecYK+WBb8IDEqLEM3hRqg=\",\n  \"channel\": \"<string>\",\n  \"accountName\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/walletUpgrade-tier3-base64"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "upgrade Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"**********\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/upgrade_status"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/upgrade_status"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/upgrade_status"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/upgrade_status"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/upgrade_status"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/upgrade_status"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "open Corporate Account File Upload", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "address", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessCommencementDate", "value": "2090-10-30", "description": "(Required) ", "type": "text"}, {"key": "businessName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessType", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonFirstName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonLastName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "dateIncorporated", "value": "1953-12-21", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "industrialSector", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "natureOfBusiness", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "postalAddress", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "registrationNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "taxIDNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "companyRegDate", "value": "2037-09-30", "type": "text"}, {"key": "businessTypeName", "value": "<string>", "type": "text"}, {"key": "webAddress", "value": "<string>", "type": "text"}, {"key": "cacCertificate", "value": "<string>", "type": "text"}, {"key": "scumlCertificate", "value": "<string>", "type": "text"}, {"key": "regulatoryLicenseFintech", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "accountSignatories", "value": "[object Object],[object Object]", "type": "text"}, {"key": "directors", "value": "[object Object]", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/open_corporate_account_file_upload"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "address", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessCommencementDate", "value": "2090-10-30", "description": "(Required) ", "type": "text"}, {"key": "businessName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessType", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonFirstName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonLastName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "dateIncorporated", "value": "1953-12-21", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "industrialSector", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "natureOfBusiness", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "postalAddress", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "registrationNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "taxIDNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "companyRegDate", "value": "2037-09-30", "type": "text"}, {"key": "businessTypeName", "value": "<string>", "type": "text"}, {"key": "webAddress", "value": "<string>", "type": "text"}, {"key": "cacCertificate", "value": "<string>", "type": "text"}, {"key": "scumlCertificate", "value": "<string>", "type": "text"}, {"key": "regulatoryLicenseFintech", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "accountSignatories", "value": "[object Object],[object Object]", "type": "text"}, {"key": "directors", "value": "[object Object]", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/open_corporate_account_file_upload"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "address", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessCommencementDate", "value": "2090-10-30", "description": "(Required) ", "type": "text"}, {"key": "businessName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessType", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonFirstName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonLastName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "dateIncorporated", "value": "1953-12-21", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "industrialSector", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "natureOfBusiness", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "postalAddress", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "registrationNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "taxIDNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "companyRegDate", "value": "2037-09-30", "type": "text"}, {"key": "businessTypeName", "value": "<string>", "type": "text"}, {"key": "webAddress", "value": "<string>", "type": "text"}, {"key": "cacCertificate", "value": "<string>", "type": "text"}, {"key": "scumlCertificate", "value": "<string>", "type": "text"}, {"key": "regulatoryLicenseFintech", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "accountSignatories", "value": "[object Object],[object Object]", "type": "text"}, {"key": "directors", "value": "[object Object]", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/open_corporate_account_file_upload"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "address", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessCommencementDate", "value": "2090-10-30", "description": "(Required) ", "type": "text"}, {"key": "businessName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessType", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonFirstName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonLastName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "dateIncorporated", "value": "1953-12-21", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "industrialSector", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "natureOfBusiness", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "postalAddress", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "registrationNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "taxIDNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "companyRegDate", "value": "2037-09-30", "type": "text"}, {"key": "businessTypeName", "value": "<string>", "type": "text"}, {"key": "webAddress", "value": "<string>", "type": "text"}, {"key": "cacCertificate", "value": "<string>", "type": "text"}, {"key": "scumlCertificate", "value": "<string>", "type": "text"}, {"key": "regulatoryLicenseFintech", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "accountSignatories", "value": "[object Object],[object Object]", "type": "text"}, {"key": "directors", "value": "[object Object]", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/open_corporate_account_file_upload"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "address", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessCommencementDate", "value": "2090-10-30", "description": "(Required) ", "type": "text"}, {"key": "businessName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessType", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonFirstName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonLastName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "dateIncorporated", "value": "1953-12-21", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "industrialSector", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "natureOfBusiness", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "postalAddress", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "registrationNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "taxIDNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "companyRegDate", "value": "2037-09-30", "type": "text"}, {"key": "businessTypeName", "value": "<string>", "type": "text"}, {"key": "webAddress", "value": "<string>", "type": "text"}, {"key": "cacCertificate", "value": "<string>", "type": "text"}, {"key": "scumlCertificate", "value": "<string>", "type": "text"}, {"key": "regulatoryLicenseFintech", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "accountSignatories", "value": "[object Object],[object Object]", "type": "text"}, {"key": "directors", "value": "[object Object]", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/open_corporate_account_file_upload"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "formdata", "formdata": [{"key": "address", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessCommencementDate", "value": "2090-10-30", "description": "(Required) ", "type": "text"}, {"key": "businessName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "businessType", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonFirstName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "contactPersonLastName", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "dateIncorporated", "value": "1953-12-21", "description": "(Required) ", "type": "text"}, {"key": "email", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "industrialSector", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "natureOfBusiness", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "phoneNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "postalAddress", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "registrationNumber", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "taxIDNo", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "companyRegDate", "value": "2037-09-30", "type": "text"}, {"key": "businessTypeName", "value": "<string>", "type": "text"}, {"key": "webAddress", "value": "<string>", "type": "text"}, {"key": "cacCertificate", "value": "<string>", "type": "text"}, {"key": "scumlCertificate", "value": "<string>", "type": "text"}, {"key": "regulatoryLicenseFintech", "value": "<string>", "type": "text"}, {"key": "utilityBill", "value": "<string>", "type": "text"}, {"key": "proofOfAddressVerification", "value": "<string>", "type": "text"}, {"key": "channelType", "value": "<string>", "type": "text"}, {"key": "accountSignatories", "value": "[object Object],[object Object]", "type": "text"}, {"key": "directors", "value": "[object Object]", "type": "text"}]}, "url": "{{baseUrl}}/api/v1/open_corporate_account_file_upload"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "open Corporate Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"address\": \"<string>\",\n  \"businessCommencementDate\": \"1922-10-30\",\n  \"businessName\": \"<string>\",\n  \"businessType\": \"<string>\",\n  \"cacCertificate\": \"QLj+VsUIYl5D\",\n  \"contactPersonFirstName\": \"<string>\",\n  \"contactPersonLastName\": \"<string>\",\n  \"dateIncorporated\": \"1917-11-05\",\n  \"email\": \"<string>\",\n  \"industrialSector\": \"<string>\",\n  \"natureOfBusiness\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"postalAddress\": \"<string>\",\n  \"registrationNumber\": \"<string>\",\n  \"taxIDNo\": \"<string>\",\n  \"utilityBill\": \"wNg9\",\n  \"companyRegDate\": \"1982-05-12\",\n  \"businessTypeName\": \"<string>\",\n  \"webAddress\": \"<string>\",\n  \"scumlCertificate\": \"uX1rsK==\",\n  \"regulatoryLicenseFintech\": \"8MT+GO2f3UfYAzknVSd0ero4ZFUuYhs8yVrU1Eh=\",\n  \"proofOfAddressVerification\": \"NGMMy7yrBnZmbRGv\",\n  \"channelType\": \"<string>\",\n  \"accountSignatories\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"2067-09-07\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"WbdtcYzD0KHssMXEOscwnTKZ\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"Vzr=\",\n      \"utilityBill\": \"s98q5ZXjTO0lMM9rjdla\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"NeCpZRAwS7Eohwrp7zmyCHoKp7sDzldbBc+kGKdP3V==\",\n      \"idCardBack\": \"lsUrXP+pGeHSqsZD2NZ0FhYNH2C5\",\n      \"residentPermit\": \"<string>\"\n    },\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1976-10-26\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"lrv=\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"72mzZ3fL\",\n      \"utilityBill\": \"9406zod/lBrmtmfINPu+\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"BeHnGmcjMEblGCIFnBlMuZ008cWpv/Y/689Dst7pa1F=\",\n      \"idCardBack\": \"Brm7z11pggMLzDAsueRhD7oI\",\n      \"residentPermit\": \"<string>\"\n    }\n  ],\n  \"directors\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1960-04-09\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"zwrkiUjhcfvAFMgUIgn3\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"YsOY44vbpWimpUwx8gryDiPkLoS7\",\n      \"utilityBill\": \"pUHgLtNpxitfXgm0+DJyma6arH==\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"v6y7MD8ylI1hfN9G3k26vmHi6qbl2m6p\",\n      \"idCardBack\": \"3pxsE8OuxY7nc3cGdA/2NVPp\",\n      \"residentPermit\": \"<string>\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_corporate_account"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"address\": \"<string>\",\n  \"businessCommencementDate\": \"1922-10-30\",\n  \"businessName\": \"<string>\",\n  \"businessType\": \"<string>\",\n  \"cacCertificate\": \"QLj+VsUIYl5D\",\n  \"contactPersonFirstName\": \"<string>\",\n  \"contactPersonLastName\": \"<string>\",\n  \"dateIncorporated\": \"1917-11-05\",\n  \"email\": \"<string>\",\n  \"industrialSector\": \"<string>\",\n  \"natureOfBusiness\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"postalAddress\": \"<string>\",\n  \"registrationNumber\": \"<string>\",\n  \"taxIDNo\": \"<string>\",\n  \"utilityBill\": \"wNg9\",\n  \"companyRegDate\": \"1982-05-12\",\n  \"businessTypeName\": \"<string>\",\n  \"webAddress\": \"<string>\",\n  \"scumlCertificate\": \"uX1rsK==\",\n  \"regulatoryLicenseFintech\": \"8MT+GO2f3UfYAzknVSd0ero4ZFUuYhs8yVrU1Eh=\",\n  \"proofOfAddressVerification\": \"NGMMy7yrBnZmbRGv\",\n  \"channelType\": \"<string>\",\n  \"accountSignatories\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"2067-09-07\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"WbdtcYzD0KHssMXEOscwnTKZ\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"Vzr=\",\n      \"utilityBill\": \"s98q5ZXjTO0lMM9rjdla\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"NeCpZRAwS7Eohwrp7zmyCHoKp7sDzldbBc+kGKdP3V==\",\n      \"idCardBack\": \"lsUrXP+pGeHSqsZD2NZ0FhYNH2C5\",\n      \"residentPermit\": \"<string>\"\n    },\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1976-10-26\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"lrv=\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"72mzZ3fL\",\n      \"utilityBill\": \"9406zod/lBrmtmfINPu+\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"BeHnGmcjMEblGCIFnBlMuZ008cWpv/Y/689Dst7pa1F=\",\n      \"idCardBack\": \"Brm7z11pggMLzDAsueRhD7oI\",\n      \"residentPermit\": \"<string>\"\n    }\n  ],\n  \"directors\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1960-04-09\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"zwrkiUjhcfvAFMgUIgn3\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"YsOY44vbpWimpUwx8gryDiPkLoS7\",\n      \"utilityBill\": \"pUHgLtNpxitfXgm0+DJyma6arH==\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"v6y7MD8ylI1hfN9G3k26vmHi6qbl2m6p\",\n      \"idCardBack\": \"3pxsE8OuxY7nc3cGdA/2NVPp\",\n      \"residentPermit\": \"<string>\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_corporate_account"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"address\": \"<string>\",\n  \"businessCommencementDate\": \"1922-10-30\",\n  \"businessName\": \"<string>\",\n  \"businessType\": \"<string>\",\n  \"cacCertificate\": \"QLj+VsUIYl5D\",\n  \"contactPersonFirstName\": \"<string>\",\n  \"contactPersonLastName\": \"<string>\",\n  \"dateIncorporated\": \"1917-11-05\",\n  \"email\": \"<string>\",\n  \"industrialSector\": \"<string>\",\n  \"natureOfBusiness\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"postalAddress\": \"<string>\",\n  \"registrationNumber\": \"<string>\",\n  \"taxIDNo\": \"<string>\",\n  \"utilityBill\": \"wNg9\",\n  \"companyRegDate\": \"1982-05-12\",\n  \"businessTypeName\": \"<string>\",\n  \"webAddress\": \"<string>\",\n  \"scumlCertificate\": \"uX1rsK==\",\n  \"regulatoryLicenseFintech\": \"8MT+GO2f3UfYAzknVSd0ero4ZFUuYhs8yVrU1Eh=\",\n  \"proofOfAddressVerification\": \"NGMMy7yrBnZmbRGv\",\n  \"channelType\": \"<string>\",\n  \"accountSignatories\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"2067-09-07\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"WbdtcYzD0KHssMXEOscwnTKZ\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"Vzr=\",\n      \"utilityBill\": \"s98q5ZXjTO0lMM9rjdla\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"NeCpZRAwS7Eohwrp7zmyCHoKp7sDzldbBc+kGKdP3V==\",\n      \"idCardBack\": \"lsUrXP+pGeHSqsZD2NZ0FhYNH2C5\",\n      \"residentPermit\": \"<string>\"\n    },\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1976-10-26\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"lrv=\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"72mzZ3fL\",\n      \"utilityBill\": \"9406zod/lBrmtmfINPu+\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"BeHnGmcjMEblGCIFnBlMuZ008cWpv/Y/689Dst7pa1F=\",\n      \"idCardBack\": \"Brm7z11pggMLzDAsueRhD7oI\",\n      \"residentPermit\": \"<string>\"\n    }\n  ],\n  \"directors\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1960-04-09\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"zwrkiUjhcfvAFMgUIgn3\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"YsOY44vbpWimpUwx8gryDiPkLoS7\",\n      \"utilityBill\": \"pUHgLtNpxitfXgm0+DJyma6arH==\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"v6y7MD8ylI1hfN9G3k26vmHi6qbl2m6p\",\n      \"idCardBack\": \"3pxsE8OuxY7nc3cGdA/2NVPp\",\n      \"residentPermit\": \"<string>\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_corporate_account"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"address\": \"<string>\",\n  \"businessCommencementDate\": \"1922-10-30\",\n  \"businessName\": \"<string>\",\n  \"businessType\": \"<string>\",\n  \"cacCertificate\": \"QLj+VsUIYl5D\",\n  \"contactPersonFirstName\": \"<string>\",\n  \"contactPersonLastName\": \"<string>\",\n  \"dateIncorporated\": \"1917-11-05\",\n  \"email\": \"<string>\",\n  \"industrialSector\": \"<string>\",\n  \"natureOfBusiness\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"postalAddress\": \"<string>\",\n  \"registrationNumber\": \"<string>\",\n  \"taxIDNo\": \"<string>\",\n  \"utilityBill\": \"wNg9\",\n  \"companyRegDate\": \"1982-05-12\",\n  \"businessTypeName\": \"<string>\",\n  \"webAddress\": \"<string>\",\n  \"scumlCertificate\": \"uX1rsK==\",\n  \"regulatoryLicenseFintech\": \"8MT+GO2f3UfYAzknVSd0ero4ZFUuYhs8yVrU1Eh=\",\n  \"proofOfAddressVerification\": \"NGMMy7yrBnZmbRGv\",\n  \"channelType\": \"<string>\",\n  \"accountSignatories\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"2067-09-07\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"WbdtcYzD0KHssMXEOscwnTKZ\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"Vzr=\",\n      \"utilityBill\": \"s98q5ZXjTO0lMM9rjdla\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"NeCpZRAwS7Eohwrp7zmyCHoKp7sDzldbBc+kGKdP3V==\",\n      \"idCardBack\": \"lsUrXP+pGeHSqsZD2NZ0FhYNH2C5\",\n      \"residentPermit\": \"<string>\"\n    },\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1976-10-26\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"lrv=\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"72mzZ3fL\",\n      \"utilityBill\": \"9406zod/lBrmtmfINPu+\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"BeHnGmcjMEblGCIFnBlMuZ008cWpv/Y/689Dst7pa1F=\",\n      \"idCardBack\": \"Brm7z11pggMLzDAsueRhD7oI\",\n      \"residentPermit\": \"<string>\"\n    }\n  ],\n  \"directors\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1960-04-09\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"zwrkiUjhcfvAFMgUIgn3\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"YsOY44vbpWimpUwx8gryDiPkLoS7\",\n      \"utilityBill\": \"pUHgLtNpxitfXgm0+DJyma6arH==\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"v6y7MD8ylI1hfN9G3k26vmHi6qbl2m6p\",\n      \"idCardBack\": \"3pxsE8OuxY7nc3cGdA/2NVPp\",\n      \"residentPermit\": \"<string>\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_corporate_account"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"address\": \"<string>\",\n  \"businessCommencementDate\": \"1922-10-30\",\n  \"businessName\": \"<string>\",\n  \"businessType\": \"<string>\",\n  \"cacCertificate\": \"QLj+VsUIYl5D\",\n  \"contactPersonFirstName\": \"<string>\",\n  \"contactPersonLastName\": \"<string>\",\n  \"dateIncorporated\": \"1917-11-05\",\n  \"email\": \"<string>\",\n  \"industrialSector\": \"<string>\",\n  \"natureOfBusiness\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"postalAddress\": \"<string>\",\n  \"registrationNumber\": \"<string>\",\n  \"taxIDNo\": \"<string>\",\n  \"utilityBill\": \"wNg9\",\n  \"companyRegDate\": \"1982-05-12\",\n  \"businessTypeName\": \"<string>\",\n  \"webAddress\": \"<string>\",\n  \"scumlCertificate\": \"uX1rsK==\",\n  \"regulatoryLicenseFintech\": \"8MT+GO2f3UfYAzknVSd0ero4ZFUuYhs8yVrU1Eh=\",\n  \"proofOfAddressVerification\": \"NGMMy7yrBnZmbRGv\",\n  \"channelType\": \"<string>\",\n  \"accountSignatories\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"2067-09-07\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"WbdtcYzD0KHssMXEOscwnTKZ\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"Vzr=\",\n      \"utilityBill\": \"s98q5ZXjTO0lMM9rjdla\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"NeCpZRAwS7Eohwrp7zmyCHoKp7sDzldbBc+kGKdP3V==\",\n      \"idCardBack\": \"lsUrXP+pGeHSqsZD2NZ0FhYNH2C5\",\n      \"residentPermit\": \"<string>\"\n    },\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1976-10-26\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"lrv=\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"72mzZ3fL\",\n      \"utilityBill\": \"9406zod/lBrmtmfINPu+\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"BeHnGmcjMEblGCIFnBlMuZ008cWpv/Y/689Dst7pa1F=\",\n      \"idCardBack\": \"Brm7z11pggMLzDAsueRhD7oI\",\n      \"residentPermit\": \"<string>\"\n    }\n  ],\n  \"directors\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1960-04-09\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"zwrkiUjhcfvAFMgUIgn3\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"YsOY44vbpWimpUwx8gryDiPkLoS7\",\n      \"utilityBill\": \"pUHgLtNpxitfXgm0+DJyma6arH==\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"v6y7MD8ylI1hfN9G3k26vmHi6qbl2m6p\",\n      \"idCardBack\": \"3pxsE8OuxY7nc3cGdA/2NVPp\",\n      \"residentPermit\": \"<string>\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_corporate_account"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"address\": \"<string>\",\n  \"businessCommencementDate\": \"1922-10-30\",\n  \"businessName\": \"<string>\",\n  \"businessType\": \"<string>\",\n  \"cacCertificate\": \"QLj+VsUIYl5D\",\n  \"contactPersonFirstName\": \"<string>\",\n  \"contactPersonLastName\": \"<string>\",\n  \"dateIncorporated\": \"1917-11-05\",\n  \"email\": \"<string>\",\n  \"industrialSector\": \"<string>\",\n  \"natureOfBusiness\": \"<string>\",\n  \"phoneNo\": \"***********\",\n  \"postalAddress\": \"<string>\",\n  \"registrationNumber\": \"<string>\",\n  \"taxIDNo\": \"<string>\",\n  \"utilityBill\": \"wNg9\",\n  \"companyRegDate\": \"1982-05-12\",\n  \"businessTypeName\": \"<string>\",\n  \"webAddress\": \"<string>\",\n  \"scumlCertificate\": \"uX1rsK==\",\n  \"regulatoryLicenseFintech\": \"8MT+GO2f3UfYAzknVSd0ero4ZFUuYhs8yVrU1Eh=\",\n  \"proofOfAddressVerification\": \"NGMMy7yrBnZmbRGv\",\n  \"channelType\": \"<string>\",\n  \"accountSignatories\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"2067-09-07\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"WbdtcYzD0KHssMXEOscwnTKZ\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"Vzr=\",\n      \"utilityBill\": \"s98q5ZXjTO0lMM9rjdla\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"NeCpZRAwS7Eohwrp7zmyCHoKp7sDzldbBc+kGKdP3V==\",\n      \"idCardBack\": \"lsUrXP+pGeHSqsZD2NZ0FhYNH2C5\",\n      \"residentPermit\": \"<string>\"\n    },\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1976-10-26\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"lrv=\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"72mzZ3fL\",\n      \"utilityBill\": \"9406zod/lBrmtmfINPu+\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"BeHnGmcjMEblGCIFnBlMuZ008cWpv/Y/689Dst7pa1F=\",\n      \"idCardBack\": \"Brm7z11pggMLzDAsueRhD7oI\",\n      \"residentPermit\": \"<string>\"\n    }\n  ],\n  \"directors\": [\n    {\n      \"accountOfficerCode\": \"<string>\",\n      \"address\": \"<string>\",\n      \"bankVerificationNumber\": \"<string>\",\n      \"customerPassportInBytes\": \"<string>\",\n      \"customerType\": \"<string>\",\n      \"dateOfBirth\": \"1960-04-09\",\n      \"email\": \"<string>\",\n      \"firstName\": \"<string>\",\n      \"gender\": \"male\",\n      \"idCardFront\": \"zwrkiUjhcfvAFMgUIgn3\",\n      \"lastName\": \"<string>\",\n      \"nationality\": \"<string>\",\n      \"nextOfKinName\": \"<string>\",\n      \"nextOfKinPhoneNumber\": \"<string>\",\n      \"pep\": \"<string>\",\n      \"phoneNo\": \"<string>\",\n      \"signature\": \"YsOY44vbpWimpUwx8gryDiPkLoS7\",\n      \"utilityBill\": \"pUHgLtNpxitfXgm0+DJyma6arH==\",\n      \"otherNames\": \"<string>\",\n      \"city\": \"<string>\",\n      \"placeOfBirth\": \"<string>\",\n      \"nationalIdentityNo\": \"<string>\",\n      \"branchID\": \"<integer>\",\n      \"status\": \"<string>\",\n      \"otherNationalityType\": \"<string>\",\n      \"proofOfAddressVerification\": \"v6y7MD8ylI1hfN9G3k26vmHi6qbl2m6p\",\n      \"idCardBack\": \"3pxsE8OuxY7nc3cGdA/2NVPp\",\n      \"residentPermit\": \"<string>\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/open_corporate_account"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "fetch Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"sessionID\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/notification_requery"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"sessionID\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/notification_requery"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"sessionID\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/notification_requery"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"sessionID\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/notification_requery"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"sessionID\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/notification_requery"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"sessionID\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/notification_requery"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "get Request Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_request_status"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_request_status"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_request_status"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_request_status"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_request_status"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_request_status"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "Get Bank List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_banks"}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"bankList\": [\n      {\n        \"bankName\": \"<string>\",\n        \"bankCode\": \"<string>\",\n        \"nibssBankCode\": \"<string>\"\n      },\n      {\n        \"bankName\": \"<string>\",\n        \"bankCode\": \"<string>\",\n        \"nibssBankCode\": \"<string>\"\n      }\n    ],\n    \"responseCode\": \"<string>\",\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"mollit_80\": \"<string>\",\n    \"quis8\": \"<string>\",\n    \"in_b_d\": \"<string>\",\n    \"aliquab\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Accepted", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "get Bank List", "request": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_banks"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"bankList\": [\n      {\n        \"bankName\": \"<string>\",\n        \"bankCode\": \"<string>\",\n        \"nibssBankCode\": \"<string>\"\n      },\n      {\n        \"bankName\": \"<string>\",\n        \"bankCode\": \"<string>\",\n        \"nibssBankCode\": \"<string>\"\n      }\n    ],\n    \"responseCode\": \"<string>\",\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"mollit_80\": \"<string>\",\n    \"quis8\": \"<string>\",\n    \"in_b_d\": \"<string>\",\n    \"aliquab\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "url": "{{baseUrl}}/api/v1/get_banks"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "Get Corporate Account Number", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"taxIDNo\": \"\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_account_number"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"taxIDNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_account_number"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"taxIDNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_account_number"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"taxIDNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_account_number"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"taxIDNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_account_number"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"taxIDNo\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/get_account_number"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}, {"name": "change Wallet Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"accountNumber\": \"**********\",\n    \"accountStatus\": \"ACTIVE\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/change_wallet_status"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"accountStatus\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/change_wallet_status"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"newWalletStatus\": \"<string>\",\n    \"responseCode\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"commodoca\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Accepted", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"accountStatus\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/change_wallet_status"}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"accountStatus\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/change_wallet_status"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Forbidden", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"accountStatus\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/change_wallet_status"}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"FAILED\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {},\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"eiusmode73\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}, {"name": "Internal Server Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}, {"key": "Authorization", "value": "Bearer <token>", "description": "Added as a part of security scheme: bearer"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"<string>\",\n  \"accountStatus\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/change_wallet_status"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "body": "{\n  \"status\": \"PENDING\",\n  \"responseCode\": \"<string>\",\n  \"message\": \"<string>\",\n  \"data\": {\n    \"message\": \"<string>\"\n  },\n  \"error\": \"<string>\",\n  \"fieldErrors\": {\n    \"culpa7e1\": \"<string>\",\n    \"do_7a\": \"<string>\"\n  },\n  \"jwt\": \"<string>\"\n}"}]}]}, {"name": "QR", "item": [{"name": "CREATE MERCHANT", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Indaboski Agency\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"address\": \"Plot 56, Onits<PERSON>\",\r\n    \"tin\": \"**********\",\r\n    \"accountNo\": \"**********\",\r\n    \"merchantBearsFee\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/api/v1/qr/merchant"}, "response": [{"name": "CREATE MERCHANT SUCCESS", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Indaboski Agency\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"address\": \"Plot 56, Onitsha\",\r\n    \"tin\": \"**********\",\r\n    \"accountNo\": \"**********\",\r\n    \"merchantBearsFee\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{localUrl}}/api/v1/qr/merchant"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Thu, 05 Sep 2024 15:12:29 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"responseCode\": \"00\",\n    \"message\": \"Merchant Creation Successful\",\n    \"data\": {\n        \"name\": \"Indaboski Agency\",\n        \"merchantNo\": \"M0000033800\",\n        \"tin\": \"**********\",\n        \"accountNo\": \"**********\",\n        \"accountName\": \"<PERSON>\",\n        \"merchantBearsFee\": false\n    }\n}"}]}, {"name": "CREATE FIXED QR CODE", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"reference\": \"****************\",\r\n    \"merchantNo\": \"M0000033800\",\r\n    \"subMerchantName\": \"Indaboski Aba-2\",\r\n    \"phoneNo\": \"***********\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"isFixedAmount\": false,\r\n    \"fixedAmount\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/api/v1/qr/fixed"}, "response": [{"name": "CREATE FIXED QR SUCCESS", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"reference\": \"****************\",\r\n    \"merchantNo\": \"M0000033800\",\r\n    \"subMerchantName\": \"Indaboski Aba-2\",\r\n    \"phoneNo\": \"***********\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"isFixedAmount\": false,\r\n    \"fixedAmount\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{localUrl}}/api/v1/qr/fixed"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Thu, 05 Sep 2024 15:21:35 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"responseCode\": \"00\",\n    \"message\": \"Fixed QRCode generated\",\n    \"data\": {\n        \"codeUrl\": \"0002010102111531**999166**999166****M000003380026370018NG.COM.NIBSSPLC.QR0111S00001041335204000053035665802NG5916Indaboski Agency6007Nigeria63041F92\",\n        \"reference\": \"****************\",\n        \"merchantNo\": \"M0000033800\",\n        \"subMerchantNo\": \"S0000104133\",\n        \"name\": \"Indaboski Aba-2\",\n        \"email\": \"<EMAIL>\",\n        \"phoneNo\": \"***********\",\n        \"type\": \"FIXED\",\n        \"createdAt\": \"2024-09-05 16:21:18\"\n    }\n}"}]}, {"name": "CREATE DYNAMIC QR CODE", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"reference\": \"2024090516230000\",\r\n    \"amount\": 1000,\r\n    \"subMerchantNo\": \"S0000104133\",\r\n    \"customerName\": \"Leo Panthera\",\r\n    \"customerEmail\": \"<EMAIL>\",\r\n    \"customerPhoneNo\": \"08134746418\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/api/v1/qr/dynamic"}, "response": [{"name": "CREATE DYNAMIC QR SUCCESS", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"reference\": \"2024090516230000\",\r\n    \"amount\": 1000,\r\n    \"subMerchantNo\": \"S0000104133\",\r\n    \"customerName\": \"Leo Panthera\",\r\n    \"customerEmail\": \"<EMAIL>\",\r\n    \"customerPhoneNo\": \"08134746418\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{localUrl}}/api/v1/qr/dynamic"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Thu, 05 Sep 2024 15:29:54 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"responseCode\": \"00\",\n    \"message\": \"Dynamic QRCode generated\",\n    \"data\": {\n        \"codeUrl\": \"0002010102121531**999166**999166****M000003380026710018NG.COM.NIBSSPLC.QR0111S00001041330230999166240905162936528343369569520400005303566540410005802NG5916Indaboski Agency6007Nigeria6304DD29\",\n        \"reference\": \"****************\",\n        \"orderNo\": \"202409051629362390610420358587\",\n        \"orderSn\": \"999166240905162936528343369569\",\n        \"merchantNo\": \"M0000033800\",\n        \"subMerchantNo\": \"S0000104133\",\n        \"name\": \"<PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"phoneNo\": \"08134746418\",\n        \"type\": \"DYNAMI<PERSON>\",\n        \"fixedAmount\": 1000,\n        \"createdAt\": \"2024-09-05 16:29:36\"\n    }\n}"}]}, {"name": "GET QR CODE PROPERTIES", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/qr/properties?codeUrl=0002010102111531**999166**999166****M000003380026370018NG.COM.NIBSSPLC.QR0111S00001041335204000053035665802NG5916Indaboski Agency6007Nigeria63041F92", "host": ["{{baseUrl}}"], "path": ["api", "v1", "qr", "properties"], "query": [{"key": "codeUrl", "value": "0002010102111531**999166**999166****M000003380026370018NG.COM.NIBSSPLC.QR0111S00001041335204000053035665802NG5916Indaboski Agency6007Nigeria63041F92"}]}}, "response": [{"name": "DYNAMIC QR CODE PROPERTIES SUCCESS", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{localUrl}}/api/v1/qr/properties?codeUrl=0002010102121531**999166**999166****M000003380026710018NG.COM.NIBSSPLC.QR0111S00001041330230999166240905162936528343369569520400005303566540410005802NG5916Indaboski Agency6007Nigeria6304DD29", "host": ["{{localUrl}}"], "path": ["api", "v1", "qr", "properties"], "query": [{"key": "codeUrl", "value": "0002010102121531**999166**999166****M000003380026710018NG.COM.NIBSSPLC.QR0111S00001041330230999166240905162936528343369569520400005303566540410005802NG5916Indaboski Agency6007Nigeria6304DD29"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Thu, 05 Sep 2024 15:31:21 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"responseCode\": \"00\",\n    \"message\": \"QR Code properties retrieved\",\n    \"data\": {\n        \"type\": \"DYNAMIC\",\n        \"amount\": 1000,\n        \"orderSn\": \"999166240905162936528343369569\",\n        \"merchantName\": \"Indaboski Agency\",\n        \"merchantNo\": \"M0000033800\",\n        \"subMerchantNo\": \"S0000104133\",\n        \"countryCode\": \"NG\",\n        \"countryName\": \"Nigeria\",\n        \"institutionNumber\": \"999166\",\n        \"forwardingNumber\": \"999166\",\n        \"crcCode\": \"DD29\"\n    }\n}"}, {"name": "FIXED QR CODE PROPERTIES SUCCESS", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{localUrl}}/api/v1/qr/properties?codeUrl=0002010102111531**999166**999166****M000003380026370018NG.COM.NIBSSPLC.QR0111S00001041335204000053035665802NG5916Indaboski Agency6007Nigeria63041F92", "host": ["{{localUrl}}"], "path": ["api", "v1", "qr", "properties"], "query": [{"key": "codeUrl", "value": "0002010102111531**999166**999166****M000003380026370018NG.COM.NIBSSPLC.QR0111S00001041335204000053035665802NG5916Indaboski Agency6007Nigeria63041F92"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Thu, 05 Sep 2024 15:33:07 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"responseCode\": \"00\",\n    \"message\": \"QR Code properties retrieved\",\n    \"data\": {\n        \"type\": \"FIXED\",\n        \"amount\": null,\n        \"orderSn\": null,\n        \"merchantName\": \"Indaboski Agency\",\n        \"merchantNo\": \"M0000033800\",\n        \"subMerchantNo\": \"S0000104133\",\n        \"countryCode\": \"NG\",\n        \"countryName\": \"Nigeria\",\n        \"institutionNumber\": \"999166\",\n        \"forwardingNumber\": \"999166\",\n        \"crcCode\": \"1F92\"\n    }\n}"}]}, {"name": "CREATE MERCHANTS IN BULK", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "[\r\n    {\r\n        \"name\": \"Abidosheka Corp\",\r\n        \"email\": \"<EMAIL>\",\r\n        \"address\": \"Plot 77, Powers Road\",\r\n        \"tin\": \"**********\",\r\n        \"accountNo\": \"**********\",\r\n        \"merchantBearsFee\": false\r\n    },\r\n     {\r\n        \"name\": \"<PERSON><PERSON><PERSON>\",\r\n        \"email\": \"<EMAIL>\",\r\n        \"address\": \"Plot 57, Powers Road\",\r\n        \"tin\": \"**********\",\r\n        \"accountNo\": \"**********\",\r\n        \"merchantBearsFee\": false\r\n    }\r\n]", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/api/v1/qr/bulk-merchants"}, "response": [{"name": "POSSIBLE SUCCESS", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "[\r\n    {\r\n        \"name\": \"Abidosheka Corp\",\r\n        \"email\": \"<EMAIL>\",\r\n        \"address\": \"Plot 77, Powers Road\",\r\n        \"tin\": \"**********\",\r\n        \"accountNo\": \"**********\",\r\n        \"merchantBearsFee\": false\r\n    },\r\n     {\r\n        \"name\": \"<PERSON><PERSON><PERSON>\",\r\n        \"email\": \"<EMAIL>\",\r\n        \"address\": \"Plot 57, Powers Road\",\r\n        \"tin\": \"**********\",\r\n        \"accountNo\": \"**********\",\r\n        \"merchantBearsFee\": false\r\n    }\r\n]", "options": {"raw": {"language": "json"}}}, "url": "{{localUrl}}/api/v1/qr/bulk-merchants"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Thu, 05 Sep 2024 15:38:28 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"responseCode\": \"00\",\n    \"message\": \"0 of 2 merchants created\",\n    \"data\": {\n        \"successful\": [],\n        \"failed\": [\n            {\n                \"name\": null,\n                \"tin\": null,\n                \"reason\": \"name invalid\"\n            },\n            {\n                \"name\": null,\n                \"tin\": null,\n                \"reason\": \"name invalid\"\n            }\n        ]\n    }\n}"}]}, {"name": "CREATE BULK FIXED QR CODES", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"merchantNo\": \"M0000033800\",\r\n    \"reference\": \"2024090516420000\",\r\n    \"batchList\": [\r\n        {\r\n            \"name\": \"Ganduga\",\r\n            \"email\": \"<EMAIL>\",\r\n            \"phoneNo\": \"08097563457\",\r\n            \"isFixedAmount\": false,\r\n            \"fixedAmount\": 1000\r\n        },\r\n        {\r\n            \"name\": \"<PERSON>and<PERSON>\",\r\n            \"email\": \"<EMAIL>\",\r\n            \"phoneNo\": \"08097563456\",\r\n            \"isFixedAmount\": false,\r\n            \"fixedAmount\": 1000\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/api/v1/qr/bulk-fixed"}, "response": [{"name": "CREATE BATCH FIXED QR CODES SUCCESS", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"merchantNo\": \"M0000033800\",\r\n    \"reference\": \"2024090516420000\",\r\n    \"batchList\": [\r\n        {\r\n            \"name\": \"Ganduga\",\r\n            \"email\": \"<EMAIL>\",\r\n            \"phoneNo\": \"08097563457\",\r\n            \"isFixedAmount\": false,\r\n            \"fixedAmount\": 1000\r\n        },\r\n        {\r\n            \"name\": \"<PERSON>and<PERSON>\",\r\n            \"email\": \"<EMAIL>\",\r\n            \"phoneNo\": \"08097563456\",\r\n            \"isFixedAmount\": false,\r\n            \"fixedAmount\": 1000\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{localUrl}}/api/v1/qr/bulk-fixed"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Thu, 05 Sep 2024 15:49:15 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"status\": \"SUCCESS\",\n    \"responseCode\": \"00\",\n    \"message\": \"Fixed QR Codes generated\",\n    \"data\": [\n        {\n            \"codeUrl\": \"0002010102111531**999166**999166****M000003380026370018NG.COM.NIBSSPLC.QR0111S00001041345204000053035665802NG5916Indaboski Agency6007Nigeria6304F04A\",\n            \"reference\": \"2024090516420000\",\n            \"orderNo\": null,\n            \"orderSn\": null,\n            \"merchantNo\": \"M0000033800\",\n            \"subMerchantNo\": \"S0000104134\",\n            \"name\": \"<PERSON><PERSON><PERSON>\",\n            \"email\": null,\n            \"phoneNo\": null,\n            \"type\": \"FIXED\",\n            \"isFixedAmount\": null,\n            \"fixedAmount\": 1000,\n            \"createdAt\": \"2024-09-05 16:48:58\"\n        },\n        {\n            \"codeUrl\": \"0002010102111531**999166**999166****M000003380026370018NG.COM.NIBSSPLC.QR0111S00001041355204000053035665802NG5916Indaboski Agency6007Nigeria63046FAF\",\n            \"reference\": \"2024090516420000-1\",\n            \"orderNo\": null,\n            \"orderSn\": null,\n            \"merchantNo\": \"M0000033800\",\n            \"subMerchantNo\": \"S0000104135\",\n            \"name\": \"Gandusa\",\n            \"email\": null,\n            \"phoneNo\": null,\n            \"type\": \"FIXED\",\n            \"isFixedAmount\": null,\n            \"fixedAmount\": 1000,\n            \"createdAt\": \"2024-09-05 16:48:58\"\n        }\n    ]\n}"}]}]}], "auth": {"type": "bearer", "bearer": {"token": "{{<PERSON>T<PERSON>}}"}}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://**************:9090/waas"}, {"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}, {"key": "clientId", "value": "", "type": "string"}, {"key": "clientSecret", "value": "", "type": "string"}]}