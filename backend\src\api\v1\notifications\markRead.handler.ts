import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { NotificationService } from '../../../services/notification.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';
import { AuthMiddleware } from '../../../common/middlewares/auth.middleware';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const authMiddleware = new AuthMiddleware();
    const user = authMiddleware.authenticate(event);
    
    const notificationId = event.pathParameters?.id;
    if (!notificationId) {
      return errorResponse(400, 'MISSING_PARAMETER', 'Notification ID is required');
    }
    
    const notificationService = new NotificationService();
    await notificationService.markAsRead(user.userId, notificationId);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, message: 'Notification marked as read' })
    };
  } catch (err: any) {
    logger.error('Mark notification read error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred while marking notification as read');
  }
};
