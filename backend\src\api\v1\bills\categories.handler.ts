import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { BillService } from '../../../services/bill.service';
import { errorResponse } from '../../../common/errors/errorResponse';
import { logger } from '../../../common/logging/logger';

export const main = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const billService = new BillService();
    const categories = await billService.getBillCategories();
    
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'OPTIONS,POST,GET,PUT,DELETE'
      },
      body: JSON.stringify({ 
        success: true, 
        data: { 
          categories: categories || [] 
        } 
      })
    };
  } catch (err: any) {
    logger.error('Get bill categories error', { error: err.message });
    return errorResponse(500, 'INTERNAL_ERROR', 'An error occurred while fetching bill categories');
  }
};
