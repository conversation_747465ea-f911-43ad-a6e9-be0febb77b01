import { APIGatewayProxyEvent } from 'aws-lambda';
import { main } from '../../src/api/v1/transfers/transfer.handler';
import { TransferService } from '../../src/services/transfer.service';
import { AuthMiddleware } from '../../src/common/middlewares/auth.middleware';
import { TokenPayload } from '../../src/common/security/jwt.service';

// Mock dependencies
jest.mock('../../src/services/transfer.service');
jest.mock('../../src/common/middlewares/auth.middleware');

const mockTransferService = TransferService as jest.MockedClass<typeof TransferService>;
const mockAuthMiddleware = AuthMiddleware as jest.MockedClass<typeof AuthMiddleware>;

describe('Transfer Handler', () => {
  let mockEvent: APIGatewayProxyEvent;
  let mockTransferServiceInstance: jest.Mocked<TransferService>;
  let mockAuthMiddlewareInstance: jest.Mocked<AuthMiddleware>;

  beforeEach(() => {
    jest.clearAllMocks();

    mockEvent = {
      headers: {
        Authorization: 'Bearer valid-token'
      },
      body: JSON.stringify({
        recipientType: 'ZAPWALLET',
        recipientId: 'recipient123',
        amount: 10000,
        pin: '1234',
        description: 'Transfer to friend'
      }),
      pathParameters: null,
      queryStringParameters: null,
      httpMethod: 'POST',
      path: '/transfers',
      resource: '/transfers',
      requestContext: {} as any,
      isBase64Encoded: false,
      multiValueHeaders: {},
      multiValueQueryStringParameters: null,
      stageVariables: null
    };

    // Mock TransferService instance
    mockTransferServiceInstance = {
      initiateTransfer: jest.fn(),
      processTransfer: jest.fn(),
      validateRecipient: jest.fn(),
      getTransferHistory: jest.fn(),
      getTransferById: jest.fn(),
      cancelTransfer: jest.fn(),
    } as unknown as jest.Mocked<TransferService>;

    mockTransferService.mockImplementation(() => mockTransferServiceInstance);

    // Mock AuthMiddleware instance
    mockAuthMiddlewareInstance = {
      authenticate: jest.fn(),
    } as unknown as jest.Mocked<AuthMiddleware>;

    mockAuthMiddleware.mockImplementation(() => mockAuthMiddlewareInstance);
  });

  describe('successful transfer', () => {
    it('should initiate transfer successfully', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };      const mockTransferResult = {
        transferId: 'transfer123',
        reference: 'TRF-123456',
        status: 'PENDING',
        amount: 10000,
        fee: 0,
        estimatedCompletion: 'Instant',
        message: 'Transfer initiated successfully'
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.initiateTransfer.mockResolvedValue(mockTransferResult);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockTransferResult
      });
      expect(mockAuthMiddlewareInstance.authenticate).toHaveBeenCalledWith(mockEvent);
      expect(mockTransferServiceInstance.initiateTransfer).toHaveBeenCalledWith(
        'user123',
        {
          recipientType: 'ZAPWALLET',
          recipientId: 'recipient123',
          amount: 10000,
          pin: '1234',
          description: 'Transfer to friend'
        }
      );
    });

    it('should handle bank transfer', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };      const bankTransferData = {
        recipientType: 'BANK',
        recipientId: '**********',
        amount: 50000,
        pin: '1234',
        description: 'Salary payment'
      };const mockTransferResult = {
        transferId: 'transfer456',
        reference: 'TRF-456789',
        status: 'PENDING',
        amount: 50000,
        fee: 50,
        estimatedCompletion: '1-3 business days',
        message: 'Transfer initiated successfully'
      };

      mockEvent.body = JSON.stringify(bankTransferData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.initiateTransfer.mockResolvedValue(mockTransferResult);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockTransferResult
      });
      expect(mockTransferServiceInstance.initiateTransfer).toHaveBeenCalledWith(
        'user123',
        bankTransferData
      );
    });
  });

  describe('validation errors', () => {
    it('should return 400 for missing recipientType', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const invalidData = {
        recipientId: 'recipient123',
        amount: 10000,
        pin: '1234'
      };      mockEvent.body = JSON.stringify(invalidData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('"recipientType" is required');
    });

    it('should return 400 for invalid amount', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };      const invalidData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'recipient123',
        amount: 5,
        pin: '1234'
      };

      mockEvent.body = JSON.stringify(invalidData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('"amount" must be greater than or equal to 10');
    });

    it('should return 400 for invalid pin length', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };
      const invalidData = {
        recipientType: 'ZAPWALLET',
        recipientId: 'recipient123',
        amount: 10000,
        pin: '123'
      };

      mockEvent.body = JSON.stringify(invalidData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('"pin" length must be 4 characters long');
    });
  });

  describe('authentication errors', () => {
    it('should return 500 for missing authorization', async () => {
      mockEvent.headers = {};

      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Missing or invalid authorization header');
      });

      const result = await main(mockEvent);      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred during transfer');
    });

    it('should return 500 for invalid token', async () => {
      mockAuthMiddlewareInstance.authenticate.mockImplementation(() => {
        throw new Error('Invalid or expired token');
      });

      const result = await main(mockEvent);      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred during transfer');
    });
  });

  describe('service errors', () => {
    it('should handle transfer service errors', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.initiateTransfer.mockRejectedValue(new Error('Insufficient balance'));

      const result = await main(mockEvent);      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred during transfer');
    });

    it('should handle recipient validation errors', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.initiateTransfer.mockRejectedValue(new Error('Invalid recipient'));

      const result = await main(mockEvent);      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred during transfer');
    });
  });

  describe('edge cases', () => {
    it('should handle invalid JSON in request body', async () => {
      mockEvent.body = 'invalid json';

      const result = await main(mockEvent);      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('INTERNAL_ERROR');
      expect(body.error.message).toBe('An error occurred during transfer');
    });

    it('should handle empty request body', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockEvent.body = '';
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('"recipientType" is required');
    });

    it('should handle null request body', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };

      mockEvent.body = null;
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);

      const result = await main(mockEvent);      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
      expect(body.error.message).toBe('"recipientType" is required');
    });

    it('should handle large transfer amounts', async () => {
      const mockUser: TokenPayload = { 
        userId: 'user123', 
        email: '<EMAIL>', 
        role: 'USER' 
      };      const largeTransferData = {
        recipientType: 'BANK',
        recipientId: '**********',
        amount: 1000000,
        pin: '1234',
        description: 'Large transfer'
      };const mockTransferResult = {
        transferId: 'transfer789',
        reference: 'TRF-789012',
        status: 'PENDING',
        amount: 1000000,
        fee: 50,
        estimatedCompletion: '1-3 business days',
        message: 'Transfer initiated successfully'
      };

      mockEvent.body = JSON.stringify(largeTransferData);
      mockAuthMiddlewareInstance.authenticate.mockReturnValue(mockUser);
      mockTransferServiceInstance.initiateTransfer.mockResolvedValue(mockTransferResult);

      const result = await main(mockEvent);

      expect(result.statusCode).toBe(200);
      expect(JSON.parse(result.body)).toEqual({
        success: true,
        data: mockTransferResult
      });
    });
  });
});
