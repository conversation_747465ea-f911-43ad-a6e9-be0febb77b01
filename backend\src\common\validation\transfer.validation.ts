import Joi from 'joi';

export const recipientValidationSchema = Joi.object({
  type: Joi.string().valid('ZAPWALLET', 'BANK').required(),
  identifier: Joi.string().required(),
  bankCode: Joi.when('type', {
    is: 'BANK',
    then: Joi.string().required(),
    otherwise: Joi.string().optional()
  })
});

export const transferSchema = Joi.object({
  recipientType: Joi.string().valid('ZAPWALLET', 'BANK').required(),
  recipientId: Joi.string().required(),
  amount: Joi.number().min(10).required(),
  description: Joi.string().max(255).optional(),
  pin: Joi.string().length(4).required()
});

export function validateRecipient(data: any) {
  return recipientValidationSchema.validate(data, { abortEarly: true });
}

export function validateTransfer(data: any) {
  return transferSchema.validate(data, { abortEarly: true });
}
